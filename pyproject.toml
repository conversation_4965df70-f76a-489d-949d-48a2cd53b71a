[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "jiradashboard"
version = "0.1.0"
description = "A Python-based dashboard for JIRA project management with secure credential handling and comprehensive analytics capabilities"
readme = "README.md"
requires-python = ">=3.11"
license = "MIT"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "vishal<PERSON><EMAIL>"}
]

dependencies = [
    "aiohttp[speedups]",
    "asyncpg",
    "beautifulsoup4",
    "Babel",
    "cachetools",
    "celery",
    "circuitbreaker",
    "dash[compress,async]",
    "dash-ag-grid",
    "dash-daq",
    "dash-svg",
    "dash-extensions",
    "dash-iconify",
    "dash-mantine-components",
    "dependency-injector",
    "diskcache",
    "flask-caching",
    "flask-login",
    "flask-principal",
    "flask-session[redis]",
    "msal",
    "multipledispatch",
    "odfpy==1.4.1",
    "openai==1.79.0",
    "openpyxl",
    "orjson",
    "pandas",
    "pillow",
    "plotly",
    "psycopg2-binary",
    "pykeepass",
    "pyodbc",
    "aioodbc",
    "pyotp",
    "python-dotenv",
    "python-magic",
    "python-pptx",
    "psutil",
    "qrcode",
    "sqlalchemy[asyncio]",
    "sqlalchemy-citext",
    "sqlalchemy-json",
    "sqlalchemy-utils",
    "statsmodels",
    "urllib3[socks]",
    "webdriver-manager",
    "xlrd",
    "xlsxwriter",
    "xmltodict",
]

[project.optional-dependencies]
dev = [
    "cyclonedx-bom",
    "pip-licenses",
    "pytest",
    "fabric",
]
docs = [
    "mkdocs",
    "mkdocs-material",
    "mkdocstrings[python]",
]

[project.urls]
"Homepage" = "https://bitbucket.org/visby8em/jiradashboard"
"Bug Tracker" = "https://biyani.atlassian.net/browse/DASH"

[tool.hatch.build.targets.wheel]
packages = ["jiradashboard"]

[tool.pytest]
testpaths = ["tests"]

[tool.black]
line-length = 79
target-version = ["py312"]

[tool.ruff]
line-length = 79
target-version = "py312"
select = ["E", "F", "W", "I"]
ignore = []

[tool.isort]
profile = "black"
line_length = 79

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
