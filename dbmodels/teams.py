import datetime
from typing import Optional, List

from sqlalchemy import ForeignKey, Index, Sequence, func
from sqlalchemy.dialects.postgresql import ExcludeConstraint, TEXT
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.schema import FetchedValue
from sqlalchemy_utils import ChoiceType, EmailType

from .user import User
from .base import Base, TableName, TableNameCamelCase, CacheyCIText

TABLE_ID_SEQ = Sequence('table_id_seq')
RECURRING_TASK_CHOICES = [
    ("Yes", "Yes"),
    ("No", "No"),
    ("Blank", "")
]

# reading material:
# https://dba.stackexchange.com/questions/140024/placing-a-uniqueness-constraint-on-a-date-range


class Teams(Base, TableName):
    id: Mapped[int] = mapped_column(TABLE_ID_SEQ, primary_key=True, server_default=FetchedValue())
    team_name: Mapped[str] = mapped_column(TEXT, nullable=False)
    emailAddress: Mapped[str] = mapped_column(
        EmailType,
        doc="Email address of the user. Using CIText ensures case in-sensitive comparison",
        nullable=False
    )
    accountId: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    startDate: Mapped[datetime.date] = mapped_column(default=datetime.date(1970, 1, 1), server_default='1970-01-01')
    endDate: Mapped[datetime.date] = mapped_column(default=datetime.date(2050, 12, 31), server_default='2050-12-31')
    active: Mapped[bool] = mapped_column(server_default='t', default=True)
    designation: Mapped[Optional[str]] = mapped_column(TEXT)

    # Relationships
    user: Mapped[Optional["User"]] = relationship(
        "User",
        primaryjoin="Teams.accountId == foreign(User.accountId)",
        backref="teams"
    )

    __table_args__ = (
        ExcludeConstraint(
            (team_name, "="),
            (emailAddress, "="),
            (func.daterange(startDate, endDate, "[]"), "&&"),
            name="uq_teams_emailAddress_daterange",
            using="gist",
        ),
        {'schema': 'public'}
    )


# TABLE_TRAINING_ID_SEQ = Sequence('training_data_id_seq')

class NLPTrainingData(TableNameCamelCase, Base):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    summary: Mapped[Optional[str]] = mapped_column(TEXT)
    label: Mapped[Optional[str]] = mapped_column(TEXT)
    rank: Mapped[Optional[str]] = mapped_column(TEXT)

    __table_args__ = (
        {'schema': 'public'}
    )


class RequestTracker(Base):
    use_snake_case = True
    id: Mapped[int] = mapped_column(primary_key=True)
    desc: Mapped[str] = mapped_column(TEXT, nullable=False)
    reported_by: Mapped[str] = mapped_column(TEXT, nullable=False)
    reported_on: Mapped[datetime.datetime] = mapped_column(nullable=False)
    last_updated_by: Mapped[Optional[str]] = mapped_column(TEXT)
    last_updated_on: Mapped[Optional[datetime.datetime]] = mapped_column()
    project: Mapped[str] = mapped_column(TEXT, nullable=False)
    organization: Mapped[str] = mapped_column(TEXT, nullable=False)
    category: Mapped[Optional[str]] = mapped_column(TEXT)
    priority: Mapped[Optional[str]] = mapped_column(TEXT)
    assigned_to: Mapped[Optional[str]] = mapped_column(TEXT)
    status: Mapped[Optional[str]] = mapped_column(TEXT)
    task: Mapped[Optional[str]] = mapped_column(TEXT)
    reference_no: Mapped[Optional[str]] = mapped_column(TEXT)
    help_type: Mapped[Optional[str]] = mapped_column(TEXT)
    help_from: Mapped[Optional[str]] = mapped_column(TEXT)
    recurring_task: Mapped[Optional[str]] = mapped_column(ChoiceType(RECURRING_TASK_CHOICES))
    add_rt_to_irr: Mapped[Optional[str]] = mapped_column(TEXT)
    irr_id: Mapped[Optional[str]] = mapped_column(TEXT)
    functionality: Mapped[Optional[str]] = mapped_column(TEXT)
    estimated_time_hours: Mapped[Optional[int]] = mapped_column()
    complexity: Mapped[Optional[str]] = mapped_column(TEXT)
    label: Mapped[Optional[str]] = mapped_column(TEXT)
    confidence_percent: Mapped[Optional[float]] = mapped_column()
    label_max_percent: Mapped[Optional[str]] = mapped_column(TEXT)
    max_confidence_percent: Mapped[Optional[float]] = mapped_column()

    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f"<Task(id={self.id}, desc='{self.desc}', ...)>"
