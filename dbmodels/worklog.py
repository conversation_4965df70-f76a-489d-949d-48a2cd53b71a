from datetime import datetime
from typing import Optional

from sqlalchemy import ForeignKey, and_
from sqlalchemy.orm import relationship, foreign, Mapped, mapped_column

from .base import Base, TableName
from .teams import Teams
from .user import User
from .issue import Issue


class WorkLog(Base, TableName):
    id: Mapped[int] = mapped_column(primary_key=True, nullable=False)
    author: Mapped[str] = mapped_column(ForeignKey(User.accountId), nullable=False, index=True)
    updateauthor: Mapped[str] = mapped_column(ForeignKey(User.accountId), nullable=False)
    created: Mapped[datetime] = mapped_column(nullable=False, index=True)
    updated: Mapped[datetime] = mapped_column(nullable=False)
    started: Mapped[datetime] = mapped_column(nullable=False, index=True)
    timeSpent: Mapped[str] = mapped_column(nullable=False)
    timeSpentSeconds: Mapped[int] = mapped_column(nullable=False)
    issue_id: Mapped[int] = mapped_column(ForeignKey(Issue.id), nullable=False, index=True)
    issue_key: Mapped[str] = mapped_column(ForeignKey(Issue.key), nullable=False, index=True)

    # Relationships
    issue: Mapped["Issue"] = relationship(
        back_populates="worklogs",
        foreign_keys=[issue_key],
        primaryjoin="WorkLog.issue_key == Issue.key"
    )
    author_user: Mapped["User"] = relationship(
        foreign_keys=[author],
        backref="authored_worklogs"
    )
    update_user: Mapped["User"] = relationship(
        foreign_keys=[updateauthor],
        backref="updated_worklogs"
    )
    team_created: Mapped[Optional["Teams"]] = relationship(
        "Teams",
        primaryjoin=and_(author == foreign(Teams.accountId), created.between(Teams.startDate, Teams.endDate)),
        viewonly=True
    )
    team_started: Mapped[Optional["Teams"]] = relationship(
        "Teams",
        primaryjoin=and_(author == foreign(Teams.accountId), started.between(Teams.startDate, Teams.endDate)),
        viewonly=True
    )
