from datetime import datetime
from typing import List

from sqlalchemy import String
from sqlalchemy.dialects.postgresql import TEXT, ARRAY, SMALLINT, BOOLEAN
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TableNameCamelCase, CacheyCIText


class SVNBase:
    __abstract__ = True
    name: Mapped[str] = mapped_column(TEXT, primary_key=True, index=True, nullable=False)
    kind: Mapped[str] = mapped_column(TEXT, nullable=False)
    revision: Mapped[int] = mapped_column(nullable=False)
    author: Mapped[str] = mapped_column(TEXT, nullable=False)
    author_email: Mapped[str] = mapped_column(CacheyCIText, nullable=False, index=True)
    svn_server: Mapped[str] = mapped_column(TEXT, nullable=False)
    created_on: Mapped[datetime] = mapped_column(nullable=False)


class SVNBranch(Base, TableNameCamelCase, SVNBase):
    branch_directory: Mapped[str] = mapped_column(TEXT, nullable=False)


class SVNPackage(Base, TableNameCamelCase, SVNBase):
    package_directory: Mapped[str] = mapped_column(TEXT, nullable=False)


class SVNCheckIn:
    __abstract__ = True
    revision: Mapped[int] = mapped_column(nullable=False, primary_key=True)
    filename: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)
    author: Mapped[str] = mapped_column(TEXT, nullable=False)
    author_email: Mapped[str] = mapped_column(CacheyCIText, nullable=False, index=True)
    checked_in_on: Mapped[datetime] = mapped_column(nullable=False, index=True)
    comment: Mapped[str] = mapped_column(TEXT)
    action: Mapped[str] = mapped_column(TEXT)
    prop_mods: Mapped[str] = mapped_column(TEXT, nullable=False)
    text_mods: Mapped[str] = mapped_column(TEXT, nullable=False)
    kind: Mapped[str] = mapped_column(TEXT, nullable=False)
    copyfrom_path: Mapped[str] = mapped_column(TEXT)
    copyfrom_rev: Mapped[str] = mapped_column(TEXT)
    cc_jira: Mapped[List[str]] = mapped_column(ARRAY(TEXT), nullable=True)
    client_jira: Mapped[List[str]] = mapped_column(ARRAY(TEXT), nullable=True)
    app_indexes: Mapped[bool] = mapped_column(BOOLEAN, nullable=False)
    report_indexes: Mapped[bool] = mapped_column(BOOLEAN, nullable=False)
    svn_server: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)


class SVNBranchCheckIn(Base, TableNameCamelCase, SVNCheckIn):
    branch_name: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)


class SVNPackageCheckIn(Base, TableNameCamelCase, SVNCheckIn):
    package_name: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)


class CCJiraBaseCheckin:
    __abstract__ = True
    revision: Mapped[int] = mapped_column(nullable=False, primary_key=True)
    filename: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)
    cc_jira: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)
    author: Mapped[str] = mapped_column(TEXT, nullable=False)
    author_email: Mapped[str] = mapped_column(CacheyCIText(), nullable=False, index=True)
    checked_in_on: Mapped[datetime] = mapped_column(nullable=False, index=True)
    app_index_change: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    report_index_change: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    primary_schema_change: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    primary_sql_change: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    conversion_script: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    control_parameters: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    svn_server: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)


class CCJiraBranchCheckIn(Base, TableNameCamelCase, CCJiraBaseCheckin):
    branch_name: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)


class CCJiraPackageCheckIn(Base, TableNameCamelCase, CCJiraBaseCheckin):
    package_name: Mapped[str] = mapped_column(TEXT, nullable=False, primary_key=True)