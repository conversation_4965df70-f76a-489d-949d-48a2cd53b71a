from typing import Optional

from sqlalchemy import Column, <PERSON>, <PERSON>ole<PERSON>, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import TEXT, DATE, INTEGER
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy_utils import ChoiceType, EmailType, LocaleType, TimezoneType

from .base import Base, TableName, TableNameCamelCase, CacheyCIText


class RunDetailsView(Base, TableNameCamelCase):
    schema_name = Column(TEXT, primary_key=True)
    Topic = Column(TEXT, primary_key=True)
    LAST_RUN = Column(DateTime(timezone=True), nullable=False)
    __table_args__ = (
        {'schema': 'public'}
    )


class User(Base, TableName):
    ACCOUNT_TYPES = [
        ('atlassian', 'Atlassian'),
        ('app', 'App'),
        ('customer', 'Customer'),
    ]

    accountId: Mapped[str] = mapped_column(
        TEXT, primary_key=True, nullable=False,
        unique=True
    )
    accountType: Mapped[ChoiceType] = mapped_column(
        ChoiceType(ACCOUNT_TYPES), nullable=False
    )

    emailAddress: Mapped[EmailType] = mapped_column(
        EmailType, unique=True, nullable=True
    )
    displayName: Mapped[TEXT] = mapped_column(
        TEXT, nullable=False
    )
    active: Mapped[Boolean] = mapped_column(
        Boolean, nullable=False
    )
    timeZone: Mapped[TimezoneType] = mapped_column(
        TimezoneType(backend='zoneinfo'), nullable=True
    )
    locale: Mapped[Optional[LocaleType]] = mapped_column(
        LocaleType, nullable=True
    )
    otp_secret: Mapped[Optional[TEXT]] = mapped_column(
        TEXT, nullable=True
    )
    otp_secret_added_on: Mapped[DATE] = mapped_column(
        DATE, nullable=True
    )

    # teams = relationship("Teams", back_populates="user")

    # assignee = relationship("Issue", foreign_keys="[Issue.assignee]", innerjoin=False, uselist=False)
    # reporter = relationship("Issue", foreign_keys="[Issue.reporter]", innerjoin=False, uselist=False)

    # assigned_issues = relationship(
    #     "Issue", back_populates="assignee_user",
    #     foreign_keys="Issue.assignee"
    # )
    # reported_issues = relationship(
    #     "Issue", back_populates="reporter_user", lazy="dynamic",
    #     foreign_keys="[Issue.reporter]"
    # )

    # issues_by_reporter = relationship(
    #     "Issue", back_populates="reporter_user_other", lazy=True,
    #     foreign_keys="Issue.reporter", uselist=False
    # )
    __table_args__ = (
        {'schema': 'public'}
    )

    # def issue_info(self):
    #     assigned_issues_list = [
    #         {
    #             "id": issue.id,
    #             "key": issue.key,
    #             "summary": issue.summary
    #         } for issue in self.assigned_issues
    #     ]
    #     reported_issues_list = [
    #         {
    #             "id": issue.id,
    #             "key": issue.key,
    #             "summary": issue.summary
    #         } for issue in self.reported_issues
    #     ]
    #     return {
    #         "assigned_issues": assigned_issues_list,
    #         "reported_issues": reported_issues_list
    #     }


class Role(Base, TableName):
    id: Mapped[int] = mapped_column(
        INTEGER, primary_key=True, nullable=False, autoincrement=True
    )
    name: Mapped[str] = mapped_column(TEXT, unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f'<Role {self.name}>'


class Permission(Base, TableName):
    id: Mapped[int] = mapped_column(
        INTEGER, primary_key=True, nullable=False, autoincrement=True
    )
    name: Mapped[str] = mapped_column(TEXT, unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f'<Permission {self.name}>'


class RolePermission(Base, TableName):
    role_id = Column(INTEGER, ForeignKey(Role.id), primary_key=True, nullable=False)
    permission_id = Column(INTEGER, ForeignKey(Permission.id), primary_key=True, nullable=False)
    __table_args__ = (
        {'schema': 'public'}
    )
