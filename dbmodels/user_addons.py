from datetime import datetime

from sqlalchemy.sql.functions import func
from sqlalchemy.sql.sqltypes import DateTime
from sqlalchemy_utils import EmailType, StringEncryptedType

from dbmodels import Base, CacheyCIText, Role, User, Permission
from dbmodels.base import TableName, TableNameCamelCase, HasPrivate, TimestampMixin
from sqlalchemy import Column, INTEGER, ForeignKey, Unicode
from sqlalchemy.orm import Mapped, mapped_column


def get_key():
    return 'dynamic-key'


class UserRole(Base, TableNameCamelCase):
    emailAddress: Mapped[EmailType] = mapped_column(
        EmailType,
        ForeignKey(User.emailAddress),
        primary_key=True
    )
    role_id = Column(
        INTEGER, ForeignKey(Role.id), primary_key=True, nullable=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )


class RolePermissions(Base, TableNameCamelCase):
    role_id: Mapped[int] = mapped_column(
        INTEGER, Foreign<PERSON>ey(Role.id), primary_key=True, nullable=False
    )
    permission_id: Mapped[int] = mapped_column(
        INTEGER, ForeignKey(Permission.id), primary_key=True, nullable=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )


class UserMfa(HasPrivate, TimestampMixin, Base):
    use_snake_case = True

    emailAddress: Mapped[EmailType] = mapped_column(
        EmailType,
        ForeignKey(User.emailAddress),
        primary_key=True
    )
    # qr_code: Mapped[str] = mapped_column(
    #     StringEncryptedType(Unicode, get_key)
    # )
    totp_secret: Mapped[str] = mapped_column(
        StringEncryptedType(Unicode, get_key)
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    expires_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

