from enum import Enum, unique
from typing import Optional

from sqlalchemy import ForeignKey
from sqlalchemy.dialects.postgresql import TEXT, ENUM
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy_utils import LtreeType

from .base import Base, TableName
from .issue import Issue
from .initiativeattribute import InitiativeAttribute


@unique
class IssueClassificationEnum(Enum):
    initiative = 1
    epic = 2
    standard = 3
    subtask = 4


class IssueClassification(Base, TableName):
    """
    Defines data model to store 4 level hierarchy for a given issue key/id
    """
    id: Mapped[int] = mapped_column(ForeignKey(Issue.id), primary_key=True, unique=True)
    key: Mapped[str] = mapped_column(ForeignKey(Issue.key), index=True, nullable=False, unique=True)
    issueclass: Mapped[IssueClassificationEnum] = mapped_column(ENUM(IssueClassificationEnum), nullable=False)
    path_id: Mapped[Optional[str]] = mapped_column(LtreeType, nullable=True)
    path_key: Mapped[Optional[str]] = mapped_column(LtreeType, nullable=True)
    initiative_key: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    epic_key: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    standard_key: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    subtask_key: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    initiative_id: Mapped[Optional[int]] = mapped_column(nullable=True)
    epic_id: Mapped[Optional[int]] = mapped_column(nullable=True)
    standard_id: Mapped[Optional[int]] = mapped_column(nullable=True)
    subtask_id: Mapped[Optional[int]] = mapped_column(nullable=True)

    # Relationships
    issue: Mapped["Issue"] = relationship(
        "Issue",
        foreign_keys=[id],
        backref="classification",
        primaryjoin="IssueClassification.id == Issue.id"
    )

    myself: Mapped["Issue"] = relationship(
        "Issue",
        backref='issueclassification_self',
        primaryjoin="IssueClassification.key == Issue.key",
        foreign_keys=[key]
    )

    standard_issue: Mapped[Optional["Issue"]] = relationship(
        "Issue",
        backref='issueclassification_stan',
        primaryjoin="IssueClassification.standard_id == Issue.id",
        foreign_keys=[standard_id]
    )

    epic_issue: Mapped[Optional["Issue"]] = relationship(
        "Issue",
        backref='issueclassification_epic',
        primaryjoin="IssueClassification.epic_key == Issue.key",
        foreign_keys=[epic_key]
    )

    initiative_attr: Mapped[Optional["InitiativeAttribute"]] = relationship(
        "InitiativeAttribute",
        backref='issueclassification',
        primaryjoin="IssueClassification.initiative_key == InitiativeAttribute.initiative_key",
        foreign_keys="InitiativeAttribute.initiative_key"
    )