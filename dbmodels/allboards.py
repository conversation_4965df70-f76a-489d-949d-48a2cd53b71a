from typing import Optional, Annotated

from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TableName


class AllBoards(Base, TableName):
    id: Mapped[int] = mapped_column(primary_key=True, nullable=False)
    name: Mapped[str] = mapped_column(nullable=False)
    type: Mapped[str] = mapped_column(nullable=False)
    projectId: Mapped[Optional[int]] = mapped_column()
    displayName: Mapped[Optional[str]] = mapped_column(nullable=True)
    projectName: Mapped[Optional[str]] = mapped_column(nullable=True)
    projectKey: Mapped[Optional[str]] = mapped_column(nullable=True)
    projectTypeKey: Mapped[Optional[str]] = mapped_column()
    userId: Mapped[Optional[int]] = mapped_column()
    userAccountId: Mapped[Optional[str]] = mapped_column()
    __table_args__ = (
        {'schema': 'public'}
    )
