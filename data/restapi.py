import asyncio
import base64
import datetime
import os
import sys
from io import BytesIO

import pandas as pd
import requests
from PIL import Image
from requests.auth import HTTPBasicAuth
from pykeepass import PyKeePass as pkp
import json
import time
from aiohttp import ClientRequest, ClientSession, BasicAuth

import data.custom_logger
from data import helper
from typing import Union, Optional

from data.helper import SHORT_TIME_OUT
from collections import defaultdict
from tenacity import retry, stop_after_attempt, wait_fixed, before_log, after_log, before_sleep_log
from circuitbreaker import CircuitBreaker
from dash import get_app
from flask import session
from .decorators import conditional_cache_decorator, cache


# if __name__ == "__main__":
#     from cachetools import cached, TTLCache
#     from cachetools.keys import hashkey
#
#     cache = TTLCache(maxsize=100, ttl=300)
# else:
#     from flask_caching import Cache
#     from flask_caching.backends import RedisCache
#     from dash import get_app
#     dash_app = get_app()
#     cache = Cache(
#         dash_app.server,
#         config={
#             "DEBUG": True,
#             'CACHE_TYPE': RedisCache.__name__,
#             'CACHE_REDIS_URL': os.environ.get('REDIS_URL', ''),
#             "CACHE_DEFAULT_TIMEOUT": 600
#         }
#     )
# Move this to right location after testing
async def image_to_base64(self, image_data: bytes):
    image = Image.open(BytesIO(image_data))
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    return base64.b64encode(buffered.getvalue()).decode('utf-8')


class JiraRestAioHttpRepository:
    def __init__(self, user_id: str, api_token: str, host: str = None):
        self.host: str = 'https://corecard.atlassian.net' if host is None else host
        self.auth = BasicAuth(user_id, api_token)

    async def _get(
            self, _session, path: str,
            params: dict = None
    ):
        url = f"{self.host}{path}"
        headers = {"Accept": "application/json"}
        async with _session.get(
                url, headers=headers, auth=self.auth,
                params=params
        ) as response:
            print(path)
            response_json = await response.json()
            return {"status": response.status, "response": response_json}

    async def _post(self, _session, path, payload):
        url = f"{self.host}{path}"
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        async with _session.post(url, headers=headers, auth=self.auth, data=payload) as response:
            response_json = await response.json()
            return {"status": response.status, "response": response_json}

    async def _put(self, _session, url, payload):
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        async with _session.put(url, headers=headers, auth=self.auth, data=payload) as response:
            response_json = await response.json()
            return {"status": response.status, "response": response_json}

    async def get_myself(self):
        path = f"/rest/api/3/myself"
        async with ClientSession() as _session:
            return await self._get(_session, path)

    async def get_project_permissions(self):
        path = f"/rest/api/3/permissions/project"
        payload = json.dumps({
            "permissions": ["BROWSE_PROJECTS"]
        })
        async with ClientSession() as _session:
            return await self._post(_session, path, payload)

    async def download_image(self, url):
        async with ClientSession() as _session:
            async with _session.get(url, auth=self.auth) as response:
                _data = await response.read()
                return {"status_code": response.status, "data": _data}


def dict2values(raw):
    arr = defaultdict(lambda: "")

    def extract(raw, arr):
        seqs = tuple, list, set, frozenset
        final = str, int
        # arr['customfield_10018.key'] = arr['customfield_10018.id'] = None

        if isinstance(raw, dict):
            for key, value in raw.items():
                if key == "fields":
                    extract(value, arr)
                elif key in ['issuelinks', 'worklog', 'changelog', 'renderedFields']:
                    pass
                elif key in ["assignee", "reporter"]:
                    arr[key] = None if value is None else value['accountId']
                elif key in ["expand", "self"]:
                    pass
                elif key in ["issuetype"]:
                    arr[key] = value['name']
                    arr['isSubTask'] = value['subtask']
                elif key == "parent":
                    if value is not None:
                        arr[f'{key}_key'] = value['key']
                        arr[f'{key}_id'] = value['id']
                elif key in ["resolution", "status", "priority"]:
                    arr[key] = None if value is None else value['name']
                    if key == "status":
                        arr['statusCategory'] = value['statusCategory']['name']
                elif key in ["components", "fixVersions", "versions"]:
                    arr[key] = None if value is None else [i['name'] for i in value]
                elif key in ["aggregateprogress", "progress"]:
                    if value is not None:
                        arr[key + '_' + 'progress'] = value["progress"] / 3600
                        arr[key + '_' + 'total'] = value["total"] / 3600
                        if 'percent' in value:
                            arr[key + '_' + 'percent'] = value['percent'] / 100
                        else:
                            arr[key + '_' + 'percent'] = None
                elif key in ['aggregatetimeoriginalestimate']:
                    arr[key] = 0 if value is None else value
                elif key in ['customfield_10001']:
                    if value is not None:
                        arr['Team'] = value['title']
                    else:
                        arr['Team'] = None
                elif key in ['customfield_10020']:
                    if value is not None:
                        arr[key + '.' + 'name'] = [name["name"] for name in value]
                        arr[key + '.' + 'id'] = [name["id"] for name in value]
                    else:
                        arr[key + '.' + 'name'] = None
                        arr[key + '.' + 'id'] = None
                elif key in ['customfield_10146', 'customfield_10078']:
                    if value is not None:
                        arr[key] = value['value']
                    else:
                        arr[key] = None
                elif key in ['customfield_10147']:
                    if value is None:
                        arr[key] = 0
                    else:
                        arr[key] = value
                else:
                    arr[key] = value
        elif isinstance(raw, list):
            for item in raw:
                extract(item, arr)
        return arr

    return extract(raw, arr)


def get_env_variables(user: str = None, passwd: str = None) -> dict:
    if __name__ == "__main__" or os.curdir == '.':
        try:
            with open("config.json", encoding='utf-8', errors='ignore') as fp:
                data = json.load(fp)
        except FileNotFoundError:
            with open("data/config.json", encoding='utf-8', errors='ignore') as fp:
                data = json.load(fp)
    else:
        print(f"Checking the call: {os.curdir}, {__name__}")
        with open("data/config.json", encoding='utf-8', errors='ignore') as fp:
            data = json.load(fp)
    ref = pkp(filename=data[f'KeePassDB_{os.name}'], keyfile=data[f'KeyPassKey_{os.name}'])
    entry = ref.find_entries(title=data['KpCoreCardJira'], first=True)
    data['baseurl'] = entry.url
    if user is None:
        data['username'] = entry.username
    else:
        data['username'] = user
    if passwd is None:
        data['pwd'] = entry.password
    else:
        data['pwd'] = passwd
    # Should use username and password from arrary
    # data['auth'] = HTTPBasicAuth(entry.username, entry.password)
    try:
        data['auth'] = session.get("jira_basic_auth") or HTTPBasicAuth(data['username'], data['pwd'])
    except RuntimeError:
        data['auth'] = HTTPBasicAuth(data['username'], data['pwd'])
    data['payload'] = None
    data['headers'] = {'Accept': data['Accept'], 'Content-Type': data['Content-Type']}

    # Get the cloudId
    response = requests.get(url=f'{data["baseurl"]}/_edge/tenant_info')
    for k, v in response.json().items():
        data[k] = v
    # Delete unwanted keys that are not going to be used later
    keys = ['KeePassDB_nt', 'KeyPassKey_nt', 'KeePassDB_posix', 'KeyPassKey_posix', 'KpCoreCardJira', 'username', 'pwd',
            'Accept', 'Content-Type']

    keys_to_remove = set(keys).intersection(set(data.keys()))
    for k in keys_to_remove:
        del data[k]
    return data


# Define a Circuit Breaker with a failure threshold and reset timeout
breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=10)


@retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
@breaker
def make_http_request(request_method: str, **kwargs) -> requests.Response:
    response = requests.Response()
    try:
        if request_method in ["POST", "PUT"]:
            print(kwargs['data'], f"{kwargs['baseurl']}{kwargs['url_seg']}", )
            response = requests.request(
                request_method,
                url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
                headers=kwargs['headers'],
                auth=kwargs['auth'],
                data=kwargs['data']
            )
        elif request_method == "GET":
            response = requests.request(
                request_method,
                url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
                headers=kwargs['headers'],
                auth=kwargs['auth'],
                params=kwargs['payload'] if kwargs['payload'] is not None else None,
            )

        # response.raise_for_status()
        return response
    except requests.exceptions.ConnectionError as errc:
        print(f'Connection error: {errc}')
        raise errc
    except requests.exceptions.HTTPError as errh:
        print(f'http error: {errh}')
        raise errh
    except requests.exceptions.Timeout as errt:
        print(f'Timeout: {errt}')
        raise errt
    except requests.exceptions.RequestException as err:
        print(f'{err.response.text}')
        raise err


def get_sprint_details(boardId: int) -> (list, int):
    """
    source: https://stackoverflow.com/questions/54637847/how-to-change-dictionary-keys-in-a-list-of-dictionaries?noredirect=1&lq=1
    :param boardId:
    :return:
    """
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/agile/1.0/board/{boardId}/sprint"
    try:
        response = make_http_request("GET", **config_dict)
        print(json.dumps(response.json()))
        sprint_detail = [{key: value for key, value in i.items() if key in ["id", "state", "name"]} for i in
                         response.json()["values"]]

        for d in sprint_detail:
            d['value'] = d.pop('id')
            d['label'] = d.pop("name")

        return sprint_detail, f'{response.elapsed.total_seconds():.3f}'
    except Exception as e:
        print(e)
        exit(1)


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_scope_change_burndown_chart(boardId: int, sprintId: int):
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/greenhopper/1.0/rapid/charts/scopechangeburndownchart"
    config_dict['payload'] = {'rapidViewId': boardId, 'sprintId': sprintId}

    response = make_http_request("GET", **config_dict)
    print(f'Time taken in get_scope_change_burndown_chart: {response.elapsed.total_seconds()} sec')
    return response.json()


def get_sprint_velocity(boardId: int):
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/greenhopper/1.0/rapid/charts/velocity"
    config_dict['payload'] = {'rapidViewId': boardId}
    try:
        response = make_http_request("GET", **config_dict)
        print(f'Time taken in get_sprint_velocity: {response.elapsed.total_seconds()} sec')
        return response.json()
    except Exception as e:
        print(e)
        exit(1)


def get_board_issues_for_sprint(boardId, sprintId) -> Union[list, list, int, int]:
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/agile/1.0/board/{boardId}/sprint/{sprintId}/issue"
    startAt = 0
    maxResults = 50
    total = 50
    issue_list = []
    epic_list = []
    aggregatetimeoriginalestimate = 0
    storypoint = 0
    starttime = time.time_ns()
    while startAt < total:
        config_dict['payload'] = {'startAt': startAt, 'maxResults': maxResults}
        try:
            response = make_http_request("GET", **config_dict)
            print(f'Clock time: {(time.time_ns() - starttime) * pow(10, -9)}')
            print(f'get_board_issues_for_sprint Time Taken: {response.elapsed.total_seconds()} seconds')
            issues = [{key: value for key, value in d.items() if key in ['key']} for d in response.json()['issues']]
            issue_list.extend([item for sublist in [list(item.values()) for item in issues] for item in sublist])

            for d in response.json()['issues']:
                for key, value in d.items():
                    if key == "fields":
                        for k, v in value.items():
                            if k == "epic":
                                if v is not None:
                                    epic_list.append(v['key'])
                            elif k == "aggregatetimeoriginalestimate":
                                aggregatetimeoriginalestimate += v if v is not None else 0
                            elif k == "customfield_10024":
                                storypoint += v if v is not None else 0

            startAt += maxResults
            total = response.json()['total']
            print(f'Total - time taken: {(time.time_ns() - starttime) * pow(10, -9)}')
        except Exception as e:
            print(e)
            exit(1)
        # epic = [{key: value for key, value in d.items() if key in ['fields']} for d in response.json()['issues']]

    # print(f'Original Time estimate: {aggregatetimeoriginalestimate}, Story Points: {storypoint}')
    aggregatetimeoriginalestimate /= 3600
    return [epic_list, issue_list, int(aggregatetimeoriginalestimate), int(storypoint)]


def update_epic_estimates(epic_key: str, ) -> list:
    my_logger = data.custom_logger.MyLogger().get_logger()
    startAt = 0
    total = 100
    estimated_effort = defaultdict(lambda: 0)
    config_dict = get_env_variables()
    payload_dict = dict(jql=f"'epic link' = {epic_key}")
    fields = [
        "aggregatetimeoriginalestimate",
        "components",
        "issuetype",
        "customfield_10120",
        "customfield_10121",
        "customfield_10122",
        "customfield_10123",
        "customfield_10124",
        "customfield_10125",
        "customfield_10126"
    ]
    while startAt < total:
        payload_dict['startAt'] = startAt
        payload_dict['fields'] = fields
        payload_dict['maxResults'] = 100
        config_dict['url_seg'] = "/rest/api/3/search"
        config_dict['data'] = json.dumps(payload_dict)
        my_logger.debug("Calling make_http_request")
        res = make_http_request("POST", **config_dict)
        if res.status_code != 200:
            print(f"Error:{payload_dict['jql']}")
            exit(1)
        for issue in res.json()["issues"]:
            issue_dict = dict2values(issue)
            if issue_dict["issuetype"] == "Specifications":
                # Sum up BA effort
                estimated_effort['customfield_10122'] += issue_dict["aggregatetimeoriginalestimate"]
            elif issue_dict["issuetype"] == "Story":
                if "Reports" in issue_dict["components"]:
                    # Reports estimates
                    estimated_effort['customfield_10124'] += issue_dict["aggregatetimeoriginalestimate"]
                else:
                    # dash_app estimates
                    estimated_effort['customfield_10123'] += issue_dict["aggregatetimeoriginalestimate"]
            elif issue_dict["issuetype"] == "Feature Testing":
                # QA estimates
                estimated_effort['customfield_10125'] += issue_dict["aggregatetimeoriginalestimate"]

        total = res.json()['total']
        startAt += len(res.json()["issues"])
    # Total Dev Effort
    estimated_effort['customfield_10121'] = estimated_effort['customfield_10123'] + estimated_effort[
        'customfield_10124']
    for field_name in ['customfield_10122', 'customfield_10123', 'customfield_10124', 'customfield_10125']:
        # Contingency
        estimated_effort['customfield_10126'] += estimated_effort[field_name] * 0.1

    for field_name in ['customfield_10122', 'customfield_10121', 'customfield_10125', 'customfield_10126']:
        estimated_effort['customfield_10120'] += estimated_effort[field_name]
    estimated_effort = {key: int(estimated_effort[key] / 3600) for key in estimated_effort.keys()}

    update_fileds = {}
    for key, value in estimated_effort.items():
        update_fileds[key] = value
    field_dict = dict(fields=update_fileds)

    config_dict['url_seg'] = f"/rest/api/3/issue/{epic_key}"
    config_dict['data'] = json.dumps(field_dict)

    res = make_http_request("PUT", **config_dict)
    return [res, estimated_effort['customfield_10120'], estimated_effort['customfield_10121'],
            estimated_effort['customfield_10122'], estimated_effort['customfield_10125'],
            estimated_effort['customfield_10126']]


async def update_epic_totals():
    await asyncio.gather(update_epic_totals())


def get_transition(issueIdOrKey: str):
    workflow_scheme_transition = defaultdict(lambda: dict())
    workflow_scheme_transition['Plat_Epic_V2'] = {'123': 456}
    workflow_scheme_transition['Plat_WF_Story_new V2'] = {
        '1': 321, '10084': 311, '10085': 341, 10086: 401
    }
    # print(workflow_scheme_transition['Plat_Epic_V2'])

    config_dict = get_env_variables()
    # config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}/transitions"
    # response = make_http_request("GET", **config_dict)
    # print(json.dumps(response.json()))

    config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}"
    config_dict['payload'] = {'fields': ['status', 'summary', "issuekey", "issuetype"], 'fieldsByKeys': "false",
                              'expand': []}
    resp = make_http_request("GET", **config_dict)
    print(json.dumps(resp.json()['fields']['status']['id']))


def get_id_status(issueIdOrKey: str) -> [str, str, str, str, str, str, str, str]:
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}"
    config_dict['payload'] = {
        'fields': [
            'status', 'summary', "issuekey", "issuetype", 'resolution',
            'customfield_10015', 'timeoriginalestimate', 'duedate'
        ],
        'fieldsByKeys': "false",
        'expand': []
    }
    resp = make_http_request("GET", **config_dict)

    return resp.json()['fields']['status']['id'], \
        resp.json()['fields']['status']['name'], \
        resp.json()['fields']['issuetype']['id'], \
        resp.json()['fields']['issuetype']['name'], \
        resp.json()['fields']['resolution'], resp.json()['fields']['customfield_10015'], resp.json()['fields'][
        'duedate'], \
        resp.json()['fields']['timeoriginalestimate']


def get_project_details(projectIdOrKey: str):
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/project/{projectIdOrKey.upper()}"
    response = make_http_request("GET", **config_dict)
    # Source: https://stackoverflow.com/questions/26716616/convert-a-pandas-dataframe-to-a-dictionary
    # print(df.set_index('id').T.to_dict('records'))
    # dict_value = [{key: value} for key, value in response.json()['issueTypes'].items()]
    # dict_value = [node.items() for node in response.json()['issueTypes']]
    # print(dict_value)
    # for node in response.json()['issueTypes']:
    #     for key, value in node.items():
    #         print(key, value if key in ['id', 'name'] else None )
    return response.json()['id'], response.json()['issueTypes']


def add_user(projectIdOrKey: str = 'plat', email_id: str = None):
    """Creates a user. However, this can't be done using Cloud REST API as it is disabled by Atlassian"""
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/user"

    if email_id is None:
        raise Exception("Provide valid email id")
    config_dict['data'] = json.dumps({"emailAddress": email_id, "applicationKeys": ["jira-software"]})
    print(config_dict)
    res = make_http_request("POST", **config_dict)
    return res


def get_project_roles(projectIdOrKey: str = 'plat'):
    """Returns a list of project roles. The last part is the role id"""
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/project/{projectIdOrKey.upper()}/role"
    res = make_http_request("GET", **config_dict)
    return res


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_workflow_schemes(projectId: str) -> dict:
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/workflowscheme/project"
    config_dict['payload'] = dict(projectId={projectId})
    response = make_http_request("GET", **config_dict)
    for value in response.json()['values']:
        return value['workflowScheme']['issueTypeMappings']


def transition_issue(issueIdOrKey: str, projectId: str, user: str = None, passwd: str = None):
    my_logger = helper.MyLogger().get_logger()
    my_logger.debug(f'project id = {projectId}')
    project_id, issuetypes = get_project_details(projectId)
    workflow_schemes = get_workflow_schemes(project_id)
    # workflow_scheme_transition = defaultdict(lambda: dict())
    # df = pd.DataFrame(issuetypes, columns=['id', 'name'])
    # options include dict, list, series, split, records, index
    # Source: https://stackoverflow.com/questions/26716616/convert-a-pandas-dataframe-to-a-dictionary
    # issue_id_name_dict = df.set_index('id').T.to_dict('records')
    # pending
    # Plat_WF_SubTask_Dev, Plat_WF_Std_SubTask, Plat_WF_SubTask, Plat_WF_SubTask_NoCondition
    # PLAT_BUG_V2.1
    # PLAT_BA_WF_V2, WF_Initiative_Common
    workflow_scheme_transition = defaultdict(lambda: defaultdict(str))
    workflow_scheme_transition['Plat_Epic_V3']['1'] = '11'
    workflow_scheme_transition['Plat_Epic_V3']['10147'] = '21'
    workflow_scheme_transition['Plat_Epic_V3']['10156'] = '31'
    workflow_scheme_transition['Plat_Epic_V3']['10070'] = '41'
    workflow_scheme_transition['Plat_Epic_V3']['10072'] = '51'
    workflow_scheme_transition['Plat_Epic_V3']['10152'] = '61'
    workflow_scheme_transition['Plat_Epic_V3']['10153'] = '191'
    workflow_scheme_transition['Plat_Epic_V3']['10195'] = '201'

    workflow_scheme_transition['Plat_WF_Story_V3']['1'] = '411'
    workflow_scheme_transition['Plat_WF_Story_V3']['10095'] = '421'
    workflow_scheme_transition['Plat_WF_Story_V3']['10193'] = '441'
    workflow_scheme_transition['Plat_WF_Story_V3']['10085'] = '461'
    workflow_scheme_transition['Plat_WF_Story_V3']['10079'] = '491'
    workflow_scheme_transition['Plat_WF_Story_V3']['10194'] = '501'

    workflow_scheme_transition['PLAT_BA_WF_V2']['1'] = '11'
    workflow_scheme_transition['PLAT_BA_WF_V2']['10147'] = '21'
    workflow_scheme_transition['PLAT_BA_WF_V2']['10148'] = '31'
    workflow_scheme_transition['PLAT_BA_WF_V2']['10149'] = '41'
    workflow_scheme_transition['PLAT_BA_WF_V2']['10127'] = '61'

    for key in ['Plat_WF_SubTask_Dev', 'Plat_WF_Std_SubTask', 'Plat_WF_SubTask', 'Plat_WF_SubTask_NoCondition']:
        workflow_scheme_transition[key]['1'] = '11'
        workflow_scheme_transition[key]['3'] = '21'

    config_dict = get_env_variables(user, passwd)
    config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}/transitions"
    count = 0

    while (count := count + 1) < 10:
        status_id, status_name, issuetype_id, issuetype_name, resolution, start_date, duedate, original_estimate = get_id_status(
            issueIdOrKey)
        # print(f'Workflowscheme: {workflow_scheme_transition[workflow_schemes[issuetype_id]]}')
        # print(f'Transition: {workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id]}')
        if resolution is not None:
            my_logger.debug(f'{issueIdOrKey} status = {status_name} with resolution = {resolution["name"]}')
            my_logger.debug(f'Nothing further to do.')
            my_logger.debug('Returning!!!!')
            break
        if workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id] == "":
            my_logger.info(
                f'Value for {issueIdOrKey} of issuetype {issuetype_name} for status {status_name} not found'
            )
            break
        else:
            my_logger.debug("Get workflow details")
            my_logger.debug(workflow_schemes[issuetype_id])
            my_logger.debug(workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id])
            payload = {}
            if workflow_schemes[issuetype_id] in ['Plat_WF_SubTask']:
                if workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id] == '11':
                    my_logger.debug("Values got from issue")
                    my_logger.debug(f"{start_date}, {duedate}, {original_estimate}")

                    if start_date is None:
                        payload['customfield_10015'] = datetime.date.today().strftime("%Y-%m-%d")
                    if duedate is None:
                        payload['duedate'] = datetime.date.today().strftime("%Y-%m-%d")

            if payload == {}:
                config_dict['data'] = json.dumps(
                    {
                        "transition": {"id": workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id]}
                    }
                )
            else:
                config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}"
                config_dict['data'] = json.dumps({"fields": payload})

                print(json.dumps(config_dict['data']))
                res = make_http_request("PUT", **config_dict)
                print(f'{issueIdOrKey} Fields update Status code {res.status_code}')

                config_dict['url_seg'] = f"/rest/api/3/issue/{issueIdOrKey}/transitions"
                config_dict['data'] = json.dumps(
                    {
                        "transition": {"id": workflow_scheme_transition[workflow_schemes[issuetype_id]][status_id]}
                    }
                )

            try:
                response = make_http_request("POST", **config_dict)
                my_logger.info(f"{issueIdOrKey} {response.status_code}")
                if response.status_code == 400:
                    my_logger.exception('Bad Request')
                    break
                elif response.status_code == 401:
                    my_logger.exception('Unauthorized')
                    break
                elif response.status_code == 404:
                    my_logger.exception('Not Found')
                    break

            except requests.exceptions.HTTPError:
                my_logger.info("""
                    no transition is specified.
                    the user does not have permission to transition the issue.
                    a field that isn't included on the transition screen is defined in fields or update.
                    a field is specified in both fields and update.
                    the request is invalid for any other reason.
                    """)
                break


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_myself(user: str, passwd: str):
    my_logger = data.custom_logger.MyLogger().get_logger()

    config_dict = get_env_variables(user, passwd)
    config_dict['url_seg'] = f"/rest/api/3/myself"
    config_dict['payload'] = {'expand': 'groups,applicationRoles'}

    response = make_http_request("GET", **config_dict)
    print(response.request.url)

    my_logger.debug(f'{response.status_code}')
    return response


def get_authentication_result():
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"rest/api/3/authentication/auhtorization"
    response = make_http_request("GET", **config_dict)
    print(response.status_code)


# @cache.memoize(timeout=SHORT_TIME_OUT)
def get_project_list(user: str, passwd: str) -> list:
    my_logger = helper.MyLogger().get_logger()
    _funcname = sys._getframe().f_code.co_name
    url = "https://corecard.atlassian.net/rest/api/3/permissions/project"

    config_dict = get_env_variables(user, passwd)
    config_dict['url_seg'] = f"/rest/api/3/permissions/project"
    config_dict['data'] = dict(permissions='BROWSE_PROJECTS')
    payload = json.dumps({
        "permissions": [
            "BROWSE_PROJECTS"
        ]
    })
    response = requests.request("POST", url, data=payload, headers=config_dict['headers'], auth=config_dict['auth'])
    my_logger.info(f'{_funcname} Response Code: {response.status_code}')
    if response.status_code == 200:
        output = response.json()

        # print(json.dumps(json.loads(response.text), sort_keys=True, indent=4, separators=(",", ": ")))
        master_list = [prj['key'].lower() for prj in output['projects']]
        # my_logger.debug(f"project list returned: {master_list}")
        # base_list = ['plat', 'cpp', 'ccp']
        # remove_element = list(set(base_list).difference(master_list))
        #
        # for elem in remove_element:
        #     base_list.remove(elem)
        # master_list = list(set(master_list) - set(base_list))
        #
        # my_logger.debug((f'final list: {base_list + master_list}'))
        return master_list
    else:
        my_logger.info("No projects returned")
        return []


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_rmp_portal():
    url = 'https://rmpapi.corecard.in/api/Account/UserLogin'
    header = {'accept': 'text/plain', 'Content-Type': 'application/json'}
    data = {
        "userName": "<EMAIL>",
        "emailId": "<EMAIL>",
        "password": "password"
    }
    response = requests.post(url, json=data)
    jwt_token = response.json()['jwtToken']

    url2 = 'https://rmpapi.corecard.in/api/PODEnvironment/GetAllPODEnvironmentForVersionManagement'
    data = {
        "processingEnvironmentID": 1,
        "podid": 0,
        "environmentName": "",
        "currentPage": 1
    }
    header['Authorization'] = f"Bearer {jwt_token}"
    node_list = []
    while True:
        response = requests.post(url2, json=data, headers=header)
        data['currentPage'] += 1
        req_data = response.json()['lstPODEnvironmentDTO']

        if req_data:
            node_list.extend([
                {
                    key: value for key, value in node.items()
                    if key in ['environmentName', 'applicationVersion', 'platformVersion', 'podName']
                } for node in req_data
            ])

        if response.json()['currentPage'] == response.json()['totalPages']:
            break

    df = pd.DataFrame(node_list)
    df_jazz = df.query("podName == 'POD3'")
    df_pivot = pd.pivot_table(df_jazz, values=['applicationVersion', 'platformVersion'], index=['environmentName'],
                              columns=['podName'], aggfunc=lambda x: x)
    return df


if __name__ == '__main__':

    resp = get_sprint_details(39)
    print(json.dumps(resp))
    resp = get_scope_change_burndown_chart(39, 87)
    print(json.dumps(resp))
    for x in ["changes", "statisticField", "issueToParentKeys", "issueToSummary", "workRateData", "openCloseChanges"]:
        resp.pop(x, None)

    for x, y in resp.items():
        resp[x] = time.strftime('%Y-%b-%d %I:%M %p %Z',
                                time.localtime(y / 1000)) if x not in ["lastUserWhoClosedHtml", "warningMessage"] else y
