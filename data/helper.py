import ast
import base64
import errno

import dash_bootstrap_components as dbc
import select
from decimal import Decimal

import psutil

import os
import re
import sys

import requests
import logging
from logging.handlers import TimedRotatingFileHandler
from dataclasses import dataclass, field

from functools import lru_cache, wraps, WRAPPER_ASSIGNMENTS
from datetime import datetime, timedelta, date
from typing import Any
from time import time, perf_counter_ns, process_time_ns, time_ns
from collections import deque, namedtuple

import struct
import json
import inspect
import contextlib
import cProfile
import io
import pstats
import pandas as pd
import socket
# , AF_INET, SOCK_DGRAM
import hashlib
import pyotp
import qrcode
import string
import random
import dash.html as html
from dash import callback_context, html
from dependency_injector import providers
from dependency_injector.wiring import inject
from flask_login import UserMixin
from plotly import graph_objs as go

from sqlalchemy.orm import Session

# from custom_container import KPDatabaseOpener, IKeyPassPasswordManager, KeePassPasswordManager, My<PERSON>eePassContainer
from data.custom_logger import My<PERSON>ogger
from dependency_injector import containers

SHORT_TIME_OUT = 600
TIME_OUT = 3600
LONG_TIME_OUT = 14400


# Reference: https://docs.sqlalchemy.org/en/14/faq/performance.html
@contextlib.contextmanager
def profiled(filename: str = 'profile.log'):
    pr = cProfile.Profile()
    pr.enable()
    yield
    pr.disable()
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats()
    # uncomment this to see who's calling what
    # ps.print_callers()
    # print(s.getvalue())
    if os.name == "nt":
        with open(f'e:/vishal/{filename}', 'w+') as f:
            f.write(s.getvalue())
    else:
        with open(f'/tmp/{filename}', 'w+') as f:
            f.write(s.getvalue())


# This is not being used
# @contextlib.contextmanager
# def open_kp_database(database_path: str, master_password: Optional[str] = None, master_keyfile: Optional[str] = None):
#     if master_password is None and master_keyfile is None:
#         raise ValueError("At least one of master_password or keyfile is required.")
#     try:
#         with PyKeePass(database_path, password=master_password, keyfile=master_keyfile) as kp:
#             yield kp
#     except CredentialsError:
#         raise Exception("Invalid master password or keyfile.")
#     except (HeaderChecksumError, PayloadChecksumError):
#         raise Exception("The KeePass database appears to be corrupt.")
# Remove this code duing clean-up


def get_network_info() -> dict:
    interfaces = psutil.net_if_addrs()

    # Define a named tuple to store network information
    NetworkInfo = namedtuple('NetworkInfo', ['ip_address', 'subnet_mask'])

    network_info: dict = {}

    for interface, address in interfaces.items():
        for addr in address:
            if addr.family == socket.AF_INET:
                network_info[interface] = NetworkInfo(ip_address=addr.address, subnet_mask=addr.netmask)
    return network_info

def is_server_accessible(ip, port: int, timeout: int = 2):
    my_logger = MyLogger().get_logger()
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        socket.setdefaulttimeout(timeout)

        socket.socket.setblocking = setblocking()
        sock.setblocking(True)
        sock.settimeout(timeout)
        start_time = time_ns()
        result = sock.connect_ex((ip, port))
        if result == 0:
            return True
        elif result in (errno.EINPROGRESS, 10035):  # EINPROGRESS on Linux, WSAEWOULDBLOCK on Windows
            # Use select to wait until the socket is ready for writing
            ready_to_write, _, _ = select.select([], [sock], [], timeout)
            my_logger.debug(f"Status of select command: {ready_to_write}")
            if ready_to_write:
                # Check if the connection was successful
                result = sock.getsockopt(socket.SOL_SOCKET, socket.SO_ERROR)
                if result == 0:
                    my_logger.debug(f"Connection successful!!!")

                    return True
                else:
                    my_logger.debug(f"Connection failed with error code: {result}")

                    return False
            else:
                my_logger.debug(f"Connection timed out")
                my_logger.debug(f"error code: {result} error msg = {errno.errorcode.get(result, 'Unknown Error')}")
                my_logger.debug(f"Time taken: {(time_ns() - start_time) * pow(10, -6)} ms")

                return False
        else:
            my_logger.debug(
                f"Connection failed. error code: {result} error msg = {errno.errorcode.get(result, 'Unknown Error')}")
            my_logger.debug(f"Time taken: {(time_ns() - start_time) * pow(10, -6)} ms")

            return False
    except Exception as e:
        my_logger.exception(f"Exception caught: {e}")
        raise e
    finally:
        sock.close()


def setblocking():
    """
    Source: https://medium.com/pipedrive-engineering/socket-timeout-an-important-but-not-simple-issue-with-python-4bb3c58386b4
    Returns:

    """
    setblocking_func = socket.socket.setblocking

    def wrapper(self, flag):
        if flag:
            # prohibit timeout reset
            timeout = socket.getdefaulttimeout()
            if timeout:
                self.settimeout(timeout)
            else:
                setblocking_func(self, flag)
        else:
            setblocking_func(self, flag)

    wrapper.__doc__ = setblocking_func.__doc__
    wrapper.__name__ = setblocking_func.__name__
    return wrapper


def getNTPTime(host="pool.ntp.org") -> int:
    # CREDIT: https://www.mattcrampton.com/blog/query_an_ntp_server_from_python/
    # Change to return epoch time.
    # Convert epoch time to timestamp using fromtimestamp
    port = 123
    buf = 1024
    address = (host, port)
    msg = '\x1b' + 47 * '\0'

    # reference time (in seconds since 1900-01-01 00:00:00)
    # To get reference time use below command
    # print(datetime(1900, 1, 1, 0, 0, 0, 0, pytz.timezone('UTC')).timestamp())
    TIME1970 = 2208988800.0  # 1970-01-01 00:00:00

    # connect to server
    client = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    client.sendto(msg.encode('utf-8'), address)
    msg_b, address = client.recvfrom(buf)
    _t = struct.unpack("!12I", msg_b)[10]
    _t -= TIME1970
    return _t


@dataclass
class TimeTaken:
    func_name: str = ""
    total_time: int = 0
    api_time: dict = field(default_factory=dict)
    db_time: dict = field(default_factory=dict)


class InvalidFileExtension(Exception):
    def __init__(self, m):
        self.message = m

    def __str__(self):
        return self.message


# Create a custom formatter to format log records as JSON
class CustomJsonFormatter(logging.Formatter):
    def format(self, record):
        return json.dumps(record.__dict__)


def set_json_formatter(logfile: str, backup_count: int = 5) -> TimedRotatingFileHandler:
    file_handler = TimedRotatingFileHandler(logfile, when='W0', backupCount=backup_count, utc=True)
    file_handler.setFormatter(CustomJsonFormatter())
    return file_handler


def default_handler(obj):
    print(obj)
    if isinstance(obj, float) and obj == float('inf'):
        return "0.0"
    raise TypeError(f'Object of type {type(obj)} is not JSON serializable')


class LogRecord(logging.LogRecord):
    db: Session

    def __init__(self, name, level, fn, lno, msg, args, exc_info, func=None, sinfo=None, db=None):
        self.db = db
        super().__init__(name, level, fn, lno, msg, args, exc_info, func, sinfo)


_config = {
    'logname': 'myapp',
    'use_formatter': True,
    'level': logging.DEBUG,
    'propagate': True,
    'dirname': os.getenv('TEMP') if os.name == 'nt' else '/var/log/dashboard',
}


class TimeCodeBlock:
    def __init__(self, name=None):
        self.name = name if name else ''
        self.logger = MyLogger()
        self._start_time = 0
        self._stop_time = 0
        self._timing_stack = deque()

    def __enter__(self):
        self.start_perf_counter_ns = perf_counter_ns()
        self.start_process_time_ns = process_time_ns()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.total_perf_counter_ns = perf_counter_ns() - self.start_perf_counter_ns
        self.total_process_time_ns = process_time_ns() - self.start_process_time_ns
        self.logger.info(f'{self.name:}: {self.total_perf_counter_ns * pow(10, -6):.0f} ms')

    def start(self):
        self._timing_stack.append(perf_counter_ns())

    def stop(self):
        if len(self._timing_stack) > 0:
            timing = self._timing_stack.pop() - perf_counter_ns()


def time_taken(func):
    @wraps(func)
    def timed(*args, **kwargs):
        call_stack = []
        stack = inspect.stack()
        modules = [(index, inspect.getmodule(stack[index][0])) for index in (range(1, len(stack) - 1))]
        for index, module in modules:
            call_stack.append(stack[index][3])

        time_started = perf_counter_ns()
        result = func(*args, **kwargs)
        time_end = perf_counter_ns()
        dict_value = {"duration": (time_end - time_started) * pow(10, -6), 'callstack': '->'.join(call_stack)}
        return result

    return timed


def error_handler_http(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        import inspect
        try:
            # Reference for inspect module
            # https://stackoverflow.com/questions/2654113/how-to-get-the-callers-method-name-in-the-called-method
            call_stack = []
            stack = inspect.stack()
            # modules = [(index, inspect.getmodule(stack[index][0])) for index in reversed(range(1, len(stack)))]
            modules = [(index, inspect.getmodule(stack[index][0])) for index in (range(1, len(stack) - 1))]
            # module_name_lengths = [len(module.__name__) for _, module in modules]
            for index, module in modules:
                call_stack.append(stack[index][3])
                # print(index,  module.__name__, stack[index][3])
            # call_stack.append(func.__name__)
            # print(0, func.__module__, func.__name__)
            # s = '{index:>5} : {module:^%i} : {name}' % (max(module_name_lengths) + 4)

            # callers = ['', s.format(index='level', module='module', name='name'), '-' * 50]

            # for index, module in modules:
            #     callers.append(s.format(index=index,
            #                             module=module.__name__,
            #                             name=stack[index][3]))
            #
            # callers.append(s.format(index=0,
            #                         module=func.__module__,
            #                         name=func.__name__))
            # callers.append('')

            # print('\n'.join(callers))
            print('->'.join(call_stack))

            result = func(*args, **kwargs)
            return result
        except requests.exceptions.ConnectionError as errc:
            print(f'Connection error: {errc}')
            raise errc
        except requests.exceptions.HTTPError as errh:
            print(f'http error: {errh}')
            raise errh
        except requests.exceptions.Timeout as errt:
            print(f'Timeout: {errt}')
            raise errt
        except requests.exceptions.RequestException as err:
            print(f'{err.response.text}')
            raise err

    return wrapper


def lru_cache_with_expiration(ttl_seconds: int, maxsize: int = 1024):
    """
    Source: https://realpython.com/lru-cache-python/#using-lru_cache-to-implement-an-lru-cache-in-python
    :param ttl_seconds: no of seconds after which cache will expire
    :param maxsize: size of cache
    :return: the cached function
    """

    def wrapper_cache(func):
        func = lru_cache(maxsize=maxsize)(func)
        func.lifetime = timedelta(seconds=ttl_seconds)
        func.expiration = datetime.utcnow() + func.lifetime
        # Source: https://stackoverflow.com/a/64111268/16073830
        attributes = WRAPPER_ASSIGNMENTS + ('cache_info', 'cache_clear')

        # pass cache_info to calling function
        @wraps(func, assigned=attributes)
        def wrapped_func_cache(*args, **kwargs):
            # Convert list to tuple as list is unhashable
            args = [tuple(x) if type(x) == list else x for x in args]
            if datetime.utcnow() > func.expiration:
                func.cache_clear()
                func.expiration = datetime.utcnow() + func.lifetime
            return func(*args, **kwargs)

        return wrapped_func_cache

    return wrapper_cache


def dict2values(raw):
    arr = {}

    def extract(raw, arr):
        seqs = tuple, list, set, frozenset
        final = str, int
        if isinstance(raw, dict):
            for key, value in raw.items():
                if key == "changes_":
                    extract(value, arr)
                else:
                    arr[key] = value
        elif isinstance(raw, list):
            for item in raw:
                extract(item, arr)
        return arr

    return extract(raw, arr)


def daterange(start_date, end_date):
    for n in range(int((end_date.date() - start_date.date()).days)):
        yield start_date + timedelta(n)


# source: https://stackoverflow.com/a/40372261/16073830
def custom_round(x, base=5):
    return base * round(float(x) / base)


# if sys.version_info.minor >= 9:
def generate_layer_bullet(layers: list, bullet_no: int) -> tuple[str, dict[Any, list[str]], dict[Any, list[str]]]:
    # Sample
    # lst = ['two', 'three', 'five', 'six']
    # d = {lst[i]: ['' if j != i else 'active' for j in range(4)] for i in range(4)}

    dict_toggle_bullet = {}
    dict_layer = {}
    layer_name = num_to_words[bullet_no]

    for _, y in enumerate(layers):
        dict_toggle_bullet[y] = ["active" if j == y else "" for _, j in enumerate(layers)]
        dict_layer[y] = [f"layer {j} show" if j == y else f"layer {j}" for _, j in enumerate(layers)]
    return layer_name, dict_layer, dict_toggle_bullet


# else:
#     def generate_layer_bullet(layers: list, bullet_no: int) -> Tuple[int, Dict[Any, List[str]], Dict[Any, List[str]]]:
#         # Sample
#         # lst = ['two', 'three', 'five', 'six']
#         # d = {lst[i]: ['' if j != i else 'active' for j in range(4)] for i in range(4)}
#
#         dict_toggle_bullet = {}
#         dict_layer = {}
#         layer_name = num_to_words[bullet_no]
#
#         for _, y in enumerate(layers):
#             dict_toggle_bullet[y] = ["active" if j == y else "" for _, j in enumerate(layers)]
#             dict_layer[y] = [f"layer {j} show" if j == y else f"layer {j}" for _, j in enumerate(layers)]
#         return layer_name, dict_layer, dict_toggle_bullet

num_to_words = ('zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten')


class WriteToExcel:
    def __init__(self, df: pd.DataFrame, filename: str, sheetname: str):
        self.df = df
        self.filename = filename
        self.sheetname = sheetname

    def create(self, idx=False):
        while True:
            try:
                writer = pd.ExcelWriter(self.filename, 'xlsxwriter', date_format='mm/dd/yyyy')
                break
            except PermissionError:
                print(f'Please close the file {self.filename}. Retry in 10 seconds')
                time.sleep(10)

        # if need index row, then set index = True
        self.df.to_excel(writer, index=idx, sheet_name=self.sheetname)
        workbook = writer.book
        worksheet = writer.sheets[self.sheetname]
        wrap_format = workbook.add_format({'text_wrap': True})
        format1 = workbook.add_format({'bg_color': '#B4D7E6', 'font_color': '#9C0006', 'text_wrap': True})

        row_, col_ = self.df.shape
        if idx:
            col_ += 1

        for i, col in enumerate(self.df.columns):
            # Column of type date is giving exception
            if isinstance(col, date):
                column_len = 15
            else:
                column_len = self.df[col].astype(str).str.len().max()
                column_len = max(column_len, len(col)) + 2

            if column_len > 51:
                worksheet.set_column(i, i, 50, wrap_format)
            elif isinstance(col, date):
                worksheet.set_column(i, i, 15)
            else:
                worksheet.set_column(i, i, column_len)

        worksheet.autofilter(0, 0, row_, col_ - 1)
        writer.save()


def parse_tuple(value):
    try:
        _s = ast.literal_eval(str(value))
        if type(_s) == tuple:
            return _s
        return
    except Exception:
        return


def parse_xml_to_json():
    import xml.etree.ElementTree as node
    import xmltodict
    import json
    tree = node.parse("c:/vishal/sample-xml-svn-log/Plat_22.11.xml")
    root = tree.getroot()
    d = {}
    for child in root:
        if child.tag not in d:
            d[child.tag] = []
        dic = {}
        for child2 in child:
            if child2.tag not in dic:
                dic[child2.tag] = child2.text
        d[child.tag].append(dic)

    with open("c:/vishal/sample-xml-svn-log/Plat_22.11.xml") as xml_file:
        data_dict = xmltodict.parse(xml_file.read())

    json_data = json.dumps(data_dict)

    with open("c:/vishal/sample-xml-svn-log/data.json", "w") as json_file:
        json_file.write(json_data)


def sorted_nicely(l):
    """ Sort the given iterable in the way that humans expect."""
    convert = lambda text: int(text) if text.isdigit() else text
    alphanum_key = lambda key: [convert(c) for c in re.split('([0-9]+)', key)]
    return sorted(l, key=alphanum_key, reverse=True)


def sort_col_value(y: list):
    y = sorted(y, key=lambda v: [int(i) for i in v.split('.')])
    return y


class Node:
    def __init__(self, name: str):
        self._marker = name.lower().replace(' ', '_') if name else ''
        self._start_perf = perf_counter_ns()
        self._start_cpu = process_time_ns()
        self._total_perf = 0
        self._total_cpu = 0
        self._variance = 0

    def get_time_perf(self):
        return self._start_perf

    def set_time_perf(self):
        self._start_perf = perf_counter_ns()

    def get_marker(self):
        return self._marker

    def set_marker(self, name: str):
        self._marker = name

    def set_total_time(self):
        # print(f'end_perf: {perf_counter_ns()}')
        end_time_ns = process_time_ns()
        self._total_perf = perf_counter_ns() - self._start_perf
        self._total_cpu = end_time_ns - self._start_cpu
        self._variance = (self._total_perf - self._total_cpu) / self._total_cpu if self._total_cpu != 0 else 'Infinity'

    def get_total_time_perf_ns(self):
        return self._total_perf

    def get_total_time_cpu_ns(self):
        return self._total_cpu

    def get_variance(self):
        return self._variance

    def __str__(self):
        print(self._marker)


class ExecutionTime:
    def __init__(self, func_name: str = __name__, marker: str = ''):
        self._funcname: str = func_name
        self._name: str = marker
        self._timing_stack: deque = deque()
        self._results: deque = deque()

    def __enter__(self):
        self.start(self._name)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()
        return self

    def start(self, marker: str):
        node = Node(marker)
        self._timing_stack.append(node)

    def stop(self):
        if len(self._timing_stack) > 0:
            node = self._timing_stack.pop()
            node.set_total_time()
            self._results.append(node)
        else:
            raise IndexError

    def show_result(self):
        result = {
            node.get_marker(): [
                {'total_time_perf_ms': round(node.get_total_time_perf_ns() * pow(10, -6), 3)},
                {'total_time_cpu_ms': round(node.get_total_time_cpu_ns() * pow(10, -6), 3)},
                {'variance': node.get_variance()}
            ]
            for node in reversed(self._results)
        }
        return {self._funcname: result}


def get_random_string(length):
    # choose from all lowercase letter
    letters = string.ascii_uppercase + string.digits
    result_str = ''.join(random.choice(letters) for i in range(length))
    return result_str


import os
import base64
import pyotp
import qrcode

def token_2fa(email_id: str) -> tuple[str, dict]:
    my_logger = MyLogger().get_logger()
    my_logger.info(f'CWD = {os.getcwd()}')

    # Generate new secret
    secret_key = pyotp.random_base32()
    totp_auth = pyotp.TOTP(secret_key)
    my_logger.info(f'secret key = {secret_key}')

    # Build provisioning URI
    totp_auth_uri = totp_auth.provisioning_uri(name=email_id, issuer_name='CoreCARD')
    my_logger.info(f'uri = {totp_auth_uri}')

    # Build path safely (always relative to current script folder)
    base_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(base_dir, "qr_code", "qr_auth.png")
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Generate QR code from dynamic URI (not hardcoded)
    qrcode.make(totp_auth_uri).save(file_path)

    # Encode for Microsoft Graph
    with open(file_path, "rb") as upload:
        media_content = base64.b64encode(upload.read())

    data_body = {
        '@odata.type': '#microsoft.graph.fileAttachment',
        'contentBytes': media_content.decode('utf-8'),
        'name': os.path.basename(file_path)
    }

    return secret_key, data_body



def verify_token(
        secret_key: str, code: str,
        valid_window: int = 1, ntp_server: str = "time.google.com"
) -> bool:
    """
        Verify a TOTP code against system time first, then retry with NTP time if needed.

        Args:
            secret_key (str): Base32 secret used for TOTP.
            code (str): Code provided by user.
            valid_window (int): Grace window for time drift (in steps of 30s).
            ntp_server (str): NTP server hostname.

        Returns:
            bool: True if valid, False otherwise.
    """
    import ntplib
    my_logger = MyLogger().get_logger()
    totp_auth = pyotp.totp.TOTP(secret_key)
    # 1. Try verification with system time
    if totp_auth.verify(code, valid_window=valid_window):
        my_logger.debug(
            f"[SYSTEM TIME] Code={code}, expected={totp_auth.now()}, result=True"
        )
        return True

    # 2. If system time failed, try with NTP
    try:
        ntp_client = ntplib.NTPClient()
        ntp_response = ntp_client.request(ntp_server, version=3)
        ntp_time = ntp_response.tx_time
        ntp_code = totp_auth.at(ntp_time)

        if code == ntp_code:
            my_logger.debug(
                f"[NTP TIME] Code={code}, expected={ntp_code}, result=True"
            )
            return True

        # Optionally check ±valid_window steps manually
        for offset in range(1, valid_window + 1):
            if (totp_auth.at(ntp_time - offset * 30) == code or
                    totp_auth.at(ntp_time + offset * 30) == code):
                my_logger.debug(
                    f"[NTP TIME with window={valid_window}] "
                    f"Code={code}, result=True"
                )
                return True

        my_logger.debug(
            f"[NTP TIME] Code={code}, expected={ntp_code}, result=False"
        )
        return False
    except Exception as e:
        my_logger.error(f"NTP lookup failed: {e}. Verification already failed with system time.")
        return False


def hash_data(data):
    return hashlib.sha256(str(data).encode()).hexdigest()


def generate_html_header_div(project_name, item_name, href):
    return html.Li(children=[
        html.A(children=[html.Span(children=[item_name])], href=href),
    ], className="sub-menu-lists")


def generate_header_leaf_node(menu_heading: str, menu_link: str) -> html.Li:
    return html.Li(children=[html.A(menu_heading, href=menu_link)])


def generate_layout(project_list):
    top_level_links = [
        {'name': value, "href": f"/" if value == 'Home' else f"/{value.lower()}"}
        for value in ['Home'] + [x.upper() for x in project_list] + ['PMO', 'Playground']
    ]

    top_level_li_children = [
        html.Li(children=[html.A(children=[html.Span(children=[link["name"]])],
                                 href=link["href"]
                                 ),
                          ], className="top-level-link") for link in top_level_links
    ]

    print(top_level_li_children)
    menu_mapper = {
        "By sprint": "overview", "By release": "version", "SVN Details": "/"
    }

    sub_menu_cols = [
        "Dashboard", "Time sheets", "Project Specific", "Metrics", "Reports", "Batch Job", "Misc.",
    ]
    sub_menu_map = {
        "Dashboard": ["By sprint", "By Release", "SVN Details"]
    }

    sub_menu_links = [
        {"name": key, "href": f"/{{}}/{value}"} for key, value in menu_mapper.items()
    ]

    sub_menu_links = [
        {"name": "By sprint", "href": "/{}/overview"},
        {"name": "By release", "href": "/{}/version"},
        {"name": "SVN Details", "href": "/{}/svn"}, {"name": "Month Wise", "href": "/{}/monthlyts"},
        {"name": "By Resource", "href": "/{}/resourcets"},
        {"name": "Backlog Refinement", "href": "/{}/refinement"},
        {"name": "SQL Code Review", "href": "/{}/SQL Code Review"},
        {"name": "Release Notes", "href": "/{}/releasenotes"},
        {"name": "Description", "href": "/{}/description"}, {"name": "Aging", "href": "/{}/issueaging"},
        {"name": "Compliance", "href": "/{}/compliance"},
        {"name": "Unassigned", "href": "/{}/unassigned"},
        {"name": "Update Estimates", "href": "/{}/estimates"},
        {"name": "Transition Issues", "href": "/{}/transition"},
        {"name": "Create CTs", "href": "/{}/coretrack"}, {"name": "SVN Info", "href": "/{}/svndetails"},
        {"name": "Duplicate JIRA", "href": "/{}/duplicate"},
        {"name": "Initiative Attributes", "href": "/{}/initattr"},
        {"name": "Update Initiative Attributes", "href": "/{}/updateinitattribs"},
        {"name": "Update Teams", "href": "/{}/updateteams"}, ]


# Function to get the current memory usage in MB
def get_memory_usage():
    process = psutil.Process()
    mem_info = process.memory_info()
    return mem_info.rss / (1024 ** 2)


# Function to get the current CPU usage as a percentage
def get_cpu_usage():
    return psutil.cpu_percent(interval=1)


# def get_flask_cache_keys():
#     k_prefix = dash_app.cache.cache.key_prefix
#     print(f'key prefix = {k_prefix}')
#     keys = dash_app.cache.cache._write_client.keys(k_prefix + '*')
#     print(f'keys in cache = {keys}')
#     keys = [k.decode('utf8') for k in keys]
#     keys = [k.replace(k_prefix, '') for k in keys]
#     for key in keys:
#         values = dash_app.cache.get_many(key)
#         print(f'Key = {key}')
#
#         for value in values:
#             if isinstance(value, pd.DataFrame):
#                 print(f'shape of df = {value.shape}')
#             elif isinstance(value, str):
#                 print(value)
#             else:
#                 print(type(value))

class ConfigContainer(containers.DeclarativeContainer):
    config = providers.Configuration()


def get_error_message(status_code):
    status_code_descriptions = {
        400: "400 Bad Request: The server could not understand the request due to invalid syntax.",
        401: "401 Unauthorized: The request has not been applied because it lacks valid authentication credentials for the target resource.",
        403: "403 Forbidden: The server understood the request, but refuses to authorize it.",
        404: "404 Not Found: The server can not find the requested resource.",
        500: "500 Internal Server Error: The server has encountered a situation it doesn't know how to handle.",
        502: "502 Bad Gateway: The server was acting as a gateway or proxy and received an invalid response from the upstream server.",
        503: "503 Service Unavailable: The server is not ready to handle the request.",
        504: "504 Gateway Timeout: The server was acting as a gateway or proxy and did not receive a timely response from the upstream server."
    }
    return status_code_descriptions.get(status_code, f"Unexpected status code: {status_code}")


if __name__ == '__main__':
    start_date_ = datetime.fromtimestamp(1629132894.926)
    generate_layer_bullet(['two', 'three', 'five', 'six'], 2)
    token_2fa('<EMAIL>')


def make_progress_graph(progress, total):
    progress_graph = (
        go.Figure(data=[go.Bar(x=[progress])])
        .update_xaxes(range=[0, total])
        .update_yaxes(
            showticklabels=False,
        ).update_layout(height=65, margin=dict(t=20, b=40))
    )
    return progress_graph


def canonical_form(value) -> tuple:
    """
        Returns a canonical form of the given list of values, sorted in a way that
        makes sense for mixed-type values.
        """

    # Define a dictionary that maps the specific strings to their priority
    priority_map = {
        'Plat': 1,
        'CP_Plat_CI': 2,
        'Plat-Jazz': 3,
        'Plat_NEST': 4,
        'Plat Stress Test': 5,
        'Plat-Lotus': 6,
        'Plat-CODA': 7,
        'Plat Managed Services': 8,
        'Plat Production Support': 9,
        'Plat-NBL': 10,
        'CoreCardIndia': 20,
        'Posting 2.0': 21,
        'plat': 30,
    }
    # If the value is in the priority map, return its priority
    if value in priority_map:
        return 0, priority_map[value]
    else:
        return 1, value


class UserAuth(UserMixin):
    def __init__(
            self, username: str, displayName: str,
            is_admin=False, is_pmo=False, is_superuser=False
    ):
        super().__init__()
        self.id = username
        # self.email_address = username
        # self.emailAddress = username
        self.displayName = displayName
        self.superuser = username == '<EMAIL>'
        self.pmo = username in ['<EMAIL>', '<EMAIL>']
        self.admin = is_admin

    @property
    def is_superuser(self):
        return self.superuser

    @property
    def is_pmo(self):
        return self.pmo

    @property
    def is_admin(self):
        return self.admin

    def get_secret_key(self):
        secret_key = pyotp.random_base32()
        totp_auth = pyotp.totp.TOTP(secret_key)

    def get_totp_uri(self):
        totp_auth_uri = pyotp.totp_auth.provisioning_uri(name=self.emailAddress, issuer_name='CoreCARD')
        return 'otpauth://totp/2FA-Demo:{0}?secret={1}&issuer=2FA-Demo' \
            .format(self.username, self.otp_secret)

    def verify_totp(self, token) -> bool:
        totp_auth = pyotp.totp.TOTP(self.otp_secret)
        return totp_auth.verify(token)


def clicked(ctx: callback_context):
    if not ctx.triggered or not ctx.triggered[0]['value']:
        return None
    else:
        return ctx.triggered[0]['prop_id'].split('.')[0]


def apply_href(x: str) -> object:
    """
    Takes in JIRA Issue Key. Removes the project key and adds a hyperlink
    Args:
        x: JIRA Issue Key

    Returns:
        Empty html Div Tag or html Div tag with formatted issuekey

    """
    if x is None or x == '':
        return html.Div()
    elif x == 'Not Found':
        return html.Div('Not Found')
    else:
        part = x.split('-')
        return html.A(part[1], href=f'https://corecard.atlassian.net/browse/{x}', target='_blank')


def apply_td(x):
    return html.Td(x)


def sort_priority(column):
    reorder = ["Show Stopper", "Critical", "High", "Medium", "Low"]
    cat = pd.Categorical(column, categories=reorder, ordered=True)
    return pd.Series(cat)


def apply_acronymn(x):
    return "".join([e[0] for e in x.split()])


def apply_list_to_newline(x):
    return "\n ".join(x if x is not None else "")


def apply_dbc_progress(x, y, z):
    total = x + y + z
    if total == 0:
        return dbc.Progress(value=100, label="None", color='info')
    else:
        return dbc.Progress(
            children=[
                dbc.Progress(value=x * 100 / total, label=x, color="danger", bar=True),
                dbc.Progress(value=y * 100 / total, label=y, color="warning", bar=True),
                dbc.Progress(value=z * 100 / total, label=z, color="success", bar=True),
            ]
        )


def apply_list_to_string(x):
    return ", ".join(x if x is not None else "")
