from sqlalchemy.orm import sessionmaker
import pytest
from dependency_injector import containers, providers

from _pytest.fixtures import FixtureRequest as Request

from custom_container import AppContainer
from data import get_from_db as db
from data import helper
from dbmodels.user import User


class DummyAppContainer(containers.DeclarativeContainer):
    app_container = providers.Container(AppContainer)


@pytest.fixture(scope='session')
def app_container():
    container = DummyAppContainer()
    container.app_container.override(AppContainer)
    container.wire(modules=["callbacks"])
    yield container.app_container()
    container.unwire()


@pytest.fixture
def db_connections(app_container):
    return app_container.session_factory().db_connections()


@pytest.fixture
def db_ro_schemas(app_container):
    return app_container.session_factory().db_connections()['ro']()()


@pytest.fixture
def db_rw_schemas(app_container):
    return app_container.session_factory().db_connections()['rw']()()


def test_db_container_connection(db_connections, db_ro_schemas):
    # Use the injected db_connections and db_ro_schemas
    ro_schemas = db_connections['ro']()()
    assert 'plat' in ro_schemas.keys()

    assert 'plp' not in db_ro_schemas.keys()
    assert 'public' in db_ro_schemas.keys()


#
# @pytest.fixture(scope='function')
# def session(request):
#     if isinstance(request, Request):
#         schema_name = request.getfixturevalue('schema_name')
#     else:
#         schema_name = request.param if request.param else 'public'
#
#     connection_string = custom_container.DbConnectionURI()
#     db_conn = custom_container.DatabaseContainer.db_ro[schema_name]
#     db_conn = db.PgDatabase(connection_string.pg_db_url, *schema_name)
#
#     maker = sessionmaker(bind=db_conn.get_engine(), expire_on_commit=False)
#     session = maker()
#     try:
#         yield session
#     finally:
#         session.rollback()
#         session.close()
#
#
# @pytest.mark.parametrize('session', [('public',)])
# def test_get_user_otp_token(session, db_ro_schemas):
#     session_public = db.start_session('public')
#     res = db.get_user_otp_token('<EMAIL>')
#     assert res is not None

@pytest.mark.parametrize('schema_name', ['plat'])
def test_get_all_active_versions(schema_name: str, db_ro_schemas):
    with db_ro_schemas[schema_name].session() as pg_session:
        res = db.get_all_active_versions(pg_session)
        assert len(res) > 0


# @pytest.mark.parametrize('schema_name', [('public',)])
# def test_user(schema_name: str, session):
#     rows = session.query(User).all()
#     assert len(rows) > 1
