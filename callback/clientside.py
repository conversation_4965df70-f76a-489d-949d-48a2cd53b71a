from dash import clientside_callback, ClientsideFunction, Output, Input, State

# Modern Login - Password Toggle
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        const passwordInput = document.getElementById('id-login-passwd-main');
        const toggleButton = document.querySelector('.password-toggle');
        const icon = toggleButton.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fa fa-eye-slash';
            toggleButton.setAttribute('aria-label', 'Hide password');
            toggleButton.title = 'Hide password';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fa fa-eye';
            toggleButton.setAttribute('aria-label', 'Show password');
            toggleButton.title = 'Show password';
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output('id-login-passwd-main', 'type'),
    Input({'type': 'password-toggle', 'index': 'main'}, 'n_clicks'),
    prevent_initial_call=True
)

# Add login-page class to body when on login pages
clientside_callback(
    """
    function(pathname) {
        // Check if the current page is a login page
        const isLoginPage = pathname && (pathname.includes('login') || pathname.includes('logout'));

        // Add or remove the login-page class from the body
        if (isLoginPage) {
            document.body.classList.add('login-page');
        } else {
            document.body.classList.remove('login-page');
        }

        // Return pathname unchanged (this output is just a placeholder)
        return pathname;
    }
    """,
    Output("url_login", "pathname", allow_duplicate=True),
    Input("url_login", "pathname"),
    prevent_initial_call=True
)

# 2FA OTP Input Formatting
clientside_callback(
    """
    function(value) {
        if (!value) return '';

        // Remove non-digits and limit to 6 characters
        const digits = value.replace(/\\D/g, '').substring(0, 6);

        // Update input styling based on length
        const input = document.getElementById('id-input-token');
        if (input) {
            input.classList.remove('success', 'error');

            if (digits.length === 6) {
                input.classList.add('success');
            }
        }

        return digits;
    }
    """,
    Output('id-input-token', 'value'),
    Input('id-input-token', 'value'),
    prevent_initial_call=True
)

# 2FA Form Validation
clientside_callback(
    """
    function(n_clicks, otp_value) {
        if (!n_clicks) return '';

        const input = document.getElementById('id-input-token');

        if (!otp_value || otp_value.trim() === '') {
            if (input) input.classList.add('error');
            return 'Please enter the 6-digit authentication code';
        }

        if (!/^\\d{6}$/.test(otp_value.trim())) {
            if (input) input.classList.add('error');
            return 'Authentication code must be exactly 6 digits';
        }

        if (input) {
            input.classList.remove('error');
            input.classList.add('success');
        }

        return '';
    }
    """,
    Output('id-validate-otp-token', 'children'),
    Input('id-button-token', 'n_clicks'),
    State('id-input-token', 'value'),
    prevent_initial_call=True
)

# Version Page
clientside_callback(
    ClientsideFunction(
        namespace='clientside',
        function_name='filter_table'
    ),
    Output("id-version-bugs-table", "className"),
    Input("id-version-bugs-table", "id"),
)

# Source:  https://codepen.io/geoffgraham/pen/yLywVbW
clientside_callback(
    """   

    function(value) {
    const FULL_DASH_ARRAY = 283;
    const WARNING_THRESHOLD = 90;
    const ALERT_THRESHOLD = 30;

    const COLOR_CODES = {
      info: {
        color: "green"
      },
      warning: {
        color: "orange",
        threshold: WARNING_THRESHOLD
      },
      alert: {
        color: "red",
        threshold: ALERT_THRESHOLD
      }
    };

        const TIME_LIMIT = 180;

        let remainingPathColor = COLOR_CODES.info.color;;
        console.log(remainingPathColor);   
        document.getElementById(value).innerHTML = `
        <div class="base-timer">
          <svg class="base-timer__svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <g class="base-timer__circle">
              <circle class="base-timer__path-elapsed" cx="50" cy="50" r="45" />
              <path
                id="base-timer-path-remaining"
                stroke-dasharray="283"
                class="base-timer__path-remaining ${remainingPathColor}"
                d="
                  M 50, 50
                  m -45, 0
                  a 45,45 0 1,0 90,0
                  a 45,45 0 1,0 -90,0
                "
              ></path>
            </g>
          </svg>
          <span id="base-timer-label" class="base-timer__label">
            ${formatTimeLeft(TIME_LIMIT)}
          </span>
        </div>
        `;
        startTimer();
    };

    function onTimesUp() {
     clearInterval(timerInterval);
    }

    function formatTimeLeft(time) {
        const minutes = Math.floor(time / 60);
        let seconds = time % 60;
        if (seconds < 10) {
            seconds = `0${seconds}`;
        }
        return `${minutes}:${seconds}`;
    };

    function startTimer() {
        let timePassed = 0;
        const TIME_LIMIT = 180;
        timerInterval = setInterval(() => {
            // The amount of time passed increments by one
            timePassed = timePassed += 1;
            timeLeft = TIME_LIMIT - timePassed;

            // The time left label is updated
            document.getElementById("base-timer-label").innerHTML = formatTimeLeft(timeLeft);
            setCircleDasharray();
            setRemainingPathColor(timeLeft);
            if (timeLeft === 0) {
             onTimesUp();
            }
        }, 1000);
    };


    // Divides time left by the defined time limit.
    function calculateTimeFraction() {
      const TIME_LIMIT = 180;
      const rawTimeFraction = timeLeft / TIME_LIMIT;
      return rawTimeFraction - (1 / TIME_LIMIT) * (1 - rawTimeFraction);
    };

    // Update the dasharray value as time passes, starting with 283
    function setCircleDasharray() {
    const FULL_DASH_ARRAY = 283;
      const circleDasharray = `${(
        calculateTimeFraction() * FULL_DASH_ARRAY
      ).toFixed(0)} 283`;
      document
        .getElementById("base-timer-path-remaining")
        .setAttribute("stroke-dasharray", circleDasharray);
    };

    function setRemainingPathColor(timeLeft) {
    const WARNING_THRESHOLD = 90;
    const ALERT_THRESHOLD = 30;
    const COLOR_CODES = {
      info: {
        color: "green"
      },
      warning: {
        color: "orange",
        threshold: WARNING_THRESHOLD
      },
      alert: {
        color: "red",
        threshold: ALERT_THRESHOLD
      }
    };
  const { alert, warning, info } = COLOR_CODES;
  if (timeLeft <= alert.threshold) {
    document
      .getElementById("base-timer-path-remaining")
      .classList.remove(warning.color);
    document
      .getElementById("base-timer-path-remaining")
      .classList.add(alert.color);
  } else if (timeLeft <= warning.threshold) {
    document
      .getElementById("base-timer-path-remaining")
      .classList.remove(info.color);
    document
      .getElementById("base-timer-path-remaining")
      .classList.add(warning.color);
  }
}

    """,
    Output("in-component1", "value"),
    Input('dash_app', 'id')
)

# Home page
clientside_callback(
    """
    function(name) {
        const now = new Date();
        const hour = now.getHours();
        let greeting;
        elem = document.getElementById(name).innerHTML
        console.log("Javascript invoked");
        console.log(elem);
        if (navigator.languages && navigator.languages.length) {
            console.log(navigator.languages[0])
            switch (navigator.languages[0]) {
                case 'en-IN':
                    if (hour < 4) {
                      greeting = "शुभ बिहान!";
                    } else if (hour < 12) {
                      greeting = "शुभ प्रभात!";
                    } else if (hour < 16) {
                      greeting = "शुभ दोपहर!";
                    } else if (hour < 20) {
                      greeting = "शुभ संध्या!";
                    } else {
                      greeting = "शुभ रात्रि!";
                    }               
                    break;
                default:
                    if (hour < 12) {
                        greeting = "Good morning!";
                    } else if (hour < 18) {
                      greeting = "Good afternoon!";
                    } else {
                      greeting = "Good evening!";
                    } 
            }
            console.log(greeting)
            return greeting + ' ' + elem
        } else {            
            return (navigator.userLanguage || navigator.language || navigator.browserLanguage || 'en');
        }
    }
    """,
    Output("id-welcome-user", "children"),
    Input("id-welcome-user", "id")
)

# Boot Screen Management
clientside_callback(
    """
    function(pathname) {
        // Only run on login pages
        if (!pathname || (!pathname.includes('login') && !pathname.includes('logout'))) {
            return window.dash_clientside.no_update;
        }

        const bootScreen = document.getElementById('boot-screen');
        const loginContainer = document.getElementById('login-form-container');
        const login2faContainer = document.getElementById('login-2fa-container');

        if (!bootScreen) {
            return window.dash_clientside.no_update;
        }

        // Show boot screen immediately
        bootScreen.classList.remove('boot-screen-hidden');

        // Hide appropriate login container
        if (loginContainer) {
            loginContainer.classList.add('login-form-hidden');
            loginContainer.classList.remove('login-form-visible');
        }
        if (login2faContainer) {
            login2faContainer.classList.add('login-form-hidden');
            login2faContainer.classList.remove('login-form-visible');
        }

        // Simulate loading time and hide boot screen after resources are loaded
        setTimeout(() => {
            // Check if all critical resources are loaded
            const checkResourcesLoaded = () => {
                const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
                const scripts = document.querySelectorAll('script[src]');
                let loadedCount = 0;
                let totalCount = stylesheets.length + scripts.length;

                // Check stylesheets
                stylesheets.forEach(link => {
                    if (link.sheet || link.readyState === 'complete') {
                        loadedCount++;
                    }
                });

                // Check scripts
                scripts.forEach(script => {
                    if (script.readyState === 'complete' || script.readyState === 'loaded') {
                        loadedCount++;
                    }
                });

                return loadedCount >= totalCount * 0.8; // 80% loaded is good enough
            };

            const hideBootScreen = () => {
                bootScreen.classList.add('boot-screen-hidden');

                // Show appropriate login form after boot screen fades out
                setTimeout(() => {
                    const currentContainer = loginContainer || login2faContainer;
                    if (currentContainer) {
                        currentContainer.classList.remove('login-form-hidden');
                        currentContainer.classList.add('login-form-visible');

                        // Add entrance animation to form elements
                        const formElements = currentContainer.querySelectorAll('.modern-input-group, .modern-login-button, .modern-2fa-input-group, .modern-2fa-buttons');
                        formElements.forEach((element, index) => {
                            element.style.opacity = '0';
                            element.style.transform = 'translateY(20px)';

                            setTimeout(() => {
                                element.style.transition = 'all 0.4s ease-out';
                                element.style.opacity = '1';
                                element.style.transform = 'translateY(0)';
                            }, index * 100);
                        });
                    }
                }, 500);
            };

            // Check if resources are loaded, otherwise wait a bit more
            if (checkResourcesLoaded()) {
                hideBootScreen();
            } else {
                // Wait a bit more for resources to load
                setTimeout(() => {
                    hideBootScreen();
                }, 1000);
            }
        }, 2000); // Minimum 2 seconds display time

        return window.dash_clientside.no_update;
    }
    """,
    Output("url_login", "search", allow_duplicate=True),
    Input("url_login", "pathname"),
    prevent_initial_call=True
)

# Boot Screen Management for 2FA page
clientside_callback(
    """
    function(pathname) {
        // Only run on 2FA login page
        if (!pathname || !pathname.includes('2fa')) {
            return window.dash_clientside.no_update;
        }

        const bootScreen = document.getElementById('boot-screen');
        const login2faContainer = document.getElementById('login-2fa-container');

        if (!bootScreen || !login2faContainer) {
            return window.dash_clientside.no_update;
        }

        // Show boot screen immediately
        bootScreen.classList.remove('boot-screen-hidden');
        login2faContainer.classList.add('login-form-hidden');
        login2faContainer.classList.remove('login-form-visible');

        // Hide boot screen after loading
        setTimeout(() => {
            bootScreen.classList.add('boot-screen-hidden');

            // Show 2FA form after boot screen fades out
            setTimeout(() => {
                login2faContainer.classList.remove('login-form-hidden');
                login2faContainer.classList.add('login-form-visible');

                // Add entrance animation to form elements
                const formElements = login2faContainer.querySelectorAll('.modern-2fa-input-group, .modern-2fa-buttons');
                formElements.forEach((element, index) => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        element.style.transition = 'all 0.4s ease-out';
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 500);
        }, 2000);

        return window.dash_clientside.no_update;
    }
    """,
    Output("url_login_2fa", "search", allow_duplicate=True),
    Input("url_login_2fa", "pathname"),
    prevent_initial_call=True
)

# Modern Logout Menu Toggle
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');

        if (!trigger || !dropdown) return window.dash_clientside.no_update;

        const isOpen = dropdown.classList.contains('show');

        if (isOpen) {
            trigger.classList.remove('active');
            dropdown.classList.remove('show');
        } else {
            trigger.classList.add('active');
            dropdown.classList.add('show');

            // Focus first menu item for accessibility
            setTimeout(() => {
                const firstItem = dropdown.querySelector('.dropdown-item');
                if (firstItem) firstItem.focus();
            }, 100);
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("user-menu-trigger", "className", allow_duplicate=True),
    Input("user-menu-trigger", "n_clicks"),
    prevent_initial_call=True
)

# Logout Button Handler
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        // Use the modern logout confirmation
        if (window.modernLogout) {
            window.modernLogout.confirmLogout();
        } else {
            // Fallback to direct logout
            const basePath = window.dashBasePath || '/';
            window.location.href = basePath + 'logout';
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("logout-button", "className", allow_duplicate=True),
    Input("logout-button", "n_clicks"),
    prevent_initial_call=True
)

# Close dropdown when clicking outside
clientside_callback(
    """
    function(pathname) {
        // Close any open dropdowns when navigating
        const dropdown = document.getElementById('user-dropdown-menu');
        const trigger = document.getElementById('user-menu-trigger');

        if (dropdown && trigger) {
            dropdown.classList.remove('show');
            trigger.classList.remove('active');
        }

        // Close mobile menu when navigating
        if (window.modernHeader) {
            window.modernHeader.closeMobileMenu();
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("user-dropdown-menu", "className", allow_duplicate=True),
    Input("url", "pathname"),
    prevent_initial_call=True
)

# Mobile Menu Toggle
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        if (window.modernHeader) {
            window.modernHeader.toggleMobileMenu();
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("mobile-menu-toggle", "className", allow_duplicate=True),
    Input("mobile-menu-toggle", "n_clicks"),
    prevent_initial_call=True
)

# Notifications Button Handler
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        if (window.modernHeader) {
            window.modernHeader.showNotificationPanel();
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("notifications-btn", "className", allow_duplicate=True),
    Input("notifications-btn", "n_clicks"),
    prevent_initial_call=True
)

# Modern Timesheet Sidebar Toggle - Fixed to prevent infinite loop
clientside_callback(
    """
    function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;

        // Prevent multiple rapid clicks
        const button = document.querySelector('[id*="toggle-panel"][id*="isctimesheet"]');
        if (button && button.dataset.processing === 'true') {
            return window.dash_clientside.no_update;
        }

        if (button) {
            button.dataset.processing = 'true';
            setTimeout(() => {
                button.dataset.processing = 'false';
            }, 300);
        }

        if (window.modernTimesheet) {
            window.modernTimesheet.toggleSidebar();
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output({"type": "toggle-panel", "index": "isctimesheet"}, "style", allow_duplicate=True),
    Input({"type": "toggle-panel", "index": "isctimesheet"}, "n_clicks"),
    prevent_initial_call=True
)

# Modern Timesheet Tab Switching
clientside_callback(
    """
    function(n_clicks_2, n_clicks_3, n_clicks_5) {
        const ctx = window.dash_clientside.callback_context;
        if (!ctx.triggered.length) return window.dash_clientside.no_update;

        const triggerId = ctx.triggered[0].prop_id.split('.')[0];

        if (window.modernTimesheet) {
            const layerId = triggerId.replace('bullet', 'layer');
            window.modernTimesheet.switchTab(layerId);
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("id-isc-ts-bullet-2", "className", allow_duplicate=True),
    Input("id-isc-ts-bullet-2", "n_clicks"),
    Input("id-isc-ts-bullet-3", "n_clicks"),
    Input("id-isc-ts-bullet-5", "n_clicks"),
    prevent_initial_call=True
)

# Modern Timesheet File Upload Handler
clientside_callback(
    """
    function(contents, filename) {
        if (window.modernTimesheet) {
            window.modernTimesheet.handleFileUpload(contents ? [filename] : null);
        }

        return window.dash_clientside.no_update;
    }
    """,
    Output("id-gs-jira-upload", "className", allow_duplicate=True),
    Input("id-gs-jira-upload", "contents"),
    State("id-gs-jira-upload", "filename"),
    prevent_initial_call=True
)
