from datetime import datetime
from io import StringIO

import pandas as pd
from dash import Output, Input, callback, State, callback_context
from dash.exceptions import PreventUpdate
from dependency_injector.wiring import Provide, inject
from psycopg2 import ProgrammingError

from callbacks import parse_contents
from data.helper import clicked
from custom_container import IKeyPassPasswordManager, AppContainer, MyKeePassContainer
from data import MyLogger
import data.get_from_db as db


@callback(
    Output("id-gs-jira-details", "data"),
    Output("id-run-gs-jira", "disabled"),
    Input("id-gs-jira-upload", "contents"),
    State("id-gs-jira-upload", "filename"),
    State("id-gs-jira-upload", "last_modified"),
)
def process_file(contents: str, filename: str, last_modified: str):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    _, df_json = parse_contents(contents, filename, last_modified)
    return df_json, False


@callback(
    output=[
        Output("id-gs-jira-msg", "children"),
        Output("id-gs-jira-msg", "color"),
    ],
    inputs=Input("id-run-gs-jira", "n_clicks"),
    state=[
        State("id-gs-jira-details", "data"),
    ],
    background=True,
    running=[
        (Output("id-run-gs-jira", "disabled"), True, False),
        (Output("id-cancel-gs-jira", "disabled"), False, True),
    ],
    cancel=Input("id-cancel-gs-jira", "n_clicks"),
    prevent_initial_call=True
)
@inject
def process_gs_jira(
        n_click, df_json,
        password_manager: IKeyPassPasswordManager = Provide[MyKeePassContainer],
        session_factory: AppContainer = Provide[AppContainer.session_factory],
):
    my_logger = MyLogger().get_logger()

    if n_click > 0:
        df = pd.read_json(StringIO(df_json), orient='split')
        my_logger.debug(f'No of records: {df.shape}')
        my_logger.info(f"is injection working = {type(password_manager)}")
        df['created'] = df['created'].apply(lambda x: datetime.fromtimestamp(x / 1000))
        df['updated'] = df['updated'].apply(lambda x: datetime.fromtimestamp(x / 1000))
        df.drop_duplicates(keep='first', inplace=True)
        df['effort_estimates'] = pd.to_numeric(df['effort_estimates'], errors='coerce', downcast='float').fillna(0)

        session_factory.config.override({'ro_or_rw': 'rw'})
        session_factory.config_schema.override({'schema_name': 'plat'})
        with session_factory.db_conn_provider().session() as pg_session:
            try:
                db.update_gs_jira(df, pg_session)
                my_logger.info("upsert completed successfully")
            except ProgrammingError as e:
                my_logger.exception(f"Exception encountered {e}")
                return str(e.args[0]), "danger"
        return f"Updated {df.shape[0]} records!!!", "success"