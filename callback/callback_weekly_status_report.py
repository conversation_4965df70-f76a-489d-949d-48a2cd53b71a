import os
import uuid
from collections import namedtuple
from datetime import date

import pandas as pd
from dash import callback, Output, Input, State, callback_context, dcc, html, ALL, Patch, MATCH
from dash.exceptions import PreventUpdate
from dateutil.relativedelta import relativedelta
from dependency_injector import containers
from dependency_injector.wiring import inject, Provide

import custom_container

from data import get_from_db as db, MyLogger
from data.helper import make_progress_graph, clicked
from data.report import CreateReport, get_data


@callback(
    Output("id-status-report-div", "children"),
    Input("add-filter", "n_clicks"),
    Input("remove-filter", "n_clicks"),
    Input("url", "pathname"),
    State("id-status-report-div", "children"),
)
@inject
def add_release_version(
        add, remove, pathname: str, children,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    my_logger = MyLogger().get_logger()
    my_logger.debug(f"add = {add}, remove = {remove}, pathname = {pathname}, children = {children}")

    # if isinstance(session_factory, containers.DynamicContainer):
    #     my_logger.debug("session_factory is dynamic container")
    #     pg_session_generator = session_factory.db_ro().session()
    # else:
    #     my_logger.debug("Session not inserted dynamically. Constructing it manually")
    #     pg_session_generator = db.Database(db.DbConnectionURI(kpk_inst, read_only=True)).session()
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        # updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
        # pg_session.bind = updated_engine
        result = db.get_all_active_versions(pg_session)
        branch_list = db.get_branch_details(pg_session)
        my_logger.info(f"versions returned {len(result)}")
    options = [dict(label=name, value=name) for name in result]
    branch_options = [dict(label=item, value=item) for item in branch_list]

    new_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-drop-down', 'index': str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '300px'}
    )
    branch_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-branch-drop-down', 'index': str(add - remove)},
        options=branch_options, className="dropdown_custom", multi=True, style={'width': '150px'}
    )

    new_pattern_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-drop-down-pattern', 'index': str(add - remove)},
        style={'width': '200px'},
    )
    new_header_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-header', 'index': str(add - remove)}, style={'width': '250px'}
    )
    new_search_button = html.Button(id={'type': 'status-rpt-search', 'index': str(add - remove)},
                                    className="format-serach-button")
    my_logger.debug("Created required elements for Cookie")
    if click_event is None or click_event == 'add-filter':
        children.append(
            html.Div(children=[
                html.Div(children=[new_pattern_text_box, new_search_button], className="col"),
                html.Div(children=[new_dropdown], className="col"),
                html.Div(children=branch_dropdown, className="col"),
                html.Div(children=new_header_text_box, className="col")
            ], className="row ms-1 mb-1")
        )
    elif click_event == 'remove-filter':
        if len(children) > 1:
            children.pop()

    return children


@callback(
    Output("id-jazz-status-report-div", "children"),
    Input("jazz-add-filter", "n_clicks"),
    Input("jazz-remove-filter", "n_clicks"),
    Input("url", "pathname"),
    State("id-jazz-status-report-div", "children"),
)
@inject
def add_release_version_jazz(
        add, remove, pathname: str, children,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory]
):
    click_event = clicked(callback_context)
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})
    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        result = db.get_all_active_versions(pg_session)
    options = [dict(label=name, value=name) for name in result]
    new_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-drop-down', 'index': 'jazz' + str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '300px'}
    )

    branch_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-branch-drop-down', 'index': 'jazz' + str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '150px'}
    )

    new_pattern_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-drop-down-pattern', 'index': 'jazz' + str(add - remove)},
        style={'width': '200px'},
    )
    new_header_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-header', 'index': 'jazz' + str(add - remove)}, style={'width': '250px'}
    )
    new_search_button = html.Button(id={'type': 'status-rpt-search', 'index': 'jazz' + str(add - remove)},
                                    className="format-serach-button")

    if click_event is None or click_event == 'jazz-add-filter':
        children.append(
            html.Div(children=[
                html.Div(children=[new_pattern_text_box, new_search_button], className="col"),
                html.Div(children=[new_dropdown], className="col"),
                html.Div(children=[branch_dropdown], className="col"),
                html.Div(children=new_header_text_box, className="col")
            ], className="row ms-1 mb-1")
        )
    elif click_event == 'jazz-remove-filter':
        if len(children) > 1:
            children.pop()

    return children


@callback(
    Output("id-plat-risk-register", "children", allow_duplicate=True),
    Input("id-add-filter-risk", "n_clicks"),
    Input("url", "pathname"),
    State("id-plat-risk-register", "children"),
    prevent_initial_call='initial_duplicate',
)
def create_risk_register(add, pathname: str, children):
    click_event = clicked(callback_context)

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    if click_event is None or click_event == 'id-add-filter-risk':
        checkbox = dcc.Checklist(
            id={'type': 'risk-chk-box', 'index': len(children) if children else 0},
            options=[{"label": len(children) if children else 0, "value": len(children) if children else 0}],
            inline=True,
            labelStyle={"display": "flex", "align-items": "center", 'width': '10px'},
        )

        # id_label = html.Label(
        #     len(children) + 1 if children else 1,
        #     id={'type': 'risk-label', 'index': len(children) if children else 0},
        # )
        date_picker = dcc.DatePickerSingle(
            id={'type': 'risk-date-picker', 'index': len(children) if children else 0},
            initial_visible_month=date.today().strftime('%Y-%m-%d'),
            min_date_allowed=date.today() + relativedelta(months=-3),
            max_date_allowed=date.today() + relativedelta(days=1),
            date=date.today().strftime('%Y-%m-%d'),
        )
        description = dcc.Textarea(
            id={'type': 'risk-description-textarea', 'index': len(children) if children else 0},

        )

        probability_scale = dcc.Dropdown(
            options=[
                {'label': 'Unlikely', 'value': 1},
                {'label': 'Low', 'value': 2},
                {'label': '50-50', 'value': 3},
                {'label': 'High', 'value': 4},
                {'label': 'Definite', 'value': 5},
            ],
            id={'type': 'risk-probability', 'index': len(children) if children else 0},
            className="dropdown_custom",
        )

        impact_scale = dcc.Dropdown(
            options=[
                {'label': 'Insignificant', 'value': 1},
                {'label': 'Low', 'value': 2},
                {'label': 'Medium', 'value': 3},
                {'label': 'Critical', 'value': 4},
                {'label': 'Catastrophic', 'value': 5},
            ],
            id={'type': 'risk-impact', 'index': len(children) if children else 0},
            className="dropdown_custom",
        )

        mitigation_plan = dcc.Textarea(
            id={'type': 'risk-mitigation', 'index': len(children) if children else 0},
        )

        contingency_plan = dcc.Textarea(
            id={'type': 'risk-contingency', 'index': len(children) if children else 0},
        )

        children.append(
            html.Div(
                children=[
                    html.Div(date_picker, className="col align-content-center"),
                    html.Div(description, className="col d-flex align-content-center flex-wrap"),
                    html.Div(probability_scale, className="col align-content-center"),
                    html.Div(impact_scale, className="col align-content-center"),
                    html.Div(mitigation_plan, className="col d-flex align-content-center flex-wrap"),
                    html.Div(contingency_plan, className="col d-flex align-content-center flex-wrap"),
                    html.Div(checkbox, className="col d-flex align-content-center flex-wrap"),
                ],
                className="row ms-1 mt-1"
            )
        )
    return children


@callback(
    Output("id-plat-risk-register", "children", allow_duplicate=True),
    Output("paragraph_id", "children", allow_duplicate=True),
    Input("id-remove-filter-risk", "n_clicks"),
    State({'type': 'risk-chk-box', 'index': ALL}, "value"),
    State("id-plat-risk-register", "children"),
    prevent_initial_call=True
)
def remove_risk_register_row(remove, check_box_value, children):
    click_event = clicked(callback_context)
    patched_list = Patch()
    message = ""
    if click_event == 'id-remove-filter-risk':
        if len(children) > 1:
            values_to_remove = []
            for i, value in enumerate(check_box_value):
                if value:
                    values_to_remove.append(i)

            for v in values_to_remove:
                del patched_list[v]

            # Checkbox is not clicked. remove last row.
            if not values_to_remove:
                del patched_list[len(children) - 1]
                message = "No row selected. Deleted last row"
            else:
                message = f"Following rows deleted: {', '.join(list(map(str, values_to_remove)))}"
        else:
            message = "Can't delete only row"

    return patched_list, message


@callback(
    output=[
        Output("paragraph_id", "children"),
        Output("id-status-rpt-filename", "data")
    ],
    inputs=[
        Input("button_id", "n_clicks"),
        Input("url", "pathname")
    ],
    state=[
        State({'type': 'status-rpt-drop-down', 'index': ALL}, "id"),
        State({'type': 'status-rpt-drop-down', 'index': ALL}, "value"),
        State({'type': 'status-rpt-branch-drop-down', 'index': ALL}, "id"),
        State({'type': 'status-rpt-branch-drop-down', 'index': ALL}, "value"),
        State({'type': 'status-rpt-header', 'index': ALL}, "id"),
        State({'type': 'status-rpt-header', 'index': ALL}, "value"),
        State({'type': 'risk-date-picker', 'index': ALL}, "date"),
        State({'type': 'risk-description-textarea', 'index': ALL}, "value"),
        State({'type': 'risk-probability', 'index': ALL}, "value"),
        State({'type': 'risk-impact', 'index': ALL}, "value"),
        State({'type': 'risk-mitigation', 'index': ALL}, "value"),
        State({'type': 'risk-contingency', 'index': ALL}, "value"),
    ],
    background=True,
    running=[
        (Output("button_id", "disabled"), True, False),
        (Output("cancel_button_id", "disabled"), False, True),
        (
                Output("paragraph_id", "style"),
                {"visibility": "hidden"},
                {"visibility": "visible"},
        ),
        (
                Output("progress_bar_graph", "style"),
                {"visibility": "visible"},
                {"visibility": "hidden"},
        ),
    ],
    cancel=[Input("cancel_button_id", "n_clicks")],
    progress=Output("progress_bar_graph", "figure"),
    progress_default=make_progress_graph(0, 5),
)
@inject
def update_progress(
        set_progress, n_clicks, pathname,
        release_status_id, release_status,
        branch_status_id, branch_status,
        rpt_header_id, rpt_header,
        risk_date: list, risk_description: list, risk_probability: list,
        risk_impact: list, risk_mitigation: list, risk_contingency: list,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        my_logger = MyLogger().get_logger()
        risk_data = {
            'Date': risk_date,
            'Description': risk_description,
            'Probability': risk_probability,
            'Impact': risk_impact,
            'Mitigation Plan': risk_mitigation,
            'Contingency Plan': risk_contingency
        }
        df_risk = pd.DataFrame(risk_data)
        ranking = df_risk['Probability'] * df_risk['Impact']
        df_risk.insert(4, "Risk Ranking", ranking)

        total_step = 15
        current_step = 0

        jazz_release = []
        cookie_release = []
        jazz_rpt_header = []
        cookie_rpt_header = []
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        my_logger.debug(f'type of session_factory = {type(session_factory)}')

        if isinstance(session_factory, containers.DynamicContainer):
            session_factory.config.override({'ro_or_rw': 'ro'})
            session_factory.config_schema.override({'schema_name': schema_name})

            # pg_session_generator = session_factory.db_connections()['ro']()()[schema_name].session()
            pg_session_generator = session_factory.db_conn_provider().session()
            my_logger.debug("Dynamically injected DB connection")
        else:
            pg_session_generator = custom_container.Database(
                custom_container.DbConnectionURI(kpk_inst, read_only=True)).session()
            my_logger.debug("Injection of DB connection didn't work")

        for i, release_dict in enumerate(release_status_id):
            if release_dict['index'].startswith('jazz'):
                jazz_release.append(release_status[i])
            else:
                cookie_release.append(release_status[i])

        for i, rpt_header_dict in enumerate(rpt_header_id):
            if rpt_header_dict['index'].startswith('jazz'):
                jazz_rpt_header.append(rpt_header[i])
            else:
                cookie_rpt_header.append(rpt_header[i])

        my_logger.info(f"Versions to be searched for Cookie: {cookie_release}")
        my_logger.info(f"Versions to be searched for Cookie: {jazz_release}")
        template_file = r'c:\vishal\report\ReportTemplate.pptx' if os.name == "nt" else "/opt/reports/ReportTemplate.pptx"

        file_part = str(uuid.uuid4())
        report_file_name = f'c:/vishal/report/{file_part}.pptx' if os.name == "nt" else f"/opt/reports/{file_part}.pptx"

        with CreateReport(report_file_name, template_file=template_file) as rpt:
            with pg_session_generator as pg_session:
                if not isinstance(session_factory, containers.DynamicContainer):
                    updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: schema_name})
                    pg_session.bind = updated_engine

                my_logger.info("Creating title slide")
                rpt.add_slide(0)
                rpt.add_title("Weekly Status Report - Professional Services Project")
                rpt.add_subtitle("Reporting Period: ")
                my_logger.info("Creating transition slide")
                rpt.add_slide(5)
                rpt.add_section("Executive Summary")
                rpt.add_text_box_with_text(0.25, 0.8, 2, 0.3, "Key Updates", 12)
                set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                my_logger.info("Creating Release related slides")
                my_logger.info(cookie_rpt_header)

                for counter, value in enumerate(cookie_rpt_header):
                    rpt.add_slide(2)
                    rpt.add_section('Cookie Updates')
                    df = db.get_release_status(cookie_release[counter], pg_session)

                    df.query('project == "Cookie Enhancement"', inplace=True)
                    StatusCategory = namedtuple('StatusCategory', ['todo', 'wip', 'done'])

                    col_mapper = {
                        'Story & Task': StatusCategory('story_todo', 'story_wip', 'storydone'),
                        'BA & Testing': StatusCategory('other_todo', 'other_wip', 'otherdone'),
                        'All subtasks': StatusCategory('subtask_todo', 'subtask_wip', 'subtaskdone'),
                        'Bugs': StatusCategory('defect_todo', 'defect_wip', 'defectsdone')
                    }
                    for k, v in col_mapper.items():
                        df[k] = df[v.todo].astype(str) + '|' + df[v.wip].astype(str) + '|' + df[v.done].astype(str)

                    df.drop(columns=[
                        'initiative', 'initiative_id', 'Team', 'clientjira',
                        'story_todo', 'story_wip', 'storydone', 'other_todo', 'other_wip', 'otherdone',
                        'subtask_todo', 'subtask_wip', 'subtaskdone', 'defect_todo', 'defect_wip', 'defectsdone',
                        'components'

                    ], inplace=True)
                    df.rename(columns={'project': 'Project', 'status': 'Epic Status'}, inplace=True)

                    batch_size = min(10, df.shape[0])

                    for i in range(0, df.shape[0], batch_size):
                        rpt.add_slide(master_slide_no=5)
                        rpt.add_title(f'Cookie {value}')
                        batch = df.iloc[i:i + batch_size]
                        rpt.add_slide_table(
                            rows=batch.shape[0] + 1, cols=batch.shape[1],
                            left=1.5, top=1, width=8, height=1.5
                        )
                        rpt.add_table_data(batch, df.columns.values.tolist())
                        rpt.custom_style_table(11)
                        rpt.add_text_box_with_text(1.5, 6.25, 6, 0.5, "Legend: To Do| In Progress| Done", 11)

                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))
                    df = db.report_bug_summary(cookie_release[counter], pg_session)
                    df_pivot = pd.pivot_table(df, values=['opt_2'], index=['Team'], columns=['priority'],
                                              aggfunc=lambda x: x, fill_value="")

                    # Drop grand total row
                    # df_pivot.drop(columns='All', axis=1, inplace=True)
                    batch_size = min(12, df_pivot.shape[0])

                    for i in range(0, df_pivot.shape[0], batch_size):
                        rpt.add_slide(master_slide_no=5)
                        rpt.add_title('Cookie ' + value.replace('Release', 'Bug'))
                        batch = df_pivot.iloc[i:i + batch_size]
                        rpt.add_slide_table(
                            rows=batch.shape[0] + 1, cols=batch.shape[1] + 1,
                            left=1.5, top=1, width=8, height=1.5
                        )

                        rpt.add_table_data_with_index(batch, ['Team'] + batch.columns.get_level_values(1).tolist())
                        rpt.custom_style_table(11)
                        rpt.add_text_box_with_text(
                            1.5, 6.25, 7, 0.5,
                            "Legend: Open or In Progress| Fixed or Rejected or Verified| On Hold or Future Release| "
                            "Closed or Cancelled",
                            10
                        )
                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                rpt.add_slide(2)
                rpt.add_section("Jazz Updates")
                set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                # Add Logic for Jazz
                for counter, value in enumerate(jazz_rpt_header):
                    df = db.get_release_status(jazz_release[counter], pg_session)
                    df.query('project == "Jazz Enhancement"', inplace=True)

                    StatusCategory = namedtuple('StatusCategory', ['todo', 'wip', 'done'])

                    col_mapper = {
                        'Story & Task': StatusCategory('story_todo', 'story_wip', 'storydone'),
                        'BA & Testing': StatusCategory('other_todo', 'other_wip', 'otherdone'),
                        'All subtasks': StatusCategory('subtask_todo', 'subtask_wip', 'subtaskdone'),
                        'Bugs': StatusCategory('defect_todo', 'defect_wip', 'defectsdone')
                    }
                    for k, v in col_mapper.items():
                        df[k] = df[v.todo].astype(str) + '|' + df[v.wip].astype(str) + '|' + df[v.done].astype(str)

                    df.drop(columns=[
                        'initiative', 'initiative_id', 'Team', 'clientjira',
                        'story_todo', 'story_wip', 'storydone', 'other_todo', 'other_wip', 'otherdone',
                        'subtask_todo', 'subtask_wip', 'subtaskdone', 'defect_todo', 'defect_wip', 'defectsdone',
                        'components'

                    ], inplace=True)
                    df.rename(columns={'project': 'Project', 'status': 'Epic Status'}, inplace=True)
                    if df.shape[0] > 0:
                        batch_size = min(10, df.shape[0])
                        for i in range(0, df.shape[0], batch_size):
                            rpt.add_slide(master_slide_no=5)
                            rpt.add_title('Jazz ' + value)
                            batch = df.iloc[i:i + batch_size]
                            rpt.add_slide_table(
                                rows=batch.shape[0] + 1, cols=batch.shape[1],
                                left=1.5, top=1, width=8, height=1.5
                            )
                            rpt.add_table_data(batch, df.columns.values.tolist())
                            rpt.custom_style_table(11)

                            rpt.add_text_box_with_text(1.5, 6.25, 6, 0.5, "Legend: To Do| In Progress| Done", 11)

                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))
                    df = db.report_bug_summary(jazz_release[counter], pg_session)
                    df_pivot = pd.pivot_table(df, values=['opt_2'], index=['Team'], columns=['priority'],
                                              aggfunc=lambda x: x, fill_value="")

                    # Drop grand total row
                    # df_pivot.drop(columns='All', axis=1, inplace=True)
                    if df_pivot.shape[0] > 0:

                        batch_size = min(10, df_pivot.shape[0])

                        for i in range(0, df_pivot.shape[0], batch_size):
                            rpt.add_slide(master_slide_no=5)
                            rpt.add_title('Jazz ' + value.replace('Release', 'Bug'))
                            batch = df_pivot.iloc[i:i + batch_size]
                            rpt.add_slide_table(
                                rows=batch.shape[0] + 1, cols=batch.shape[1] + 1,
                                left=1.5, top=1, width=8, height=1.5
                            )

                            rpt.add_table_data_with_index(batch, ['Team'] + batch.columns.get_level_values(1).tolist())
                            rpt.custom_style_table(11)
                            rpt.add_text_box_with_text(
                                1.5, 6.25, 8, 0.5,
                                "Legend: Open or In Progress| Fixed or Rejected or Verified| On Hold or Future Release| "
                                "Closed or Cancelled", 11
                            )
                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            # End Logic for Jazz
            rpt.add_slide(2)
            rpt.add_section("Risks")
            rpt.add_slide(5)
            rpt.add_title("Risk Register")
            rpt.add_slide_table(rows=df_risk.shape[0] + 1, cols=df_risk.shape[1], left=0.5, top=1, width=12, height=5)
            rpt.add_table_data(df_risk, df_risk.columns.values.tolist())
            rpt.add_slide(2)
            rpt.add_section("Action Log")
            rpt.add_slide(5)
            rpt.add_title("Action Items")
            rpt.add_slide_table(rows=5, cols=4, left=1.5, top=1, width=8, height=1.5)
            df_action = pd.DataFrame(columns=['Action Item', 'RPOC', 'Due Date', 'Remarks'])
            rpt.add_table_data(df_action, ['Action Item', 'RPOC', 'Due Date', 'Remarks'])
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            my_logger.info("Creating environment slide")
            rpt.add_slide(2)
            rpt.add_section('Appendix')
            rpt.add_slide(2)
            rpt.add_section('Jazz, Cookie & IOPERF Environment Details')
            pod_data_list = get_data()
            rpt.add_slide(master_slide_no=5)
            rpt.add_title("Application and Platform version: Jazz & IOPERF")

            rpt.add_slide_table(
                rows=pod_data_list[0].shape[0] + 1, cols=pod_data_list[0].shape[1],
                left=1.5, top=1, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[0], ['Jazz', 'POD3'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            rpt.add_slide_table(
                rows=pod_data_list[1].shape[0] + 1, cols=pod_data_list[1].shape[1],
                left=1.5, top=5, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[1], ['IO-PERF', 'DC'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            rpt.add_slide(master_slide_no=5)
            rpt.add_title("Application and Platform version: Cookie")
            rpt.add_slide_table(
                rows=pod_data_list[2].shape[0] + 1, cols=pod_data_list[2].shape[1],
                left=1.5, top=1, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[2], ['Cookie', 'POD1', 'POD2', 'POD4'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

        # populates list of list
        # sample:
        # [['22.11', '22.11.1', '22.11.2']]
        # [['22.10.9.1', '22.10.9', '22.10.2', '22.10.1', '22.10', '22.10.8', '22.10.5', '22.10.7', '22.10.4.3', '22.10.4.2', '22.10.4', '22.10.3', '22.10.6'], ['23.1.4.1', '23.1.2', '23.1.3', '23.1.4', '23.1', '23.1.1']]

        my_logger.info(f"returning!!! Total steps: {current_step}")
        return [f"Clicked {n_clicks} times", report_file_name]
    else:
        raise PreventUpdate


@callback(
    Output("id-status-rpt-download", "data"),
    Input("id-download-report", "n_clicks"),
    State("id-status-rpt-filename", "data")
)
def download_status_report(n_clicks, filename):
    if n_clicks > 0:
        return dcc.send_file(filename)

keepass = custom_container.KPDatabaseOpener(
    os.getenv("DATABASE_PATH"),
    master_keyfile=os.getenv("MASTER_KEYFILE")
)
kpk_inst = custom_container.KeePassPasswordManager(keepass)


@callback(
    Output({'type': 'status-rpt-drop-down', 'index': MATCH}, 'value'),
    Output({'type': 'status-rpt-header', 'index': MATCH}, 'value'),
    Input({'type': 'status-rpt-drop-down-pattern', 'index': MATCH}, 'n_submit'),
    Input({'type': 'status-rpt-search', 'index': MATCH}, "n_clicks"),
    Input("url", "pathname"),
    State({'type': 'status-rpt-drop-down-pattern', 'index': MATCH}, 'value'),
)
@inject
def populate_selected_version_pattern(
        n_submit, n_clicks, pathname: str, filter_string: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    result_add = []
    result_remove = []

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
    with session_factory.db_conn_provider().session() as pg_session:
        final_list = db.get_all_active_versions(pg_session)

    filter_list = filter_string.split(",")
    filter_list = [x.strip() for x in filter_list]
    for f in filter_list:
        if f.strip().startswith('~'):
            result_remove += [x for x in final_list if x.startswith(f[1:])]
        else:
            result_add += [x for x in final_list if x.startswith(f)]

    result = list(set(result_add) - set(result_remove))
    versions = [tuple(map(int, v.split('.'))) for v in result]
    # Find the minimum and maximum versions
    min_version = min(versions)
    max_version = max(versions)

    # Convert the tuples back to strings
    min_version_str = '.'.join(map(str, min_version))
    max_version_str = '.'.join(map(str, max_version))

    return result, f"Release Status - {min_version_str} to {max_version_str}"
