import os
import platform
import time
from collections import defaultdict

import dash

import pandas as pd
from dash import callback, Output, Input, State, callback_context, html, dcc
from dash.exceptions import PreventUpdate
from dependency_injector.wiring import inject, Provide
from plotly import graph_objs as go, express as px


import custom_container
from data import MyLogger, get_from_db as db, clicked
from data.helper import (
    generate_layer_bullet, apply_href, apply_td, sort_priority, apply_acronymn,
     apply_dbc_progress, apply_list_to_string
)


@callback(
    Output("id-version-search", "value"),
    Output("id-versions", "value"),
    Input("id-version-serach-button", "n_clicks"),
    # Input("id-version-released-value", "value"),
    # Input("id-version-archive-value", "value"),
    Input("id-version-search", "n_submit"),
    State("id-version-search", "value"),
    State("url", "pathname"),
)
@inject
def get_versions_by_pattern(
        search_click,
        # released: int, archived: int,
        n_submit: int, pattern: str, pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    my_logger = MyLogger().get_logger()

    if pattern == '':
        raise PreventUpdate

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    version_filter_list = pattern.split(",")
    result_add = []
    result_remove = []

    # for i, pattern in enumerate(version_filter_list):
    #     pattern = pattern if '%' in pattern else pattern + '%'
    #     version_filter_list[i] = pattern.strip()
    #
    # pattern_list = db.get_matching_versions(version_filter_list, bool(released), bool(archived), project)
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        versions = db.get_all_active_versions(pg_session)

    filter_list = [x.strip() for x in version_filter_list]
    for f in filter_list:
        if f.strip().startswith('~'):
            result_remove += [x for x in versions if x.startswith(f[1:])]
        else:
            result_add += [x for x in versions if x.startswith(f)]
    pattern_list = list(set(result_add) - set(result_remove))
    my_logger.debug(f'Version patters: {pattern_list}')
    return "", pattern_list


@callback(
    Output("id-version-bullet-1", "className"),
    Output("id-version-bullet-2", "className"),
    Output("id-version-bullet-3", "className"),
    Output("id-version-bullet-5", "className"),
    Output("id-version-bullet-6", "className"),
    Output("id-version-bullet-7", "className"),
    Output("id-version-bullet-8", "className"),
    Output("id-version-layer-1", "className"),
    Output("id-version-layer-2", "className"),
    Output("id-version-layer-3", "className"),
    Output("id-version-layer-5", "className"),
    Output("id-version-layer-6", "className"),
    Output("id-version-layer-7", "className"),
    Output("id-version-layer-8", "className"),
    Output("id-version-main-header", "children"),
    Output("id-release-type", "className"),
    Output("id-release-selector", "className"),
    # Output("id-release-project", "className"),
    # Output("id-release-project-dropdown", "className"),
    Output("id-release-status-label", "className"),
    Output("id-release-status-value", "className"),
    Output("id-release-priority-label", "className"),
    Output("id-release-priority-value", "className"),
    Output("id-release-alert", "className"),
    Output("id-release-counts", "className"),
    Output("id-release-urgency", "className"),
    Input("id-version-bullet-1", "n_clicks"),
    Input("id-version-bullet-2", "n_clicks"),
    Input("id-version-bullet-3", "n_clicks"),
    Input("id-version-bullet-5", "n_clicks"),
    Input("id-version-bullet-6", "n_clicks"),
    Input("id-version-bullet-7", "n_clicks"),
    Input("id-version-bullet-8", "n_clicks"),
    # prevent_initial_call=True
)
def toggle_cards(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    layers = ["one", "two", "three", "five", "six", "seven", "eight"]
    layer_name, dict_layer, dict_toggle_bullet = generate_layer_bullet(layers, int(click_event.split('-')[3]))

    dict_title = defaultdict(
        lambda: "", one='Unresolved Issue Count',
        two="Related Issue Count", three="Release Status",
        five="Release Roadmap", seven="Release Roadmap",
        eight="Defects Dashboard"
    )

    option_dict = defaultdict(
        lambda: ["hide", "dcc-dropdown hide",
                 # "hide", "dcc-dropdown hide",
                 # "hide", "dcc-dropdown hide",
                 "hide", "dcc-dropdown hide", "hide", "dcc-dropdown hide", "hide", "hide", "hide"],
        eight=["", "dcc-dropdown",
               # "", "dcc-dropdown",
               # "", "dcc-dropdown",
               "", "dcc-dropdown", "", "dcc-dropdown", "",
               "", ""]
    )
    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name], dict_title[layer_name], *option_dict[layer_name]


@callback(
    Output("id-version-layer-3", "children"),
    Output("id-copy-epic", "content"),
    Input("id-versions", "value"),
    State("url", "pathname"),
    State("id-versions", "options")
)
@inject
def update_version_details(
        version_no, pathname, version_label,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    table_cols = []
    for col in [
        'Project',
        'Epic', 'Status', 'Client Jira#', 'Components', 'Stories & Tasks', 'Defects', 'Others', 'Subtasks',
        'Estimated Effort', 'Actual Effort', 'Variance',
    ]:
        table_cols.append(html.Th(col))

    if version_no is None:
        raise PreventUpdate
    elif not version_no:
        table_data = html.Table(
            children=[html.Thead(children=[html.Tr(children=table_cols)])],
            className='format-table'
        )
        return table_data, dash.no_update

    start_time = time.time_ns()
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
    query_start = time.process_time_ns()
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': project})

    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[project].session() as pg_session:
        df_new = db.get_release_status(version_lbl, pg_session)
    print(f"Query Time get_release_status: {round((time.process_time_ns() - query_start) * pow(10, -6), 3)} ms")
    if df_new.shape[0] == 0:
        return html.Div("No content found", className='no-content')

    # df_new['variance'] = df_new['variance'].astype('float64')
    df_new = df_new.round(1)
    df_new.sort_values(by=['initiative_id'], ascending=True, inplace=True)

    clipboard_epic_list = ", ".join(df_new['epic'].values)

    for col in ['clientjira', 'components']:
        df_new[col] = df_new[col].apply(apply_list_to_string)

    for col in ['initiative', 'epic']:
        df_new[col] = df_new[col].apply(apply_href)

    df_new['dbc_story'] = df_new.apply(
        lambda x: apply_dbc_progress(x['story_todo'], x['story_wip'], x['storydone']), axis=1)
    df_new['dbc_other'] = df_new.apply(
        lambda x: apply_dbc_progress(x['other_todo'], x['other_wip'], x['otherdone']), axis=1)
    df_new['dbc_defect'] = df_new.apply(
        lambda x: apply_dbc_progress(x['defect_todo'], x['defect_wip'], x['defectsdone']), axis=1)
    df_new['dbc_subtasks'] = df_new.apply(
        lambda x: apply_dbc_progress(x['subtask_todo'], x['subtask_wip'], x['subtaskdone']), axis=1)

    for col in df_new.columns:
        df_new[col] = df_new[col].apply(apply_td)

    df_new.drop(columns=[
        'storydone', 'otherdone',
        'defectsdone', 'subtaskdone',
        'story_todo', 'story_wip',
        'other_todo', 'other_wip', 'defect_todo', 'defect_wip', 'subtask_todo', 'subtask_wip',
        'initiative_id',
    ], inplace=True)
    df_new.rename(columns={
        'dbc_story': 'Story',
        'dbc_other': 'Others',
        'dbc_defect': 'Bugs',
        'dbc_subtasks': 'subtasks'
    }, inplace=True)

    final_table = html.Table(
        children=[
            html.Thead(
                children=[html.Tr([html.Th(col) for col in df_new.columns])]
            ),
            html.Tbody(
                children=[html.Tr(i) for i in df_new.values]
            )
        ], className='format-table'
    )
    total_time = (time.time_ns() - start_time) * pow(10, -9)
    print(f'update_version_details: Total time taken: {total_time} seconds')

    return final_table, clipboard_epic_list


@callback(
    # Output("id-release-project-dropdown", "options"),
    # Output("id-release-project-dropdown", "disabled"),
    # Output("id-release-project-dropdown", "value"),
    # Output("id-release-project-charts", "disabled"),
    Output("id-release-status-value", "options"),
    Output("id-release-status-value", "disabled"),
    Output("id-release-status-value", "value"),
    # Output("id-release-priority-value", "options"),
    Output("id-release-priority-value", "disabled"),
    Output("id-release-priority-value", "value"),
    Input("id-versions", "value"),
    Input("id-release-selector", "value"),
    State("id-versions", "options"),
    State("url", "pathname"),
)
@inject
def populate_project_values(
        version_no, release_selector: int, version_label, pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    if version_no is None:
        raise PreventUpdate

    my_logger = MyLogger().get_logger()

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        if version_no is not None:
            version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
            df = db.get_release_defect_status(version_lbl, pg_session, release_selector)
            my_logger.debug(f"number of rows returned: {df.shape}")

            if df.shape[0] > 0:
                options = [
                              {'label': 'ALL', 'value': 'ALL'}
                          ] + [
                              {'label': i, 'value': i} for i in df['project'].unique() if i is not None
                          ]

                disabled = False
                value = 'ALL'
                options_custom_status = [{'label': 'ALL', 'value': 'ALL'}] + [{'label': i, 'value': i} for i in
                                                                              sorted(df['statusclass'].unique()) if
                                                                              i not in [None, 'Done', 'Misc']]

                # option_team = [{'label': 'ALL', 'value': 'ALL'}] + [{'label': i, 'value': i} for i in
                #                                                     df['Team'].unique() if
                #                                                     i is not None]

                # return options, disabled, value, options_custom_status, disabled, value, option_team, disabled, value
                # return options_custom_status, disabled, value, option_team, disabled, value
                return options_custom_status, disabled, value, disabled, value

    # return [], True, None, [], True, None, [], True, None
    return [], True, None, True, None


#
#
# # @callback(
# #     Output("id-versions", "options"),
# #     State("id-version-released-value", "value"),
# #     State("id-version-archive-value", "value"),
# #     Input("url", "pathname"),
# # )
# # @inject
# # def update_version_chart(
# #         rel_val, archive_val, pathname,
# #         session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
# # ):
# #     print(f" path in get versions: {pathname}")
# #     schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
# #     if pathname == f"{os.getenv('DASH_BASE_PATH', '/')}{schema_name}/version":
# #         session_factory.config.override({'ro_or_rw': 'ro'})
# #         session_factory.config_schema.override({'schema_name': schema_name})
# #
# #         # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
# #         with session_factory.db_conn_provider().session() as pg_session:
# #             dff = db.get_versions(pg_session)[['id',
# #                                                'name', 'archived', 'released', 'issuesCount', 'issuesUnresolvedCount',
# #                                                'issuesFixedCount',
# #                                                'issuesAffectedCount']].copy(deep=True)
# #             # dff = dff.sort_values(by=['id'], ascending=False)
# #             dff = dff.sort_values(by=['name'], ascending=False)
# #             dff = dff[(dff['archived'] == archive_val) & (dff['released'] == rel_val)].copy(deep=True)
# #             options = [dict(label=name, value=name) for name in dff['name'].values]
# #         print(f"populating values")
# #         print(options)
# #         return options
# #     else:
# #         raise PreventUpdate
#
#
# @callback(
#     Output("id-version-layer-1", "children"),
#     Output("id-version-layer-2", "children"),
#
#     Input("id-version-released-value", "value"),
#     Input("id-version-archive-value", "value"),
#     Input("url", "pathname")
# )
# @inject
# def update_version_chart_details(
#         rel_val, archive_val, pathname,
#         session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
# ):
#     click_event = clicked(callback_context)
#     # Testing
#
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#     with session_factory.db_ro().session() as pg_session:
#         updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
#         pg_session.bind = updated_engine
#
#         dff = db.get_versions(pg_session)[['id',
#                                            'name', 'archived', 'released', 'issuesCount', 'issuesUnresolvedCount',
#                                            'issuesFixedCount',
#                                            'issuesAffectedCount']].copy(deep=True)
#     # dff = dff.sort_values(by=['id'], ascending=False)
#     dff = dff.sort_values(by=['name'], ascending=False)
#     dff = dff[(dff['archived'] == archive_val) & (dff['released'] == rel_val)].copy(deep=True)
#     # 3/16 Made change to drop down mapping
#     # options = [dict(label=name, value=id_) for name, id_ in dff[['name', 'id']].values]
#     options = [dict(label=name, value=name) for name in dff['name'].values]
#
#     # Create figure with secondary y-axis
#     fig3 = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)
#
#     fig3.add_trace(go.Bar(x=dff['name'], y=dff['issuesCount'], text=dff['issuesCount'],
#                           name='issuesCount', offsetgroup=0,
#                           ), secondary_y=False
#                    )
#     fig3.add_trace(go.Bar(x=dff['name'], y=dff['issuesUnresolvedCount'], text=dff['issuesUnresolvedCount'],
#                           name='issuesUnresolvedCount', offsetgroup=1),
#                    secondary_y=True
#                    )
#     # fig3.update_layout(barmode='group', title_text='Unresolved issue counts', )
#
#     fig4 = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)
#     fig4.add_trace(go.Bar(x=dff['name'], y=dff['issuesFixedCount'], text=dff['issuesFixedCount'],
#                           name='issuesFixedCount', offsetgroup=0,
#                           ), secondary_y=False
#                    )
#     fig4.add_trace(go.Bar(x=dff['name'], y=dff['issuesAffectedCount'], text=dff['issuesAffectedCount'],
#                           name='issuesAffectedCount', offsetgroup=1),
#                    secondary_y=True
#                    )
#     # fig4.update_layout(barmode='group', title_text='Related issues count', bargap=0.2, bargroupgap=0.2)
#
#     return [html.Div(""), dcc.Graph(figure=fig3)], dcc.Graph(figure=fig4)
#
#
# @callback(
#     Output("id-version-layer-6", "children"),
#     Input("id-versions", "value"),
#     Input("url", "pathname"),
#     State("id-versions", "options")
# )
# def create_release_bubble_chart(version_no, pathname, version_label):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#     start_time = time.perf_counter_ns()
#     version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
#     df = db.get_release_status_by_issuetype(version_lbl, project)
#     df['statuscategory'] = df['statuscategory'].astype(str)
#     fig = px.scatter(df, x="issuetype", y="statuscategory", size="count", color="issuetype")
#     return dcc.Graph(figure=fig)
#
#
# @callback(
#     Output("id-version-layer-7", "children"),
#     Input("id-versions", "value"),
#     Input("url", "pathname"),
#     State("id-versions", "options")
# )
# def view_release_details(version_no, pathname, version_label):
#     raise PreventUpdate
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     # Testing
#
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#     version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
#     df, df_child, df_subtask = db.get_release_details(version_lbl, project)
#
#     # parent_key = df["IssueKey"].unique().tolist()
#     # df_standard = df[~(df['Issue Type'] == "Epic") & (~df["ParentKey"].isin(parent_key))]
#     for value in ['#', 'Epic', 'Client Jira#', 'Components']:
#         pass
#     table_data = [
#         html.Thead(children=[
#             html.Tr(children=[
#                 html.Th("#"),
#                 html.Th("Epic"),
#                 html.Th("Client Jira#"),
#                 html.Th("Components"),
#                 html.Th("Issue Type"),
#                 html.Th("Status"),
#                 html.Th("Req Finalized"),
#                 html.Th("Approval Status"),
#                 html.Th("Total Effort"),
#                 html.Th("Effort Breakup", )
#             ])
#         ])
#     ]
#
#     # df_epic = df[df["Issue Type"] == "Epic"].copy(deep=True)
#
#     table_body_data = []
#     counter = 0
#     for initiative in df["Initiative"].unique():
#         table_body_data.append(
#             html.Tr(children=[html.Td(children=[html.Label(initiative)], colSpan=10, className="initiative"), ]))
#         # df_rows = df_epic[df_epic["ParentKey"] == initiative].copy(deep=True)
#         # Extract data related to Epics
#         df_rows = df[(df["ParentKey"] == initiative) & (df["Issue Type"] == "Epic")].copy(deep=True)
#         df_arrary = df_rows.to_numpy()
#         for row in df_arrary:
#             counter += 1
#             stan_arrary = df_child[(df_child["parentkey"] == row[1])].to_numpy()
#             popover_children = html.Table(
#                 children=[
#                     html.Tr(children=[
#                         html.Th("BA"),
#                         html.Th("AD"),
#                         html.Th("RD"),
#                         html.Th("QA"),
#                         html.Th("Contingency"),
#                     ]),
#                     html.Tr(
#                         children=[
#                             html.Td(row[11], className="text-font"),
#                             html.Td(row[12], className="text-font"),
#                             html.Td(row[13], className="text-font"),
#                             html.Td(row[14], className="text-font"),
#                             html.Td(row[15], className="text-font"),
#                         ]
#                     )], className="format-table")
#             td_data = [html.Td(f"{counter}.")]
#
#             td_data.extend([
#                 html.Td(
#                     html.A(row[1], href=f'https://corecard.atlassian.net/browse/{row[1]}', target='_blank')
#                 ),
#                 html.Td(", ".join(row[3]) if row[3] is not None else ""),
#                 html.Td(", ".join(row[4]) if row[4] is not None else ""),
#                 html.Td(row[5]), html.Td(row[7]),
#                 html.Td(row[8]), html.Td(row[9]),
#                 html.Td(row[10]),
#                 html.Td(
#                     children=[
#                         dbc.Button("Breakup", id=f"effort-breakup-{row[1]}", n_clicks=0),
#                         dbc.Popover(
#                             [
#                                 dbc.PopoverHeader("Effort Breakup"),
#                                 dbc.PopoverBody(popover_children)
#                             ],
#                             target=f"effort-breakup-{row[1]}",
#                             trigger="click",
#                             hide_arrow=False,
#                         )]
#                 ),
#             ])
#             if len(stan_arrary) > 0:
#                 table_body_data.append(html.Tr(children=td_data, className="view",
#                                                id={'type': 'main-row', 'index': row[1]}
#                                                ))
#             else:
#                 table_body_data.append(html.Tr(children=td_data))
#
#             # Collate all child elements in a table.
#             # Add that table as a row.
#             tr_standard = []
#             col_stan = 0
#             for row_stan in stan_arrary:
#                 col_stan += 1
#                 # Check child issues
#                 df_subtask_issue = df_subtask[df_subtask["parentkey"] == row_stan[0]].copy(deep=True)
#                 subtask_list = df_subtask_issue.to_numpy()
#
#                 record_subtask = 0
#                 tr_subtask = []
#                 table_subtask = None
#
#                 for row_subtask in subtask_list:
#                     record_subtask += 1
#                     td_data_subtask = [
#                         html.Td(f"{record_subtask}"),
#                         html.Td(html.A(row_subtask[0], href=f'https://corecard.atlassian.net/browse/{row_subtask[0]}',
#                                        target='_blank')),
#                         html.Td(row_subtask[1]),
#                         html.Td(row_subtask[2]),
#                         html.Td(row_subtask[3]),
#                         html.Td(dbc.Progress(label=f'{row_subtask[4]}%', value=row_subtask[4])),
#                         html.Td(row_subtask[5]),
#                         html.Td(row_subtask[6]),
#                         html.Td(row_subtask[7]),
#                         html.Td(row_subtask[8]),
#                         html.Td(row_subtask[9]),
#                     ]
#                     tr_subtask.append(html.Tr(children=td_data_subtask))
#
#                     table_subtask = [
#                         html.Table(children=[
#                             html.Thead(children=[html.Tr(
#                                 children=[
#                                     html.Th("#"),
#                                     html.Th("key"),
#                                     html.Th("Issue Type"),
#                                     html.Th("Status"),
#                                     html.Th("Assigned To"),
#                                     html.Th("Completion Percent"),
#                                     html.Th("Start Date"),
#                                     html.Th("Due Date"),
#                                     html.Th("Effort Estimate"),
#                                     html.Th("Remaining Estimate"),
#                                     html.Th("Total Effort"),
#                                 ]
#                             )], className="format-subtask-table"),
#                             html.Tbody(children=tr_subtask)
#                         ], className="")
#                     ]
#
#                 td_data_stan = [
#                     html.Td(f"{col_stan}."),
#                     html.Td(html.A(row_stan[0], href=f'https://corecard.atlassian.net/browse/{row_stan[0]}',
#                                    target='_blank')),
#                     html.Td(row_stan[1]),
#                     html.Td(row_stan[2]),
#                     html.Td(dbc.Progress(value=100 * row_stan[3], label=f"{100 * row_stan[3]:.0f} %", color="info")),
#                     html.Td(row_stan[4]),
#                     html.Td(row_stan[5]),
#                     html.Td(row_stan[6]),
#                 ]
#
#                 if table_subtask is not None:
#                     tr_standard.append(html.Tr(children=td_data_stan, className="view",
#                                                id={'type': 'main-row', 'index': row_stan[0]}
#                                                ))
#                     tr_standard.append(html.Tr(children=[html.Td(table_subtask, colSpan=8)],
#                                                className="fold",
#                                                id={'type': 'child-row', 'index': row_stan[0]}
#                                                ))
#                 else:
#                     tr_standard.append(html.Tr(children=td_data_stan))
#
#             table_stan = [html.Table(children=[
#                 html.Thead(children=[html.Tr(
#                     children=[
#                         html.Th("#"),
#                         html.Th("key"),
#                         html.Th("Issue Type"),
#                         html.Th("Status"),
#                         # html.Th("Assigned To"),
#                         html.Th("Completion Percent"),
#                         # html.Th("Start Date"),
#                         # html.Th("Due Date"),
#                         html.Th("Effort Estimate"),
#                         html.Th("Remaining Estimate"),
#                         html.Th("Total Effort"),
#                     ]
#                 )]),
#                 html.Tbody(children=tr_standard)
#             ], className="format-table")]
#
#             table_body_data.append(
#                 html.Tr(children=html.Td(children=table_stan, colSpan=10), className="fold",
#                         id={'type': 'child-row', 'index': row[1]}
#                         ))
#
#         # Most likely bugs will come here
#         # Should a new table be created for this?
#
#         df_rows_stan = df[(df["Initiative"] == initiative)].copy(deep=True)
#         df_rows_stan = df_rows_stan[~df_rows_stan["Issue Type"].isin(["Epic"])]
#
#         df_arrary = df_rows_stan.to_numpy()
#         for row in df_arrary:
#             counter += 1
#             td_data_stan = [
#                 # html.Td(f"{counter}. ", className="arrow"),
#                 html.Td(f"{counter}. "),
#                 html.Td(
#                     html.A(row[1], href=f'https://corecard.atlassian.net/browse/{row[1]}', target='_blank')
#                 ),
#                 html.Td(", ".join(row[3]) if row[3] is not None else ""),
#                 html.Td(", ".join(row[4]) if row[4] is not None else ""),
#                 html.Td(row[5]), html.Td(row[7]),
#                 html.Td(row[8]), html.Td(row[9]),
#                 html.Td(row[15]),
#                 html.Td(""),
#             ]
#             table_body_data.append(html.Tr(children=td_data_stan))
#
#     table_data.append(html.Tbody(children=table_body_data))
#     final_table = html.Table(children=table_data, className="format-table")
#
#     return final_table
#
#
# @callback(
#     Output("id-download", "data"),
#     Input("id-version-layer-3", "className"),
#     Input("id-version-layer-8", "className"),
#     Input("id-versions", "value"),
#     Input("url", "pathname"),
#     Input("id-excel-download", "n_clicks"),
#     State("id-versions", "options")
# )
# @inject
# def generate_xlsx(
#         layer3, layer8, version_no, pathname, download_click, version_label,
#         session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
# ):
#     click_event = clicked(callback_context)
#
#     def to_xlsx(bytes_io):
#         xslx_writer = pd.ExcelWriter(bytes_io, engine="xlsxwriter")
#         df.to_excel(xslx_writer, index=False, sheet_name="sheet1")
#         worksheet = xslx_writer.sheets['sheet1']
#         workbook = xslx_writer.book
#         wrap_format = workbook.add_format({'text_wrap': True})
#         for i, col in enumerate(df.columns):
#             column_len = df[col].astype(str).str.len().max()
#             column_len = max(column_len, len(col)) + 2
#             if col == 'summary':
#                 worksheet.set_column(i, i, 50)
#             elif col in ['clientjira', 'components']:
#                 worksheet.set_column(i, i, 25, wrap_format)
#             else:
#                 worksheet.set_column(i, i, column_len)
#         row_, col_ = df.shape
#         worksheet.autofilter(0, 0, row_, col_ - 1)
#         xslx_writer.close()
#
#     if click_event is None:
#         raise PreventUpdate
#     schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#     if version_no is None:
#         raise PreventUpdate
#
#     version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
#     if click_event == "id-excel-download":
#         if 'show' in layer3:
#             session_factory.config.override({'ro_or_rw': 'ro'})
#             session_factory.config_schema.override({'schema_name': schema_name})
#             with session_factory.db_conn_provider().session() as pg_session:
#                 # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
#                 df = db.get_release_status(version_lbl, pg_session)
#
#             for col in ['clientjira', 'components']:
#                 df[col] = df[col].apply(apply_list_to_newline)
#             return dcc.send_bytes(to_xlsx, "dashboard.xlsx")
#         elif 'show' in layer8:
#             def to_xlsx_2(bytes_io):
#                 xslx_writer = pd.ExcelWriter(bytes_io, engine="xlsxwriter")
#                 df1.to_excel(xslx_writer, index=False, sheet_name="AffectsVersion")
#                 worksheet = xslx_writer.sheets['AffectsVersion']
#                 for i, col in enumerate(df1.columns):
#                     column_len = df1[col].astype(str).str.len().max()
#                     column_len = max(column_len, len(col)) + 2
#                     if col == 'summary':
#                         worksheet.set_column(i, i, 50)
#                     else:
#                         worksheet.set_column(i, i, column_len)
#
#                 df2.to_excel(xslx_writer, index=False, sheet_name="FixVersion")
#                 worksheet = xslx_writer.sheets['FixVersion']
#                 for i, col in enumerate(df2.columns):
#                     column_len = df2[col].astype(str).str.len().max()
#                     column_len = max(column_len, len(col)) + 2
#                     if col == 'summary':
#                         worksheet.set_column(i, i, 50)
#                     else:
#                         worksheet.set_column(i, i, column_len)
#                 xslx_writer.close()
#
#             session_factory.config.override({'ro_or_rw': 'ro'})
#             session_factory.config_schema.override({'schema_name': schema_name})
#             with session_factory.db_conn_provider().session() as pg_session:
#                 # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
#                 df1 = db.get_release_defect_status(version_lbl, pg_session, 0)
#                 df2 = db.get_release_defect_status(version_lbl, pg_session, 1)
#             return dcc.send_bytes(to_xlsx_2, "version.xlsx")
#
#
@callback(
    Output("id-version-bugs-table-body", "children"),
    Output("id-release-alert", "children"),
    Output("id-release-urgency", "children"),
    Input("id-versions", "value"),
    Input("id-release-selector", "value"),
    # Input("id-release-project-dropdown", "value"),
    # Input("id-release-project-charts", "value"),
    Input("id-release-status-value", "value"),
    Input("id-release-priority-value", "value"),
    State("id-versions", "options"),
    State("url", "pathname"),
)
@inject
def show_defect_details(
        version_no: list, release_selector: str,
        # project_logical: list,
        # chart_name: str,
        custom_status: str, team_name: str, version_label: dict,
        pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    print(f"inside show_defect_details")
    print(f"click event: {click_event}")
    if click_event is None:
        raise PreventUpdate

    if version_no is None:
        raise PreventUpdate

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

    print("processing bug detals")
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})
    with session_factory.db_conn_provider().session() as pg_session:
        start_time = time.perf_counter_ns()
        print(f'Release selector = {release_selector}')
        dff = db.get_release_defect_status(version_lbl, pg_session, int(release_selector), )
        print(
            f'Query Execution Time get_release_defect_status: {(time.perf_counter_ns() - start_time) * pow(10, -6)} ms')
        if dff.shape[0] == 0:
            return html.Div("No data found", className='no-content'), "", ""

    # if 'ALL' in project_logical:
    #     dff = df.copy()
    # else:
    #     dff = df[df['project'].isin(project_logical)].copy()
    # if 'ALL' not in project_logical:
    #     dff.query("project.isin(@project_logical)", inplace=True)

    if 'ALL' not in custom_status:
        dff.query("statusclass == @custom_status", inplace=True)
        # dff = dff[dff['statusclass'] == custom_status].copy()

    if team_name == 'FILTER_HIGH':
        dff = dff[dff['priority'].isin(['Show Stopper', 'Critical', 'High'])].copy()
    elif team_name == 'FILTER_OUT_HIGH':
        dff = dff[dff['priority'].isin(['Medium', 'Low'])].copy()

    if dff.shape[0] == 0:
        return html.Div("No data found", className='no-content'), "", ""

    version = ['AffectsVersion', 'FixVersion']
    version_type = 'FixVersion' if int(release_selector) else 'AffectsVersion'
    fig = go.Figure()
    chart_name = "details"
    if chart_name == 'opendefects':
        df_chart = dff[[version[int(release_selector)], 'statusclass', 'priority', 'key']].groupby(
            [version[int(release_selector)], 'statusclass', 'priority']).agg({'key': 'count'}).reset_index()

        # Converts numpy array to python list
        priorities = df_chart['priority'].unique().tolist()
        # https://www.geeksforgeeks.org/python-sort-list-according-to-other-list-order/
        sort_by = ['Show Stopper', 'Critical', 'High', 'Medium', 'Low']
        res = [tuple for x in sort_by for tuple in priorities if tuple == x]
        priorities.sort(key=lambda i: sort_by.index(i))
        # priority_sorted = []
        # if 'Critical' in priorities:
        #     priority_sorted.append('Critical')
        # if 'High' in priorities:
        #     priority_sorted.append('High')
        # if 'Medium' in priorities:
        #     priority_sorted.append('Medium')
        # if 'Low' in priorities:
        #     priority_sorted.append('Low')

        release_sorted = sorted(df_chart[version[int(release_selector)]].unique().tolist(),
                                key=lambda item: item.split("."))

        fig = px.bar(
            df_chart, x=version[int(release_selector)], y='key', color='statusclass', text='key', barmode='group',
            hover_data=['priority'],
            facet_row='priority',
            category_orders={"priority": priorities, "AffectsVersion": release_sorted, "FixVersion": release_sorted},
            height=475,
            # labels=dict(key='Count', AffectsVersion='Release#', FixVersion='Release#')
        )
        fig.update_xaxes(title_text='Release', title_font=dict(size=12, family='Rajdhani', color='crimson'))
        fig.update_yaxes(title_text='Count', title_font=dict(size=12, family='Rajdhani', color='crimson'))
        # Source: https://community.plotly.com/t/changing-label-of-plotly-express-facet-categories/28066/4
        fig.for_each_annotation(lambda a: a.update(text=a.text.replace('priority=', '')))
    elif chart_name == 'bypriority':
        df_chart = dff[[version[int(release_selector)], 'priority', 'key']].groupby([version_type, 'priority']).agg(
            {'key': 'count'}).reset_index()
        fig = px.bar(df_chart, x=version[int(release_selector)], y='key', color='priority', text='key')
    elif chart_name == 'details':
        # table_cols = []
        # for col in [
        #     'Project', 'Issue', 'Priority', 'Status', 'Reopen count', 'Epic', 'Client JIRA#', 'Initiative', 'version'
        # ]:
        #     table_cols.append(html.Th(col))
        # table_data = [html.Tr(children=table_cols)]
        #
        # final_table = html.Table(children=table_data, className="format-table")
        print(f"dataframe has: {dff.shape}")
        if dff.shape[0] == 0:
            # return html.Div(de.Lottie(url=lottie_notfound, width="50%", height="50%", options=options))
            return html.Div("No content found", className='no-content')

        dff = dff[~(dff['statusCategory'] == 'Done')]
        dff.drop(columns=['statusCategory', 'statusclass', 'reopen', 'clientjira'], inplace=True)

        # if int(release_selector) == 1:
        #     dff.drop(columns=['statusCategory', 'AffectsVersion', 'statusclass'], inplace=True)
        # else:
        #     dff.drop(columns=['statusCategory', 'FixVersion', 'statusclass'], inplace=True)
        # dff.replace(to_replace=f"{project.upper()}-", value="", inplace=True, regex=True)
        dff.severity = dff['severity'].fillna("NA")
        list_ordering = ['Show Stopper', 'Critical', 'Urgent', 'High', 'Medium', 'Low', 'NA']
        dff.severity = pd.Categorical(dff.severity, categories=list_ordering, ordered=True)
        dff.sort_values(by=['severity', 'aging'], ascending=[True, False], inplace=True)
        df_severity = dff[['severity', 'key']].groupby(['severity']).agg({'key': 'count'}).reset_index()
        df_severity.query('key != 0', inplace=True)
        dff['severity'] = dff.severity.astype('str')

        dff.priority = pd.Categorical(dff.priority, categories=list_ordering, ordered=True)
        dff.sort_values(by=['priority', 'aging'], ascending=[True, False], inplace=True)
        # to avoid ValueError: setting an array element with a sequence need to convert these to value
        dff['priority'] = dff.priority.astype('str')

        df_priority = dff[['priority', 'key']].groupby(['priority']).agg({'key': 'count'}).reset_index()
        if platform.uname().node == 'dashboard':
            session_factory.config_schema.override({'schema_name': 'mssql'})
            with session_factory.db_conn_provider().create_engine() as connection:
                with connection.connect() as conn:
                    # query = conn.execute(
                    #     text(
                    #         f"""select
                    #  test_id as TC_Number , Test_name, a.lutdescription as "Automationstatus"
                    # from
                    # ISCProjectManagement.dbo.test_case WITH (NOLOCK)
                    # left join
                    # ISCProjectManagement.dbo.lut a WITH (NOLOCK) on a.lutcode = Automationstatus and a.tablename = 'test_case' and fieldname = 'automationstatus'
                    # where projectid in
                    # ('1106082','1106084', '1106163', '1106223' )""")
                    # )
                    # ms_rows = query.all()
                    ms_rows = db.get_test_case_mapping(conn)
                    df_ms = pd.DataFrame(ms_rows)

                    dff = dff.merge(df_ms, left_on="Test Case#", right_on="TC_Number", how="left")
                    dff.drop(columns=['TC_Number'], inplace=True)
        else:
            dff['Test_name'] = ''
            dff['Automationstatus'] = ''

        for col in ['key']:
            dff[col] = dff[col].apply(apply_href)

        for col in dff.columns:
            dff[col] = dff[col].apply(apply_td)

        # print(pd.Series(df_priority.key.values, index=df_priority.priority).to_dict())
        if df_priority.shape[0] > 0:
            df_priority = df_priority.sort_values(by="priority", key=sort_priority)
            df_priority['priority'] = df_priority.apply(lambda x: apply_acronymn(x['priority']), axis=1)
            alert_string = ", ".join(
                [i for i in (df_priority['priority'] + ": " + df_priority['key'].astype(str)).values])
        else:
            alert_string = "No Defects"
        if df_severity.shape[0] > 0:
            df_severity['severity'] = df_severity.apply(lambda x: apply_acronymn(x['severity']), axis=1)
            alert_string_severity = ", ".join(
                [i for i in (df_severity['severity'] + ": " + df_severity['key'].astype(str)).values])
        else:
            alert_string_severity = "No Defects"

        return [
            html.Tr(i) for i in dff.values.tolist()
        ], html.Span(alert_string), html.Span(alert_string_severity)
    return html.Div(children=[dcc.Graph(figure=fig)]), "", ""
