"""
Modern Async Version Callbacks
Optimized version of callback_version.py with async support and improved performance
"""

import os
import asyncio
import time
from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple

import dash
import pandas as pd
from dash import callback, Output, Input, State, callback_context, html, dcc, DiskcacheManager
from dash.exceptions import PreventUpdate
from dependency_injector.wiring import inject, Provide
from plotly import graph_objs as go, express as px

import custom_container
from data import MyLog<PERSON>, get_from_db as db, clicked
from data.helper import (
    generate_layer_bullet, apply_href, apply_td, sort_priority, apply_acronymn,
    apply_dbc_progress, apply_list_to_string
)

# Initialize async cache
import diskcache
cache = diskcache.Cache("./cache")
background_callback_manager = DiskcacheManager(cache)

# Logger instance
logger = MyLogger().get_logger()


@callback(
    Output("id-version-search", "value"),
    Output("id-versions", "value"),
    Input("id-version-serach-button", "n_clicks"),
    Input("id-version-search", "n_submit"),
    State("id-version-search", "value"),
    State("url", "pathname"),
    background=True,
    manager=background_callback_manager,
    prevent_initial_call=True
)
@inject
async def get_versions_by_pattern_async(
        search_click: int,
        n_submit: int,
        pattern: str,
        pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> Tuple[str, List[str]]:
    """
    Async version of get_versions_by_pattern with improved performance
    """
    if not pattern or pattern.strip() == '':
        raise PreventUpdate

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_filter_list = [p.strip() for p in pattern.split(",")]
        
        # Configure session for read-only access
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': schema_name})

        # Use async context manager for database operations
        async with asyncio.to_thread(session_factory.db_conn_provider().session) as pg_session:
            # Run database query in thread pool
            versions = await asyncio.to_thread(db.get_all_active_versions, pg_session)

        # Process version filtering
        result_add = []
        result_remove = []
        
        for filter_pattern in version_filter_list:
            if filter_pattern.startswith('~'):
                # Exclude pattern
                exclude_pattern = filter_pattern[1:]
                result_remove.extend([v for v in versions if v.startswith(exclude_pattern)])
            else:
                # Include pattern
                result_add.extend([v for v in versions if v.startswith(filter_pattern)])
        
        # Calculate final result
        pattern_list = list(set(result_add) - set(result_remove))
        
        logger.debug(f'Version patterns found: {len(pattern_list)} versions')
        return "", pattern_list
        
    except Exception as e:
        logger.error(f"Error in get_versions_by_pattern_async: {e}")
        raise PreventUpdate


@callback(
    [
        Output("id-version-bullet-1", "className"),
        Output("id-version-bullet-2", "className"),
        Output("id-version-bullet-3", "className"),
        Output("id-version-bullet-5", "className"),
        Output("id-version-bullet-6", "className"),
        Output("id-version-bullet-7", "className"),
        Output("id-version-bullet-8", "className"),
        Output("id-version-layer-1", "className"),
        Output("id-version-layer-2", "className"),
        Output("id-version-layer-3", "className"),
        Output("id-version-layer-5", "className"),
        Output("id-version-layer-6", "className"),
        Output("id-version-layer-7", "className"),
        Output("id-version-layer-8", "className"),
        Output("id-version-main-header", "children"),
        Output("id-release-type", "className"),
        Output("id-release-selector", "className"),
        Output("id-release-status-label", "className"),
        Output("id-release-status-value", "className"),
        Output("id-release-priority-label", "className"),
        Output("id-release-priority-value", "className"),
        Output("id-release-alert", "className"),
        Output("id-release-counts", "className"),
        Output("id-release-urgency", "className"),
    ],
    [
        Input("id-version-bullet-1", "n_clicks"),
        Input("id-version-bullet-2", "n_clicks"),
        Input("id-version-bullet-3", "n_clicks"),
        Input("id-version-bullet-5", "n_clicks"),
        Input("id-version-bullet-6", "n_clicks"),
        Input("id-version-bullet-7", "n_clicks"),
        Input("id-version-bullet-8", "n_clicks"),
    ]
)
def toggle_cards_modern(*args) -> Tuple:
    """
    Modern version of toggle_cards with improved logic
    """
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    
    # Extract layer number from click event
    layer_number = int(click_event.split('-')[3])
    layers = ["one", "two", "three", "five", "six", "seven", "eight"]
    layer_name, dict_layer, dict_toggle_bullet = generate_layer_bullet(layers, layer_number)

    # Modern title mapping
    title_map = {
        "one": "Unresolved Issue Count",
        "two": "Related Issue Count", 
        "three": "Release Status",
        "five": "Release Roadmap",
        "six": "Issue Type Analysis",
        "seven": "Release Roadmap Timeline",
        "eight": "Defects Dashboard"
    }

    # Modern visibility options for different layers
    visibility_options = {
        "eight": [
            "filter-section",  # release-type
            "modern-dropdown",  # release-selector
            "filter-section",  # release-status-label
            "modern-dropdown",  # release-status-value
            "filter-section",  # release-priority-label
            "modern-dropdown",  # release-priority-value
            "stats-container",  # release-alert
            "filter-section",  # release-counts
            "stats-container"   # release-urgency
        ],
        "default": [
            "filter-section hide",
            "modern-dropdown hide",
            "filter-section hide", 
            "modern-dropdown hide",
            "filter-section hide",
            "modern-dropdown hide",
            "stats-container hide",
            "filter-section hide",
            "stats-container hide"
        ]
    }

    # Get visibility classes for current layer
    visibility_classes = visibility_options.get(layer_name, visibility_options["default"])
    
    return (
        *dict_toggle_bullet[layer_name],  # Bullet classes
        *dict_layer[layer_name],          # Layer classes
        title_map.get(layer_name, ""),    # Header title
        *visibility_classes               # Visibility classes
    )


@callback(
    Output("id-version-layer-3", "children"),
    Output("id-copy-epic", "content"),
    Input("id-versions", "value"),
    State("url", "pathname"),
    State("id-versions", "options"),
    background=True,
    manager=background_callback_manager,
    prevent_initial_call=True
)
@inject
async def update_version_details_async(
        version_no: Optional[List[str]],
        pathname: str,
        version_label: List[Dict],
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> Tuple[html.Div, str]:
    """
    Async version of update_version_details with improved performance and error handling
    """
    if not version_no:
        # Return empty table structure
        table_cols = [
            html.Th(col) for col in [
                'Project', 'Epic', 'Status', 'Client Jira#', 'Components', 
                'Stories & Tasks', 'Defects', 'Others', 'Subtasks',
                'Estimated Effort', 'Actual Effort', 'Variance'
            ]
        ]
        
        empty_table = html.Div(
            className="empty-state",
            children=[
                html.Div(
                    className="empty-state-content",
                    children=[
                        html.I(className="empty-icon", children="📊"),
                        html.H3("No Versions Selected", className="empty-title"),
                        html.P("Select one or more versions to view release status", className="empty-description")
                    ]
                )
            ]
        )
        return empty_table, ""

    try:
        start_time = time.time_ns()
        project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]
        
        # Configure session
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': project})

        # Run database query asynchronously
        async with asyncio.to_thread(session_factory.db_conn_provider().session) as pg_session:
            query_start = time.process_time_ns()
            df_new = await asyncio.to_thread(db.get_release_status, version_lbl, pg_session)
            query_time = round((time.process_time_ns() - query_start) * pow(10, -6), 3)
            logger.debug(f"Async Query Time get_release_status: {query_time} ms")

        if df_new.shape[0] == 0:
            return html.Div(
                className="no-content-modern",
                children=[
                    html.I(className="no-content-icon", children="🔍"),
                    html.H3("No Content Found"),
                    html.P("No release data available for the selected versions")
                ]
            ), ""

        # Process data asynchronously
        df_processed = await asyncio.to_thread(process_release_data, df_new)
        
        # Generate clipboard content
        clipboard_epic_list = ", ".join(df_processed['epic'].values)
        
        # Create modern table
        final_table = create_modern_release_table(df_processed)
        
        total_time = (time.time_ns() - start_time) * pow(10, -9)
        logger.info(f'update_version_details_async: Total time taken: {total_time:.3f} seconds')

        return final_table, clipboard_epic_list
        
    except Exception as e:
        logger.error(f"Error in update_version_details_async: {e}")
        return html.Div(
            className="error-state",
            children=[
                html.I(className="error-icon", children="⚠️"),
                html.H3("Error Loading Data"),
                html.P(f"An error occurred while loading release data: {str(e)}")
            ]
        ), ""


def process_release_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process release data with optimized operations
    """
    # Round numeric columns
    df = df.round(1)
    df.sort_values(by=['initiative_id'], ascending=True, inplace=True)

    # Apply transformations
    for col in ['clientjira', 'components']:
        if col in df.columns:
            df[col] = df[col].apply(apply_list_to_string)

    for col in ['initiative', 'epic']:
        if col in df.columns:
            df[col] = df[col].apply(apply_href)

    # Apply progress bars
    progress_columns = [
        ('dbc_story', 'story_todo', 'story_wip', 'storydone'),
        ('dbc_other', 'other_todo', 'other_wip', 'otherdone'),
        ('dbc_defect', 'defect_todo', 'defect_wip', 'defectsdone'),
        ('dbc_subtasks', 'subtask_todo', 'subtask_wip', 'subtaskdone')
    ]
    
    for new_col, todo_col, wip_col, done_col in progress_columns:
        if all(col in df.columns for col in [todo_col, wip_col, done_col]):
            df[new_col] = df.apply(
                lambda x: apply_dbc_progress(x[todo_col], x[wip_col], x[done_col]), axis=1
            )

    # Apply table cell formatting
    for col in df.columns:
        df[col] = df[col].apply(apply_td)

    # Clean up columns
    columns_to_drop = [
        'storydone', 'otherdone', 'defectsdone', 'subtaskdone',
        'story_todo', 'story_wip', 'other_todo', 'other_wip', 
        'defect_todo', 'defect_wip', 'subtask_todo', 'subtask_wip',
        'initiative_id'
    ]
    
    existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    if existing_columns_to_drop:
        df.drop(columns=existing_columns_to_drop, inplace=True)

    # Rename columns
    rename_map = {
        'dbc_story': 'Story',
        'dbc_other': 'Others', 
        'dbc_defect': 'Bugs',
        'dbc_subtasks': 'subtasks'
    }
    
    existing_renames = {k: v for k, v in rename_map.items() if k in df.columns}
    if existing_renames:
        df.rename(columns=existing_renames, inplace=True)

    return df


def create_modern_release_table(df: pd.DataFrame) -> html.Div:
    """
    Create a modern styled release status table
    """
    return html.Div(
        className="modern-release-table-container",
        children=[
            html.Table(
                className="modern-release-table",
                children=[
                    html.Thead(
                        children=[
                            html.Tr(
                                className="table-header-row",
                                children=[html.Th(col, className="sortable") for col in df.columns]
                            )
                        ]
                    ),
                    html.Tbody(
                        children=[
                            html.Tr(
                                className="table-data-row",
                                children=row
                            ) for row in df.values
                        ]
                    )
                ]
            )
        ]
    )


@callback(
    [
        Output("id-release-status-value", "options"),
        Output("id-release-status-value", "disabled"),
        Output("id-release-status-value", "value"),
        Output("id-release-priority-value", "disabled"),
        Output("id-release-priority-value", "value"),
    ],
    [
        Input("id-versions", "value"),
        Input("id-release-selector", "value"),
    ],
    [
        State("id-versions", "options"),
        State("url", "pathname"),
    ],
    background=True,
    manager=background_callback_manager,
    prevent_initial_call=True
)
@inject
async def populate_project_values_async(
        version_no: Optional[List[str]],
        release_selector: int,
        version_label: List[Dict],
        pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> Tuple[List[Dict], bool, str, bool, str]:
    """
    Async version of populate_project_values with improved performance
    """
    if not version_no:
        return [], True, None, True, None

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

        # Configure session
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': schema_name})

        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

        # Run database query asynchronously
        async with asyncio.to_thread(session_factory.db_conn_provider().session) as pg_session:
            df = await asyncio.to_thread(
                db.get_release_defect_status,
                version_lbl,
                pg_session,
                release_selector
            )

        logger.debug(f"Async populate_project_values: {df.shape[0]} rows returned")

        if df.shape[0] > 0:
            # Create status options
            status_options = [{'label': 'ALL', 'value': 'ALL'}]
            unique_statuses = sorted([
                status for status in df['statusclass'].unique()
                if status not in [None, 'Done', 'Misc']
            ])
            status_options.extend([{'label': status, 'value': status} for status in unique_statuses])

            return status_options, False, 'ALL', False, 'ALL'

        return [], True, None, True, None

    except Exception as e:
        logger.error(f"Error in populate_project_values_async: {e}")
        return [], True, None, True, None


@callback(
    [
        Output("id-version-bugs-table-body", "children"),
        Output("id-release-alert", "children"),
        Output("id-release-urgency", "children"),
    ],
    [
        Input("id-versions", "value"),
        Input("id-release-selector", "value"),
        Input("id-release-status-value", "value"),
        Input("id-release-priority-value", "value"),
    ],
    [
        State("id-versions", "options"),
        State("url", "pathname"),
    ],
    background=True,
    manager=background_callback_manager,
    prevent_initial_call=True
)
@inject
async def show_defect_details_async(
        version_no: Optional[List[str]],
        release_selector: int,
        custom_status: str,
        team_name: str,
        version_label: List[Dict],
        pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> Tuple[List[html.Tr], html.Span, html.Span]:
    """
    Async version of show_defect_details with improved performance and error handling
    """
    if not version_no:
        return [
            html.Tr(
                className="empty-row",
                children=[
                    html.Td(
                        "Select versions to view defect details",
                        colSpan=18,
                        className="empty-message"
                    )
                ]
            )
        ], html.Span("Select versions to view statistics"), html.Span("Select versions to view statistics")

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

        # Configure session
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': schema_name})

        # Run database query asynchronously
        async with asyncio.to_thread(session_factory.db_conn_provider().session) as pg_session:
            start_time = time.perf_counter_ns()
            dff = await asyncio.to_thread(
                db.get_release_defect_status,
                version_lbl,
                pg_session,
                int(release_selector)
            )
            query_time = (time.perf_counter_ns() - start_time) * pow(10, -6)
            logger.debug(f'Async Query Execution Time get_release_defect_status: {query_time:.3f} ms')

        if dff.shape[0] == 0:
            return [
                html.Tr(
                    className="no-data-row",
                    children=[
                        html.Td(
                            "No defect data found for selected criteria",
                            colSpan=18,
                            className="no-data-message"
                        )
                    ]
                )
            ], html.Span("No Defects"), html.Span("No Defects")

        # Apply filters asynchronously
        filtered_df = await asyncio.to_thread(apply_defect_filters, dff, custom_status, team_name)

        if filtered_df.shape[0] == 0:
            return [
                html.Tr(
                    className="filtered-empty-row",
                    children=[
                        html.Td(
                            "No defects match the selected filters",
                            colSpan=18,
                            className="filtered-empty-message"
                        )
                    ]
                )
            ], html.Span("No Matching Defects"), html.Span("No Matching Defects")

        # Process defect data asynchronously
        processed_data = await asyncio.to_thread(process_defect_data, filtered_df, schema_name, session_factory)

        return processed_data

    except Exception as e:
        logger.error(f"Error in show_defect_details_async: {e}")
        return [
            html.Tr(
                className="error-row",
                children=[
                    html.Td(
                        f"Error loading defect data: {str(e)}",
                        colSpan=18,
                        className="error-message"
                    )
                ]
            )
        ], html.Span("Error"), html.Span("Error")


def apply_defect_filters(df: pd.DataFrame, custom_status: str, team_name: str) -> pd.DataFrame:
    """
    Apply filters to defect data
    """
    # Apply status filter
    if custom_status and 'ALL' not in custom_status:
        df = df[df['statusclass'] == custom_status].copy()

    # Apply priority filter
    if team_name == 'FILTER_HIGH':
        df = df[df['priority'].isin(['Show Stopper', 'Critical', 'High'])].copy()
    elif team_name == 'FILTER_OUT_HIGH':
        df = df[df['priority'].isin(['Medium', 'Low'])].copy()

    return df


def process_defect_data(df: pd.DataFrame, schema_name: str, session_factory) -> Tuple[List[html.Tr], html.Span, html.Span]:
    """
    Process defect data and generate table rows and statistics
    """
    # Remove done issues and unnecessary columns
    df = df[~(df['statusCategory'] == 'Done')].copy()
    df.drop(columns=['statusCategory', 'statusclass', 'reopen', 'clientjira'], inplace=True, errors='ignore')

    # Process severity and priority
    df['severity'] = df['severity'].fillna("NA")
    list_ordering = ['Show Stopper', 'Critical', 'Urgent', 'High', 'Medium', 'Low', 'NA']

    df['severity'] = pd.Categorical(df['severity'], categories=list_ordering, ordered=True)
    df.sort_values(by=['severity', 'aging'], ascending=[True, False], inplace=True)
    df['severity'] = df['severity'].astype('str')

    df['priority'] = pd.Categorical(df['priority'], categories=list_ordering, ordered=True)
    df.sort_values(by=['priority', 'aging'], ascending=[True, False], inplace=True)
    df['priority'] = df['priority'].astype('str')

    # Generate statistics
    df_priority = df[['priority', 'key']].groupby(['priority']).agg({'key': 'count'}).reset_index()
    df_severity = df[['severity', 'key']].groupby(['severity'], observed=True).agg({'key': 'count'}).reset_index()
    df_severity = df_severity[df_severity['key'] != 0]

    # Add test case mapping if available
    try:
        import platform
        if platform.uname().node == 'dashboard':
            # Add test case mapping for production environment
            session_factory.config_schema.override({'schema_name': 'mssql'})
            with session_factory.db_conn_provider().create_engine() as connection:
                with connection.connect() as conn:
                    ms_rows = db.get_test_case_mapping(conn)
                    df_ms = pd.DataFrame(ms_rows)
                    df = df.merge(df_ms, left_on="Test Case#", right_on="TC_Number", how="left")
                    df.drop(columns=['TC_Number'], inplace=True, errors='ignore')
        else:
            df['Test_name'] = ''
            df['Automationstatus'] = ''
    except Exception as e:
        logger.warning(f"Could not add test case mapping: {e}")
        df['Test_name'] = ''
        df['Automationstatus'] = ''

    # Apply formatting
    for col in ['key']:
        if col in df.columns:
            df[col] = df[col].apply(apply_href)

    for col in df.columns:
        df[col] = df[col].apply(apply_td)

    # Generate alert strings
    alert_string = "No Defects"
    alert_string_severity = "No Defects"

    if df_priority.shape[0] > 0:
        df_priority = df_priority.sort_values(by="priority", key=sort_priority)
        df_priority['priority'] = df_priority.apply(lambda x: apply_acronymn(x['priority']), axis=1)
        alert_string = ", ".join(
            (df_priority['priority'] + ": " + df_priority['key'].astype(str)).values
        )

    if df_severity.shape[0] > 0:
        df_severity['severity'] = df_severity.apply(lambda x: apply_acronymn(x['severity']), axis=1)
        alert_string_severity = ", ".join(
            (df_severity['severity'] + ": " + df_severity['key'].astype(str)).values
        )

    # Generate table rows
    table_rows = [
        html.Tr(
            className="defect-row",
            children=row
        ) for row in df.values.tolist()
    ]

    return table_rows, html.Span(alert_string), html.Span(alert_string_severity)
