"""
core dash_app definition, CSS loading, import and attach layout.
Has the required wiring for Flask Login and flask principal
"""
from __future__ import annotations

import ipaddress
import json
import os
import sys

import dash
import requests
# import logging
# import flask

from flask import Flask, session, g, url_for, redirect, request, current_app
# from flask_caching import Cache
# from flask_caching.backends.rediscache import Redis<PERSON>ache
from flask_caching.backends import Redis<PERSON>ache
from flask_session import Session
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_user

from flask_principal import Principal, <PERSON><PERSON><PERSON>, User<PERSON>eed, identity_loaded, Permission
# from flask_socketio import SocketIO, emit
#from dash.long_callback import CeleryLongCallbackManager, DiskcacheLongCallbackManager
from dash import CeleryManager, DiskcacheManager
from celery import Celery
import diskcache
from uuid import uuid4

from datetime import timedelta, datetime, timezone
from redis import from_url, RedisError
from collections import defaultdict
from werkzeug.middleware.proxy_fix import ProxyFix
from data import <PERSON><PERSON><PERSON><PERSON>, helper, get_from_db as db

from data.decorators import cache
from data.helper import UserAuth, is_server_accessible, get_network_info


external_stylesheets = [
    {
        'href': "https://cdn.jsdelivr.net/npm/bootstrap@5.3.8/dist/css/bootstrap.min.css",
        # SRI hash to ensure the integrity of the fetched resource
        'integrity': "sha384-sRIl4kxILFvY47J16cr9ZwB07vP4J8+LH7qKQnuqkuIAvNWLzeN8tE5YBujZqJLB",
        'crossorigin': "anonymous",
        'rel': 'stylesheet',
    },
#     {
#         'src': 'https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap',
#         'rel': 'stylesheet',
#         'crossorigin': 'anonymous'
#     },
# {
#         'src': 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
#         'rel': 'stylesheet',
#         'crossorigin': 'anonymous'
#     },
#     {
#         'href': 'https://fonts.googleapis.com/css2?family=Roboto&display=swap',
#         'rel': 'stylesheet',
#         'crossorigin': 'anonymous'
#     },
    {
        'href': 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
        'rel': 'stylesheet',
    },
    # {
    #     'href': 'https://unpkg.com/aos@2.3.1/dist/aos.css',
    #     'rel': 'stylesheet',
    # },
    {
        'href': 'https://unpkg.com/tablefilter@latest/dist/tablefilter/style/tablefilter.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css',
        'rel': 'stylesheet',
    },
    # This is used by dash mantime component
    {
        "href": "https://unpkg.com/@mantine/dates@7/styles.css",
        'rel': 'stylesheet',
    },
    {
        "href": "https://unpkg.com/@mantine/code-highlight@7/styles.css",
        'rel': 'stylesheet',
    },
    {
        "href": "https://unpkg.com/@mantine/charts@7/styles.css",
        'rel': 'stylesheet',
    },
    # Start Edit
    {
        "href": "https://unpkg.com/@mantine/carousel@7/styles.css",
        'rel': 'stylesheet',
    },
    {
        "href": "https://unpkg.com/@mantine/notifications@7/styles.css",
        'rel': 'stylesheet',
    },
    {
        "href": "https://unpkg.com/@mantine/nprogress@7/styles.css",
        'rel': 'stylesheet',
    },
]

# Add all the external javascripts here
external_scripts = [

    {
        'src': 'https://kit.fontawesome.com/4e20f97c7e.js',
        'crossorigin': 'anonymous',
        'async': True,
    },
{
    'src': "https://cdn.jsdelivr.net/npm/bootstrap@5.3.8/dist/js/bootstrap.bundle.min.js",
    'integrity': "sha384-FKyoEForCGlyvwx9Hj09JcYn3nv7wiPVlz7YYwJrWVcXK/BmnVDxM+D2scQbITxI",
'crossorigin': 'anonymous',

 },
    {
        'src': 'https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js',
        'crossorigin': 'anonymous',
    },
    # {
    #     'src': 'https://unpkg.com/aos@2.3.1/dist/aos.js',
    #     'crossorigin': 'anonymous',
    #     'defer': True,
    # },
    # {
    #     'src': "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js",
    #     'crossorigin': 'anonymous'
    # },
    # {
    #     'src': "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js",
    #     'crossorigin': 'anonymous'
    # },
    # {
    #     'src': "https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js",
    #     'crossorigin': 'anonymous'
    # },
    # {
    #     'src': "https://code.angularjs.org/1.7.9/angular.min.js",
    #     'async': True,
    # },
    {
        'src': "https://unpkg.com/tablefilter@latest/dist/tablefilter/tablefilter.js",
        'async': True,
    },
    {
        'src': "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js",
        'defer': True,
    },
    {
        'src': "https://cdn.jsdelivr.net/npm/marked/marked.min.js",
        'defer': True,
    },
    {
        'src': "https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js",
        'defer': True,
    },
    {
        'src': "https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js",
        'crossorigin': 'anonymous',
        'defer': True,
    }
]

# class UserAuth(User, UserMixin):


server = Flask(__name__)
# socketio = SocketIO(logger=False, engineio_logger=False)
# socketio.init_app(server)


# @socketio.on('welcome')
# def handle_message(message):
#     print(str(message))


# @socketio.on('connect')
# def handle_connect():
#     emit('my response', {'data': 'Connected'})
#
#
# @socketio.on('disconnect')
# def handle_disconnect():
#     print(f'Client disconnected: {current_user}')


# Set up Flask-Principal
principals = Principal(server)

superuser_role = RoleNeed('superuser')
admin_role = RoleNeed('admin')
pmo_role = RoleNeed('pmo')
user_role = RoleNeed('user')

roles_and_permissions = defaultdict(lambda: [user_role])

superuser_permission = Permission(superuser_role)
admin_permission = Permission(admin_role)
pmo_permission = Permission(pmo_role)
user_permission = Permission(user_role)

# Assign roles and permissions to users
roles_and_permissions['superuser'] = [superuser_role]
roles_and_permissions['admin'] = [admin_role, superuser_role]
roles_and_permissions['pmo'] = [pmo_role]
roles_and_permissions['user'] = [user_role]
roles_and_permissions['<EMAIL>'] = [superuser_role, pmo_role]
# roles_and_permissions['<EMAIL>'] = [pmo_role]
roles_and_permissions['<EMAIL>'] = [pmo_role]
roles_and_permissions['<EMAIL>'] = [pmo_role]
roles_and_permissions['<EMAIL>'] = [pmo_role]


# Create a custom authorization function
def has_role(role):
    def wrapper(f):
        def wrapped(*args, **kwargs):
            with server.app_context():
                identity = g.identity
                if identity.can(role):
                    return f(*args, **kwargs)
                return 'You do not have the required role to view this page.'

        return wrapped

    return wrapper


@identity_loaded.connect_via(server)
def on_identity_loaded(sender, identity):
    # Set the user's role based on their status
    identity.user = current_user
    if current_user.is_authenticated:
        identity.provides.add(UserNeed(current_user.id))
        if current_user.is_admin:
            identity.provides.add(admin_role)
        elif current_user.is_pmo:
            identity.provides.add(pmo_role)
        elif current_user.is_superuser:
            identity.provides.add(superuser_role)
        else:
            identity.provides.add(user_role)


server.config.update(SECRET_KEY=os.getenv('SESSION_TOKEN', os.urandom(12)))
server.config.from_object(__name__)
# Configure Redis for storing the session data on the server-side
server.config['SESSION_TYPE'] = 'redis'
server.config['SESSION_PERMANENT'] = True
server.config['SESSION_USE_SIGNER'] = True
server.config['SESSION_REDIS'] = from_url('redis://localhost:6379')

# Set the session timeout to 30 minutes (1800 seconds)
server.config['SESSION_COOKIE_MAX_AGE'] = 1800
# protects the contents of cookies from being read with JavaScript.
server.config['SESSION_COOKIE_SECURE'] = True
server.config['SESSION_COOKIE_HTTPONLY'] = True
server.config['SESSION_COOKIE_SAMESITE'] = "Lax"
server.permanent_session_lifetime = timedelta(minutes=10)
server.config['SESSION_SERIALIZATION_FORMAT'] = 'msgpack'
server.config['SESSION_KEY_PREFIX'] = 'jira'

Session(server)


# Reference: https://flask-session.readthedocs.io/en/latest/config.html
@server.errorhandler(RedisError)
def handle_redis_error(error):
    server.logger.error(f"Redis error encountered: {error}")
    return "A problem occurred with our Redis service. Please try again later.", 500


# This section is needed for url_for("foo", _external=True) to automatically
# generate http scheme when this sample is running on localhost,
# and to generate https scheme when it is deployed behind reversed proxy.
# See also https://flask.palletsprojects.com/en/1.0.x/deploying/wsgi-standalone/#proxy-setups

server.wsgi_app = ProxyFix(server.wsgi_app, x_proto=1, x_host=1)

# Cookie “session” does not have a proper “SameSite” attribute value.
# server.config['SESSION_COOKIE_SAMESITE'] = 'None'
# session_new = Session()
# session_new.init_app(server)

# server = dash_app.server
# dash_app.server.config.from_object(app_config)

login_manager = LoginManager()
login_manager.init_app(server)
login_manager.session_protection = "strong"
login_manager.login_view = 'login'
login_manager.refresh_view = 'relogin'
login_manager.needs_refresh_message = u"Session timeout, please re-login"
login_manager.needs_refresh_message_category = "info"

# class User(UserMixin):
#     def __init__(self, username):
#         self.id = username


if os.name == "nt":
    launch_uid = uuid4()
    disk_cache = diskcache.Cache("./cache")
    # long_callback_manager = DiskcacheLongCallbackManager(disk_cache, cache_by=[lambda: launch_uid], expire=60)
    background_callback_manager = DiskcacheManager(disk_cache, cache_by=[lambda: launch_uid], expire=300)
else:
    from custom_container import AppContainer

    launch_uid = uuid4()
    container = AppContainer()
    celery_app = Celery(
        __name__, broker="redis://localhost:6379/1",
        backend="redis://localhost:6379/2"
    )
    # celery_app.container.wire(["callbacks"])
    # long_callback_manager = CeleryLongCallbackManager(celery_app, cache_by=[lambda: launch_uid], expire=300)
    background_callback_manager = CeleryManager(
        celery_app, cache_by=[lambda: launch_uid], expire=300
    )

dash_app = dash.Dash(__name__,
                     url_base_pathname=os.getenv('DASH_BASE_PATH', '/'),
                     suppress_callback_exceptions=True,
                     external_scripts=external_scripts,
                     external_stylesheets=external_stylesheets,
                     update_title='Loading...',
                     meta_tags=[
                         {
                             'http-equiv': 'X-UA-Compatible',
                             'content': 'IE=edge'
                         },
                         {
                             'name': 'viewport',
                             'content': 'width=device-width, initial-scale=1'
                         }
                     ],
                     title='JIRA Dashboard',
                     assets_ignore='.*ignore.*',
                     # long_callback_manager=long_callback_manager,
                     background_callback_manager=background_callback_manager,
                     server=server,
                     eager_loading=False,
                     compress=True,
                     add_log_handler=True
                     )

dash_app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        <!-- Preload critical fonts -->
        <!-- Just use Inter for everything -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" media="print" onload="this.media='all'">
        {%css%}
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                const links = document.querySelectorAll('link[rel=stylesheet][media=print]');
                links.forEach(link => link.media = 'all');
            });
        </script>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
        <script src="https://kit.fontawesome.com/4e20f97c7e.js" crossorigin="anonymous" defer></script>
        <!-- Add other scripts here if needed -->
    </body>
</html>
'''

cache.init_app(dash_app.server, config={
    "DEBUG": True,
    'CACHE_TYPE': RedisCache.__name__,

    'CACHE_REDIS_URL': os.environ.get('REDIS_URL', ''),
    "CACHE_DEFAULT_TIMEOUT": 600
})


@login_manager.user_loader
def load_user(username):
    """ This function loads the user by user id. Typically this looks up the user from a user database.
        We won't be registering or looking up users in this example, since we'll just login using API Key.
        So we'll simply return a User object with the passed in username.
    """
    # return User(username)
    return UserAuth(username, session['displayName'])


with server.app_context():
    from pykeepass import PyKeePass as PyKP

    print(f'WERKZEUG_RUN_MAIN = {os.getenv("WERKZEUG_RUN_MAIN")}')
    # print(CreateTable(Versions.__table__))
    for env_variables in ['DATABASE_PATH', 'MASTER_KEYFILE', 'TWO_FA_ENABLED', 'SESSION_TOKEN']:

        if os.getenv(env_variables) is None:
            print(f'{env_variables} is not set')
        else:
            print(f'{env_variables} = {os.getenv(env_variables)}')

        ref = PyKP(
            filename=os.getenv('DATABASE_PATH'),
            keyfile=os.getenv('MASTER_KEYFILE')
        )

        entry = ref.find_entries(title='JIRA_RO', first=True)

        server_name = entry.get_custom_property('DB_SERVER_NAME')
        server_port = entry.get_custom_property('DB_SERVER_RO_PORT')

        if not is_server_accessible(server_name, int(server_port)):
            print(f"{server_name}:{server_port} is not accessible. Abort!!!")
            raise SystemExit

        if not is_server_accessible('localhost', port=6379):
            print(f'redis instance is not available. Abort!!!')
            raise SystemExit

        network_details: dict = get_network_info()
        # print("Available interfaces:")
        # for interface in network_details.keys():
        #     print(f"  {interface}")

        # Then use the correct interface name
        if network_details:
            # Use the first available interface or specify the correct one
            interface_name = list(network_details.keys())[0]  # or specify manually
            ip_address, subnet_mask = network_details[interface_name]


        network_id = ipaddress.ip_network(f"{ip_address}/{subnet_mask}", strict=False)
        if network_id.network_address in ['**********', '**********']:
            entry = ref.find_entries(title='ISC PM DB', first=True)
            ms_server_name = entry.url
            if not is_server_accessible(ms_server_name, port=1433):
                print(f'MSSQL Server instance is not available. Abort!!!')
                raise SystemExit




# @dash_app.server.before_first_request
# def before_first_request():
# try:
#     with dash_app.server.app_context():
#         my_logger = logging.getLogger("start_up")
#
#         log_levels = {
#             logging.CRITICAL: 'CRITICAL',
#             logging.ERROR: 'ERROR',
#             logging.WARNING: 'WARNING',
#             logging.INFO: 'INFO',
#             logging.DEBUG: 'DEBUG',
#             logging.NOTSET: 'NOT SET'
#         }
#
#         # Get the logger for the Dash application
#         print(f'WERKZEUG_RUN_MAIN = {os.getenv("WERKZEUG_RUN_MAIN")}')
#         dash_logger = logging.getLogger('dash')
#         print(f'dash_logger level: {dash_logger.level} = {log_levels[dash_logger.level]}')
#
#         # Get the logger for the Flask application
#         flask_logger = logging.getLogger('flask.dash_app.server')
#         print(f'flask_logger level: {flask_logger.level} = {log_levels[flask_logger.level]}')
#         flask_logger = flask.current_app.logger
#         print(f'flask_logger level: {flask.current_app.logger.level} = {log_levels[flask.current_app.logger.level]}')
#
#         flask.current_app.logger.info("I am logging message")
#         flask_logger.info("flask: Logging message via logger")
#         werkzeug = logging.getLogger('werkzeug')
#         print(f'werkzeug level: {werkzeug.level} = {log_levels[werkzeug.level]}')
#
#         my_logger.setLevel(logging.DEBUG)
#         # Create a console handler and set its level to DEBUG
#         console_handler = logging.StreamHandler()
#         console_handler.setLevel(logging.DEBUG)
#
#         # Create a formatter and attach it to the handler
#         formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#         console_handler.setFormatter(formatter)
#
#         # Add the handler to the logger
#         my_logger.addHandler(console_handler)
#
#         my_logger.debug(f"Running pre-checks....")
#         my_logger.debug(f"1. Checking environment variables")
#
#         for env_variables in ['DATABASE_PATH', 'MASTER_KEYFILE', 'TWO_FA_ENABLED', 'SESSION_TOKEN']:
#             if os.getenv(env_variables) is None:
#                 my_logger.info(f'{env_variables} is not set')
#             else:
#                 my_logger.debug(f'{env_variables} = {os.getenv(env_variables)}')
#
#         my_logger.debug("2. Check if directories exist")
#         required_dirs = [f'data/qr_code']
#         for directory in required_dirs:
#             if not os.path.isdir(directory):
#                 my_logger.debug(f"{required_dirs} Not Found")
#             else:
#                 my_logger.debug(f"{required_dirs} Exists")
# except Exception as exc:
#     dash_app.server.logger.error("Exception encountered!", exc_info=exc)
#     exit(1)


@server.before_request
def check_inactivity():
    # Skip for static assets and API endpoints
    if request.path.startswith(('/assets/', '/static/', '/_dash', '/favicon.ico')):
        return

    # Only process for actual page requests
    if not session.get("logged_in") or session.get("logout_reason"):
        return

    # Only check for authenticated users
    if not current_user.is_authenticated:
        return

    session.modified = True
    now = datetime.now(timezone.utc)

    # Only check inactivity if user is logged in and not already logged out
    if session.get("logged_in") and not session.get("logout_reason"):
        last_activity = session.get("last_activity")

        if last_activity:
            try:
                # Always expect an ISO string
                last_activity_time = datetime.fromisoformat(last_activity)
                # If tzinfo is missing, assume UTC
                if last_activity_time.tzinfo is None:
                    last_activity_time = last_activity_time.replace(tzinfo=timezone.utc)

                # Compare against configured lifetime (a timedelta)
                lifetime = current_app.config["PERMANENT_SESSION_LIFETIME"]
                if (now - last_activity_time) > lifetime:
                    session["logged_in"] = False
                    session["logout_reason"] = "inactivity"
            except Exception:
                # If parsing fails, treat as expired
                session["logged_in"] = False
                session["logout_reason"] = "inactivity"

        # Always refresh activity timestamp while logged in
        session["last_activity"] = now.isoformat()
    else:
        # User is not logged in or already logged out
        session.pop("last_activity", None)


import msal

msal_map = {}
for title in ['CLIENT_ID', 'TENANT_ID', 'CLIENT_SECRET']:
    entry = ref.find_entries(title=title, first=True)
    # exec(f'{title} = {entry.password}')
    msal_map[title] = entry.password
AUTHORITY = f"https://login.microsoftonline.com/{msal_map['TENANT_ID']}"
SCOPES = ["User.Read"]
SESSION_TYPE = "filesystem"

msal_cache = msal.SerializableTokenCache()
msal_app = msal.ConfidentialClientApplication(
    client_id=msal_map['CLIENT_ID'], authority=AUTHORITY, client_credential=msal_map['CLIENT_SECRET']
)



@server.route("/new_reg")
def new_registration():
    my_logger = MyLogger().get_logger()

    if current_user.is_authenticated:
        my_logger.info("User is authenticated")
        return "Authenticated"

    # Get the redirect URI
    redirect_uri = url_for('get_token_msal', _external=True)
    auth_code_flow_dict = msal_app.initiate_auth_code_flow(
        scopes=SCOPES,
        redirect_uri=redirect_uri
    )
    auth_url = auth_code_flow_dict['auth_uri']
    session['auth_code_flow_dict'] = auth_code_flow_dict
    my_logger.info(f'{auth_code_flow_dict}')
    my_logger.info(f"value in session: {session['auth_code_flow_dict']}")
    # Exchange authorization code for access token

    return redirect(auth_url)

ALLOWED_DOMAINS = ["graph.microsoft.com"]

def is_allowed_domain(url):
    from urllib.parse import urlparse
    domain = urlparse(url).netloc
    return any(allowed_domain in domain for allowed_domain in ALLOWED_DOMAINS)


@server.route("/getAToken")
def get_token_msal():
    funcname = sys._getframe().f_code.co_name
    my_logger = MyLogger().get_logger()
    username = session['email_id']
    # Get the authorization response
    code = request.args.get('code')
    code_flow = session['auth_code_flow_dict']
    my_logger.debug(f'code = {code}')
    my_logger.debug("Calling acquire_token_by_auth_code_flow")
    result = None
    try:
        result = msal_app.acquire_token_by_auth_code_flow(
            auth_code_flow=code_flow,
            auth_response=request.args.to_dict()
        )
        my_logger.info(f'result = {result}')
    except ValueError:
        pass
    except Exception as e:
        my_logger.info("Failed")
        my_logger.debug(f'{funcname} exception: {e}')
        user = UserAuth(username=username, displayName="")
        login_user(user)
        return redirect("/")
    my_logger.info(f'result = {result}')

    if "access_token" in result:
        my_logger.info("Token procured")
        my_logger.info("Access Token")
        my_logger.debug(f"{result['access_token']}")

        # Decode the JWT payload
        # payload = jwt.decode(result['access_token'], verify=False)

        # Get the expiration time from the payload
        # exp_time = payload['exp']

        # Convert the expiration time to a human-readable format
        # exp_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(exp_time))
        # exp_result = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result['exp']))
        # my_logger.debug(f'Expiration time in result: {exp_result}')
        # my_logger.debug(f'Expiration time from token: {exp_time_str}')

        my_logger.info(f'getting otp code for {username}')
        token_string, qr_image_file = helper.token_2fa(username)
        my_logger.debug(f'Token = {token_string}')

        token_url = f"https://login.microsoftonline.com/{msal_map['TENANT_ID']}/oauth2/v2.0/token"
        resource = 'https://graph.microsoft.com'

        response = requests.post(
            token_url,
            data={
                "client_id": msal_map['CLIENT_ID'],
                "client_secret": msal_map['CLIENT_SECRET'],
                "grant_type": "client_credentials",
                "scope": resource + "/.default"
            }
        )
        my_logger.info(f'response of post request: {response.status_code}')

        headers = {
            "Authorization": "Bearer " + result['access_token'],
            "Content-Type": "application/json"
        }

        message = {
            "message": {
                "subject": "New Registration Code",
                "body": {
                    "contentType": "HTML",
                    "content": "Use the registration code " + token_string +
                               " or scan the attached QR code in Google Authenticator App"
                },
                "toRecipients": [
                    {
                        "emailAddress": {
                            "address": f"{username}"
                        }
                    }
                ],
                "ccRecipients": [
                    {
                        "emailAddress": {
                            "address": f"{username}"
                        }
                    }
                ],
                # include attachments
                'attachments': [
                    qr_image_file
                ]
            },
            "saveToSentItems": "false"
        }

        # Send the email
        my_logger.debug("Message formed is")
        my_logger.debug(f'{json.dumps(message)}')

        # The endpoint to which the request is being sent
        endpoint = "https://graph.microsoft.com/v1.0/me/sendMail"
        # Validate the endpoint
        if not is_allowed_domain(endpoint):
            raise ValueError("Attempt to access a non-allowed domain.")

        response = requests.post(
            endpoint,
            headers=headers,
            data=json.dumps(message),
            timeout=30
        )
        my_logger.debug(f"Response of send mail request: {response.status_code}")
        my_logger.debug(f'Token = {token_string}')
        # Check the response status code
        if response.status_code in ([200, 202]):
            my_logger.debug("Email sent successfully.")
            # update user row with generated token
            db.update_user_otp_token(username, token_string)
            session['2fa_enabled'] = True
        else:
            my_logger.debug("Failed to send email. Response status code: " + str(response.status_code))
            my_logger.debug(f'{response.json()}')
        return redirect(f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa")
    else:
        my_logger.debug("Failed to get token")
