import base64
import os
from dash import html, dcc
from dash_iconify import DashIconify

# import dash_daq as daq
# import dash_mantine_components as dmc
# from dash_iconify import DashIconify

from app import current_user, superuser_permission
from data import helper
import dash_svg as svg

def generate_layout(project_list: tuple = (), project_index: int = 1):
    path_name = os.getenv('DASH_BASE_PATH', '/')
    li_children = [
        html.Li(children=[
            html.A(
                children=[html.Span(children=['Home'])],
                href=f"{path_name}"
            ),
            # html.Div(id="socket-connection")
        ], className="top-level-link"),
    ]

    if current_user and current_user.is_authenticated:
        if project_list:
            # if len(project_list) > 5:
            #     parse_projects = project_list[project_index - 1:5 * project_index]
            # else:
            #     parse_projects = project_list
            for key in project_list:
                li_children.append(
                    html.Li(children=[
                        html.A(children=[html.Span(children=[f'{key.upper()}'])], className='mega-menu'),
                        html.Div(children=[
                            html.Div(children=[
                                html.Div(children=[
                                    html.H2(children=["Dashboard"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        html.Li(children=[
                                            html.A("By sprint", href=f"{path_name}{key}/overview", )
                                        ]),
                                        html.Li(children=[
                                            html.A("By release", href=f"{path_name}{key}/version", )
                                        ]),
                                        html.Li(children=[
                                            html.A("SVN Details", href=f"{path_name}{key}/svn", )
                                        ]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                # html.Div(children=[
                                #     html.H2(children=["Time sheets"], className="sub-menu-head"),
                                #     html.Ul(
                                #         children=[
                                #             html.Li(children=[html.A("Month Wise", href=f"/{key}/monthlyts", )])],
                                #         className="sub-menu-lists"),
                                #     html.Ul(
                                #         children=[
                                #             html.Li(children=[html.A("By Resource", href=f"/{key}/resourcets", )])],
                                #         className="sub-menu-lists"),
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                html.Div(children=[
                                    html.H2(children=["Project Specific"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        # html.Li(children=[
                                        #     html.A("Backlog Refinement", href=f"/{key}/refinement", )
                                        # ]),
                                        html.Li(children=[
                                            html.A("SQL Code Review", href=f"{path_name}{key}/SQL Code Review", )
                                        ]),
                                        html.Li(children=[
                                            html.A("WD Timesheet", href=f"{path_name}plat/wdts", )
                                        ]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                html.Div(children=[
                                    html.H2(children=["Reports"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        html.Li(
                                            children=[html.A("Description", href=f"{path_name}{key}/description", )]
                                        ),
                                        html.Li(children=[html.A("Aging", href=f"{path_name}{key}/issueaging", )]),
                                        html.Li(children=[html.A("Compliance", href=f"{path_name}{key}/compliance", )]),
                                        html.Li(children=[html.A("Time Log", href=f"{path_name}{key}/timelog", )]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                # html.Div(children=[
                                #     html.H2(children=["Reports"], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Unassigned", href=f"/{key}/unassigned", )
                                #         ])
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                # Start New menu

                                # End New menu
                                # html.Div(children=[
                                #     html.H2(children=["Misc."], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Duplicate JIRA", href=f"/{key}/duplicate", )
                                #         ]),
                                #         html.Li(children=[
                                #             html.A("Initiative Attributes", href=f"/{key}/initattr", )
                                #         ]),
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                # Updates to team can be handled via flask-admin interface
                                # html.Div(children=[
                                #     html.H2(children=["Admin"], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Update Initiative Attributes", href=f"/{key}/updateinitattribs", )
                                #         ]),
                                #         html.Li(children=[
                                #             html.A("Update Teams", href=f"/{key}/updateteams", )
                                #         ]),
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4")
                                # html.Div(plat_li_children),
                            ], className="row"),
                        ], className="sub-menu-block")
                    ], className="top-level-link"),
                )

    # if current_user and not current_user.is_anonymous \
    #         and current_user.id in roles_and_permissions \
    #         and pmo_role in roles_and_permissions[current_user.id]:

    if current_user.is_authenticated and (current_user.is_pmo or current_user.is_superuser):
        li_children.append(
            html.Li(
                children=[
                    # html.A(children=[html.Span(children=['Clocks'])],href="/clocks"),], className="top-level-link"),
                    html.A(children=[html.Span(children=['PMO'])], className="mega-menu"),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.H2(children=["Data Management"], className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Admin", href=f"/admin", target="_blank"),
                                                    ]),
                                                    html.Li(children=[
                                                        html.A("Request Tracker", href=f"{path_name}public/rt"),
                                                    ]),
                                                ], className="sub-menu-lists"
                                            )

                                        ], className="col-md-4 col-lg-4 col-sm-4"
                                    ),
                                    # New Add
                                    html.Div(
                                        children=[
                                            html.H2(children=["Bulk Data Creation"], className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Teams - Plat", href=f"{path_name}plat/teams"),
                                                    ]),
                                                    html.Li(children=[
                                                        html.A(
                                                            "Upload Request Tracker File",
                                                            href=f"{path_name}public/requesttracker"
                                                        ),
                                                    ]),
                                                ], className="sub-menu-lists"
                                            )

                                        ], className="col-md-4 col-lg-4 col-sm-4"
                                    ),
                                    # End New Add
                                    html.Div(children=[
                                        html.H2(children=["Batch Job"], className="sub-menu-head"),
                                        html.Ul(children=[
                                            html.Li(children=[
                                                html.A("Update Estimates", href=f"{path_name}plat/estimates", )
                                            ]),
                                            html.Li(children=[
                                                html.A("Transition Issues", href=f"{path_name}plat/transition", )
                                            ]),
                                            html.Li(children=[
                                                html.A("Create CTs", href=f"{path_name}plat/coretrack", )
                                            ]),
                                            html.Li(children=[
                                                html.A("SVN Info", href=f"{path_name}plat/svndetails", )
                                            ]),

                                        ], className="sub-menu-lists")
                                    ], className="col-md-4 col-lg-4 col-sm-4"),
                                    html.Div(
                                        children=[
                                            html.H2("Status Report", className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Weekly Status Report - Plat",
                                                               href=f"{path_name}plat/weekly_report"),
                                                    ])
                                                ], className="sub-menu-lists"
                                            ),
                                        ]
                                    ),
                                    html.Div(
                                        children=[
                                            html.H2("Release Notes", className="sub-menu-head"),
                                            # html.Li(children=[
                                            #     html.A("Release Notes", href=f"/{key}/releasenotes", )
                                            # ]),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Plat Release Notes",
                                                               href=f"{path_name}plat/releasenotes", )
                                                    ]),
                                                ], className="sub-menu-lists"
                                            ),
                                        ]
                                    ),
                                ], className='row')
                        ], className="sub-menu-block"
                    )

                ],
                className="top-level-link"
            )
        )

    # Playground menu
    if current_user.is_authenticated and superuser_permission.can():
        li_children.append(
            html.Li(
                children=[
                    # html.A(children=[html.Span(children=['Clocks'])],href="/clocks"),], className="top-level-link"),
                    html.A(children=[html.Span(children=['R&D'])], className="mega-menu"),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(children=[
                                        html.H2(children=["C4 Diagrams"], className="sub-menu-head"),
                                        html.Ul(
                                            children=[
                                                helper.generate_header_leaf_node("Credit Card",
                                                                                 f"{path_name}r&d/creditcard"),
                                                helper.generate_header_leaf_node("Mortgages",
                                                                                 f"{path_name}r&d/mortgages"),
                                            ],
                                            className="sub-menu-lists"
                                        )

                                    ], className="col-md-4 col-lg-4 col-sm-4"),
                                    html.Div(children=[
                                        html.H2(children=["Research"], className="sub-menu-head"),
                                        html.Ul(
                                            children=[
                                                helper.generate_header_leaf_node("chatGPT", f"{path_name}r&d/chatgpt"),
                                                helper.generate_header_leaf_node("Service Availability",
                                                                                 f"{path_name}r&d/service"),
                                            ],
                                            className="sub-menu-lists"
                                        )

                                    ], className="col-md-4 col-lg-4 col-sm-4"),

                                ], className='row')
                        ], className="sub-menu-block"
                    )

                ],
                className="top-level-link"
            )
        )
    # End Playground

    return li_children


# Save your SVG content to a file or encode it as base64
svg_content = '''<svg width="60" height="60" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="120" height="120" rx="20" fill="#1890FF"/>
    <path d="M30 60C30 43.4315 43.4315 30 60 30V30C76.5685 30 90 43.4315 90 60V60C90 76.5685 76.5685 90 60 90V90C43.4315 90 30 76.5685 30 60V60Z" stroke="white" stroke-width="8"/>
    <path d="M60 45V75" stroke="white" stroke-width="8" stroke-linecap="round"/>
    <path d="M45 60H75" stroke="white" stroke-width="8" stroke-linecap="round"/>
</svg>'''

# Encode SVG as base64
svg_base64 = base64.b64encode(svg_content.encode()).decode()
svg_data_uri = f"data:image/svg+xml;base64,{svg_base64}"


# Modern Login screen
login = html.Div(
    children=[
        dcc.Location(id='url_login', refresh=True),
        html.Div(id="notifications-container"),
        html.Div(id="id-login-username", className="hidden"),

        # Modern Login Container
        html.Div(
            className="modern-login-container",
            children=[
                # Login Header
                html.Div(
                    className="modern-login-header",
                    children=[
                        html.H1("Welcome Back", className="modern-login-title"),
                        html.P("Sign in to your account", className="modern-login-subtitle")
                    ]
                ),

                # Login Form
                html.Div(
                    className="modern-login-form",
                    children=[
                        # Email Input Group
                        html.Div(
                            className="modern-input-group",
                            children=[
                                html.Label("Email Address", className="modern-input-label", htmlFor="id-login-main"),
                                html.Div(
                                    className="modern-input-field",
                                    children=[
                                        dcc.Input(
                                            id="id-login-main",
                                            type="email",
                                            placeholder="Enter your email address",
                                            className="modern-input",
                                            debounce=True,
                                            persistence=True,
                                            value='',
                                            autoComplete="email",
                                            required=True
                                        ),
                                        html.I(className="fa fa-envelope modern-input-icon")
                                    ]
                                )
                            ]
                        ),

                        # Password Input Group
                        html.Div(
                            className="modern-input-group",
                            children=[
                                html.Label("Password", className="modern-input-label", htmlFor="id-login-passwd-main"),
                                html.Div(
                                    className="modern-input-field",
                                    children=[
                                        dcc.Input(
                                            id="id-login-passwd-main",
                                            type="password",
                                            placeholder="Enter your password",
                                            className="modern-input",
                                            debounce=True,
                                            persistence=True,
                                            value='',
                                            autoComplete="current-password",
                                            required=True
                                        ),
                                        html.I(className="fa fa-lock modern-input-icon"),
                                        html.Button(
                                            html.I(className="fa fa-eye"),
                                            id={"type": "password-toggle", "index": "main"},
                                            className="password-toggle",
                                            type="button",
                                            title="Toggle password visibility",
                                            **{"aria-label": "Show password"}
                                        )
                                    ]
                                )
                            ]
                        ),

                        # Login Button
                        html.Button(
                            "Sign In",
                            id="id-button-login-main",
                            className="modern-login-button",
                            n_clicks=0,
                            type="submit"
                        )
                    ]
                )
            ]
        )
    ],
    className="modern-login-page"
)

# Modern 2FA Login screen
login_2fa = html.Div(
    children=[
        # Hidden components for JavaScript functionality
        html.Div(
            children=[
                dcc.Input(id='in-component1', type='text', style={'display': 'none'}),
                html.Div(id="dash_app", style={'display': 'none'}),
                html.Div(id='out-component', style={'display': 'none'}),
            ]
        ),

        dcc.Location(id="url_login_2fa"),

        # Modern 2FA Container
        html.Div(
            className="modern-login-container modern-2fa-container",
            children=[
                # 2FA Header with Security Icon
                html.Div(
                    className="modern-login-header",
                    children=[
                        html.Div(
                            className="modern-2fa-icon",
                            children=[
                                html.I(className="fa fa-shield-alt")
                            ]
                        ),
                        html.H1("Two-Factor Authentication", className="modern-login-title"),
                        html.P("Enter the 6-digit code from your Google Authenticator app", className="modern-login-subtitle")
                    ]
                ),

                # 2FA Form
                html.Div(
                    className="modern-login-form",
                    children=[
                        # OTP Input Group
                        html.Div(
                            className="modern-input-group",
                            children=[
                                html.Label("Authentication Code", className="modern-input-label", htmlFor="id-input-token"),
                                html.Div(
                                    className="modern-input-field modern-otp-field",
                                    children=[
                                        dcc.Input(
                                            id="id-input-token",
                                            type="text",
                                            placeholder="000000",
                                            className="modern-input modern-otp-input",
                                            autoFocus=True,
                                            debounce=True,
                                            value='',
                                            maxLength=6,
                                            pattern="[0-9]+",
                                            n_submit=0,
                                            autoComplete="one-time-code",
                                            required=True
                                        ),
                                        html.I(className="fa fa-key modern-input-icon")
                                    ]
                                ),
                                html.Div(id="id-validate-otp-token", className="modern-validation-message")
                            ]
                        ),

                        # Instructions
                        html.Div(
                            id="id-instructions",
                            className="modern-2fa-instructions",
                            children=[
                                html.Div(
                                    className="modern-instruction-item",
                                    children=[
                                        html.I(className="fa fa-mobile-alt"),
                                        html.Span("Open your Google Authenticator app")
                                    ]
                                ),
                                html.Div(
                                    className="modern-instruction-item",
                                    children=[
                                        html.I(className="fa fa-search"),
                                        html.Span("Find your account entry")
                                    ]
                                ),
                                html.Div(
                                    className="modern-instruction-item",
                                    children=[
                                        html.I(className="fa fa-keyboard"),
                                        html.Span("Enter the 6-digit code above")
                                    ]
                                )
                            ]
                        ),

                        # Action Buttons
                        html.Div(
                            className="modern-2fa-buttons",
                            children=[
                                html.Button(
                                    "Verify Code",
                                    id="id-button-token",
                                    className="modern-login-button modern-2fa-primary-button",
                                    n_clicks=0,
                                    type="submit"
                                ),
                                html.Button(
                                    "Reset PIN",
                                    id="id-button-reset-pin",
                                    className="modern-2fa-secondary-button",
                                    n_clicks=0,
                                    type="button"
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    ],
    className="modern-login-page modern-2fa-page"
)

# Failed Login
failed = html.Div(
    [html.Div([html.H2('Log in Failed. Please try again.'),
               html.Br(),
               html.Div([login]),
               dcc.Link('Home', href='/')
               ])  # end div
     ])

# logout
logout = html.Div(
    [html.Div(html.H2('You have been logged out - Please login')),
     html.Br(),
     dcc.Link('Home', href='/login')
     ]
)


def html_header(session):
    image_element = html.Img(
                            src='data:image/png;base64,{}'.format(session.get('encoded_image', '')),
                            className="avatar",
                            id="id-logout-menu-test",
                            n_clicks=0
                        ) if session.get('encoded_image_status') == 200 else html.Img(
                            src=session['avatarUrls16x16'] if session.get('avatarUrls16x16') else None,
                            className="avatar",
                            id="id-logout-menu",
                            n_clicks=0,
                        )

    page_header = html.Header(
        children=[
            # daq.NumericInput(
            #     id="id-my-project-count", value=1,
            #     max=math.ceil(len(session['projects'] if session.get('projects') else ['plat']) / 5),
            #     size=50,
            #     className="ic format-numeric-input",
            #     style={'left': '15px', 'top': '15px', 'z-index': '20'}
            # ),
            # dcc.Store(
            #     id="id-session-project", storage_type="local",
            #     data=session['projects'] if session.get('projects') else ['plat']
            # ),
            html.Nav(children=[
                html.A(children=[
                    html.Span(className="line"),
                    html.Span(className="line"),
                    html.Span(className="line")
                ], className="ic menu", tabIndex="1",
                ),

                html.A(
                    children=[
                        image_element,
                    ],
                    className="ic close",
                    n_clicks=0,
                    href=f"{os.getenv('DASH_BASE_PATH', '/')}logout"
                ),
                # html.Ul(
                #     className="ic close popup-menu",
                #     id="dropdown-menu",
                #     children=[
                #         html.Li(
                #            children=[
                #                DashIconify(icon="gg:profile"),
                #                html.Span("Profile")
                #            ],
                #             className="format-li"
                #         ),
                #         html.Li(
                #             children=[
                #                 DashIconify(icon="uil:setting"),
                #                 html.Span("Settings")
                #             ],
                #         ),
                #         html.Li(
                #             children=[
                #                 DashIconify(icon="mynaui:logout"),
                #                 html.A(
                #                     children=[
                #                         "Logout"
                #                     ],
                #                     href=f"{os.getenv('DASH_BASE_PATH', '/')}logout"
                #                 )
                #             ]
                #
                #         ),
                #     ],
                # ),
                html.Ul(
                    children=generate_layout(session['projects'] if session.get('projects') else ['plat']),
                    className="main-nav",
                    id="id-regen-header"
                ),
            ], role='navigation')
        ],
        className='dark'
    )
    return page_header
