from dash import html, dcc

from data import get_from_db as db
from page_layout.page_contents import PageContent


class OverviewPageContent(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            options = db.get_board_id(pg_session, self.project)
            options_team = [{'label': i[0], 'value': i[0]} for i in db.get_team_names(pg_session)]

        value = None
        if self.project == "plat":
            value = 39
        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.Label("Board"),
                        dcc.Dropdown(
                            options=options,
                            className="dcc-dropdown",
                            clearable=False,
                            searchable=False,
                            id="id-sprint-board-values",
                            value=value
                        ),
                        html.Label("Sprints"),
                        html.I(className="line-divider"),
                        html.Label("Active"),
                        dcc.Loading(dcc.Dropdown(id="id-active-sprint", className="dcc-dropdown")),
                        html.Label("Future"),
                        dcc.Loading(dcc.Dropdown(id="id-future-sprint", className="dcc-dropdown")),
                        html.Label("Closed"),
                        dcc.Loading(dcc.Dropdown(id="id-closed-sprint", className="dcc-dropdown")),
                        html.Label(children=["Team"], className="hide", id="id-label-team"),

                        dcc.Dropdown(
                            id="id-team-sprint", className="dcc-dropdown hide",
                            options=options_team,
                            value='PLAT_REWARDS'
                        ),

                        html.Label(children=["Type"], className="hide", id="id-label-view"),
                        dcc.Dropdown(
                            id="id-status-sprint",
                            options=[
                                dict(label='started', value='started'),
                                dict(label='created', value='created'),
                                dict(label='updated', value='updated'),
                            ],
                            searchable=False, value='started', className="dcc-dropdown hide"),
                        html.I(className="line-divider"),
                        html.Label("Time Taken"),
                        dcc.Loading(id="id-fetch-time-sprints"),
                    ],
                    # className="side-menu hidden",
                    className="side-menu",
                    # id="id-side-menu-panel"
                    id=dict(type='side-panel', index='sprint')
                ),
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.Button(
                                    # html.Img(src=dash_app.get_asset_url("img/menu_open_black_24dp.svg"), ),
                                    className="format-button",
                                    id={'type': 'toggle-panel', 'index': "sprint"}
                                    # id="id-toggle-button"

                                ),
                            ],
                        ),
                        html.Div("...", className="header-menu", id="id-overview-main-header"),
                        html.Div("", className="header-right-side"),
                        html.Div("", className="middle-vacant"),
                        html.Article(
                            children=[
                                html.Div(
                                    children=[""],
                                    className="layer one show",
                                    id="id-overview-layer-1"
                                ),
                                html.Div(
                                    children=[""],
                                    className="layer two",
                                    id="id-overview-layer-2"
                                ),
                                html.Div(
                                    children=[""],
                                    className="layer three",
                                    id="id-overview-layer-3"
                                ),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer four days-grid",
                                    id="id-overview-layer-4"
                                )),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer five",
                                    id="id-overview-layer-5"
                                ))

                            ],
                            # id="overview-layer",
                            id="id-sprint-details",
                            className="main-area",
                        ),
                        html.Div("--", className="side-vacant"),
                        html.Div("", className="bottom-left"),
                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(className="active", id="id-overview-bullet-1"),
                                    html.Li(id="id-overview-bullet-2"),
                                    html.Li(id="id-overview-bullet-3"),
                                    html.Li(id="id-overview-bullet-4"),
                                    html.Li(id="id-overview-bullet-5"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-middle"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                    # className="display-area expand-grid",
                    className="display-area",
                    # id="overview-display-area"
                    # id="id-main-display-panel"
                    id=dict(type='main-panel', index='sprint')
                ),
            ],
            className="page-grid"
        )