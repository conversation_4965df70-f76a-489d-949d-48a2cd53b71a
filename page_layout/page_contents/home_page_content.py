import time
from collections import Counter

import psutil
from dash import html
import dash_bootstrap_components as dbc

from data import get_from_db as db, is_server_accessible
from page_layout.page_contents.page_content import PageContent
from app import session, current_user, superuser_permission, superuser_role


def get_memory_usage():
    """Get the current memory usage in MB"""
    process = psutil.Process()
    mem_info = process.memory_info()
    return mem_info.rss / (1024 ** 2)


def get_cpu_usage():
    """Get the current CPU usage as a percentage"""
    return psutil.cpu_percent(interval=1)


class HomePageContent(PageContent):
    def render(self):
        start_time = time.time()
        
        with self.pg_session_ro as pg_session:
            res = db.get_user_counts(pg_session)
            
            # Create welcome card
            print("Calling card creation")
            welcome_card = self._create_welcome_card()
            
            # Create infrastructure card
            infra_card = self._create_infra_card()
            
            # Create account summary card
            account_card = self._create_account_summary_card(res)
            
            # Assemble the layout
            div_tag = html.Div(
                children=[
                    html.Div(
                        children=[welcome_card],
                        className="row justify-content-center mt-3"
                    ),
                    html.Div(
                        children=[infra_card, account_card],
                        className="row"
                    ),
                ],
            )

        print(f"{__name__} Time taken: {time.time() - start_time} secs")
        return div_tag
    
    def _create_welcome_card(self):
        """Create the welcome card component"""
        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.P(
                            f"{session.get('displayName')}", 
                            id="id-welcome-user",
                            className="card-text"
                        ),
                    ],
                    className="card text-center"
                ),
            ],
            className="mb-4 col-md-6 col-lg-4"
        )
    
    def _create_infra_card(self):
        """Create the infrastructure parameters card"""
        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.H3("Infra Parameters", className="card-title"),
                        html.P(
                            f"Memory: {get_memory_usage():.2f} MB", 
                            className="card-text"
                        ),
                        html.P(
                            f"CPU:\n{get_cpu_usage():.2f}%", 
                            className="card-text"
                        ),
                    ],
                    className="card"
                ),
            ],
            className="mb-4 col-md-6 col-lg-4"
        )
    
    def _create_account_summary_card(self, res):
        """Create the account summary card"""
        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.H3("Account Summary", className="card-title"),
                        html.I(f"User Active = {res.ActiveUser}",
                               className="card-text"),
                        html.I(f"User Inactive = {res.InactiveUser}",
                               className="card-text"),
                        html.P(f"Functional Active = {res.ActiveApp}",
                               className="card-text"),
                    ],
                    className="card"
                ),
            ],
            className="mb-4 col-md-6 col-lg-4"
        )
