from dash import html, dcc

from data.helper import make_progress_graph
from page_layout.page_contents import PageContent


class WeeklyReport(PageContent):
    def render(self):
        return html.Div(
            children=[
                dcc.Tabs(
                    id='id-weekly-rpt-tabs',
                    parent_className="custom-tabs",
                    className="custom-tabs-container",
                    value="display-cookie",
                    children=[
                        dcc.Tab(
                            label="Cookie",
                            value="display-cookie",
                            children=[
                                html.Div(
                                    children=[
                                        html.Label("Add & Remove Versions", className="col"),
                                        html.Div(
                                            children=[
                                                html.Button("+", id="add-filter", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                        html.Div(
                                            children=[
                                                html.Button("-", id="remove-filter", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                    ],
                                    className="row ms-1 mt-2"
                                ),
                                html.Hr(),

                                html.Div(
                                    children=[
                                        html.Label("Release Pattern", className="col"),
                                        html.Label("Release Dropdown", className="col"),
                                        html.Label("Branch Dropdown", className="col"),
                                        html.Label("Report Header", className="col")
                                    ],
                                    className="row ms-1"
                                ),
                                html.Div(children=[], id="id-status-report-div"),
                            ],
                            className="custom-tab",
                            selected_className="custom-tab--selected",
                        ),
                        dcc.Tab(
                            label="Jazz",
                            children=[
                                html.Div(
                                    children=[
                                        html.Label("Add & Remove Versions", className="col"),
                                        html.Div(
                                            children=[
                                                html.Button("+", id="jazz-add-filter", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                        html.Div(
                                            children=[
                                                html.Button("-", id="jazz-remove-filter", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                    ],
                                    className="row ms-1 mt-2"
                                ),
                                html.Hr(),

                                html.Div(
                                    children=[
                                        html.Label("Release Pattern", className="col"),
                                        html.Label("Release Dropdown", className="col"),
                                        html.Label("Branch Dropdown", className="col"),
                                        html.Label("Report Header", className="col")
                                    ],
                                    className="row ms-1"
                                ),
                                html.Div(children=[], id="id-jazz-status-report-div"),

                            ],
                            className="custom-tab",
                            selected_className="custom-tab--selected",
                        ),
                        dcc.Tab(
                            label="Risk Register",
                            className="custom-tab",
                            selected_className="custom-tab--selected",
                            children=[
                                html.Div(
                                    children=[
                                        html.Label("Add & Remove Rows", className="col"),
                                        html.Div(
                                            children=[
                                                html.Button("+", id="id-add-filter-risk", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                        html.Div(
                                            children=[
                                                html.Button("-", id="id-remove-filter-risk", n_clicks=0,
                                                            className="release-format-button"),
                                            ], className="col"
                                        ),
                                    ],
                                    className="row ms-1 mt-2"
                                ),
                                html.Hr(),
                                html.Div(
                                    children=[
                                        html.Label(
                                            "Date Raised",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Risk Description",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Probability",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Impact",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Mitigation Plan",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Contingency Plan",
                                            className="col d-flex flex-wrap bg-light me-1",
                                        ),
                                        html.Label(
                                            "Select",
                                            className="col bg-light me-4",
                                        ),
                                    ],
                                    className="row ms-1"
                                ),
                                html.Div(children=[], id="id-plat-risk-register"),
                            ]
                        )
                    ],
                    persistence=True,
                    persistence_type='local',
                         ),

                html.Div(
                    children=[
                        html.P(id="paragraph_id", children=["Button not clicked"]),
                        dcc.Graph(id="progress_bar_graph", figure=make_progress_graph(0, 10)),
                    ]
                ),
                html.Div(
                    children=[
                        html.Button(
                            id="button_id", children="Run Job!", n_clicks=0, className="format_button me-1"
                        ),
                        html.Button(
                            id="cancel_button_id", children="Cancel Running Job!", className="format_button me-1"
                        ),
                        html.Button(
                            id="id-download-report", children="Download Report", className="format_button",
                            n_clicks=0
                        ),
                    ],
                    className="hstack mb-2 mt-2"
                ),
                dcc.Download(id='id-status-rpt-download'),
                dcc.Store(id="id-status-rpt-filename", storage_type='memory'),

                html.Div(children=[html.H2("Test Value")], id='log', className="status-report"),
                dcc.Interval(
                    id='log-update',
                    interval=1 * 1000  # in milliseconds
                ),
            ],
            className="container"
        )
