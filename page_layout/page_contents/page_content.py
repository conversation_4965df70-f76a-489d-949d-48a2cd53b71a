import time
from inspect import isclass, isabstract, getmembers
from dash import html
from dependency_injector import containers

from page_layout import page_contents


class PageContent:
    pages = {}

    def __init__(self, project: str, session_factory: containers.DynamicContainer, url: str = None):

        self.session_factory = session_factory

        self.project = project
        project_default_to_public = ['', 'socket.io', 'r&d', 'admin', 'test_page']

        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override(
            {'schema_name': project if project not in project_default_to_public else 'public'}
        )

        try:
            self.pg_session_ro = session_factory.db_conn_provider().session()

        except Exception as e:
            print(e)

        self.ms_db_engine = session_factory.db_ms_engine()

        # self.load_pages(url)

    # TODO: This can dynamically load all class members. See how to leverage it later.
    def load_pages(self, url):
        classes = getmembers(page_contents, lambda x: isclass(x) and not isabstract(x))
        for name, _type in classes:
            if isclass(_type) and issubclass(_type, page_contents.PageContent):
                self.pages.update({url: [name, _type]})

    def render(self):
        raise NotImplementedError


class NotFoundPageContent(PageContent):
    def render(self):
        return html.Div(
            children=[
                html.H1("404"),
                html.P("Page Under Construction")
            ], className="error"
        )


class CoreTrackUpdates(PageContent):
    def render(self):
        pass


class GetSVNDetails(PageContent):
    def render(self):
        pass
