import asyncio
import os
from textwrap import dedent

from dash_iconify import DashIconify
from sqlalchemy.exc import InterfaceError
from sqlalchemy import text
from datetime import date, timedelta

from dash import html, dcc
import dash_bootstrap_components as dbc
import dash_ag_grid as dag
import dash_mantine_components as dmc
from data import MyLogger, is_server_accessible
from data.helper import canonical_form
from page_layout.page_contents import Page<PERSON>ontent


def show_callbacks(app):
    def wrap_list(items, padding=24):
        return ("\n" + " " * padding).join(items)

    def format_regs(registrations):
        vals = sorted("{}.{}".format(i['id'], i['property'])
                      for i in registrations)
        return wrap_list(vals)

    output_list = []
    n = 0

    for callback_id, callback in app.callback_map.items():
        if callback_id.startswith(".."):
            outputs = callback_id.strip(".").split("...")
        else:
            outputs = [callback_id]
        if n == 0:
            print(callback_id)
            print("-" * 20)
            for k, v in callback.items():
                print(k)
            n += 1
        if "callback" in callback:
            wrapped_func = callback["callback"].__wrapped__
        if "inputs" in callback:
            inputs = callback["inputs"]
        if "states" in callback:
            states = callback["state"]

        if callback_id.startswith(".."):
            outputs = callback_id.strip(".").split("...")
        else:
            outputs = [callback_id]

        str_values = {
            "callback": wrapped_func.__name__,
            "outputs": wrap_list(outputs),
            "filename": os.path.split(wrapped_func.__code__.co_filename)[-1],
            "lineno": wrapped_func.__code__.co_firstlineno,
            "num_inputs": len(inputs),
            "num_states": len(states),
            "inputs": format_regs(inputs),
            "states": format_regs(states),
            "num_outputs": len(outputs),
        }

        output = dedent(
            """
            callback    {callback} @ {filename}:{lineno}
            Outputs{num_outputs:>3}  {outputs}
            Inputs{num_inputs:>4}  {inputs}
            States{num_states:>4}  {states}
            """.format(**str_values)
        )

        output_list.append(output)
    return "\n".join(output_list)


async def test_code(session_factory):
    my_logger = MyLogger().get_logger()
    project_list = [
        'Plat', 'Plat-jazz', 'CP_PLAT_CI', 'Plat_NEST', 'Plat Stress Test',
        'Plat-CODA', 'Plat-Lotus',
        'Plat Production Support', 'Plat Managed Services', 'Plat-NBL', 'CoreCardIndia',
        'CoreProcessing', 'Credit Processing', 'CP_CBC_Corecredit', 'Prepaid Global',
        'PrePaidProcessing', 'Customer Kabbage', 'PEX', 'Posting 2.0',
    ]

    async with session_factory.db_conn_provider().create_async_engine() as async_ms_engine:
        async with async_ms_engine.connect() as async_conn:
            # Create a list of bind parameters
            bind_params = ', '.join([f":project{i}" for i in range(len(project_list))])
            # Create a dictionary of parameters to bind to the query
            params = {f"project{i}": project for i, project in enumerate(project_list)}
            query = text(
                f"SELECT ProjectCode, projectname, ClientId FROM PROJECT with (nolock) WHERE projectname in ({bind_params})"
            )
            result = await async_conn.execute(query, params)
            rows = result.all()

            week_id = await async_conn.execute(
                text(
                    f"SELECT WeekId from week with (nolock) where WeekBegDate = '{date.fromisoformat('2023-04-01').strftime('%m/%d/%Y')}'")
            )
            begin_date = week_id.all()
            my_logger.info(f'date_id = {begin_date}')
            my_logger.info(rows)

            sorted_result_set = sorted(
                rows, key=lambda x: canonical_form(x[1]),
                reverse=False
            )
            return sorted_result_set


class WDTimeSheet(PageContent):
    def render(self):
        my_logger = MyLogger().get_logger()
        # project = 'plat'
        # connection_established = True
        # connection_ms = None
        print(f"callback map")
        # print(show_callbacks(dash_app))
        project_option = [
            {'label': 'Exception Occured', 'value': 'SQLException'}
        ]
        project_value = ['SQLException']

        error_code = 0
        project_client_map = {}
        # Use this logic to disable specific days in calendar
        start_date = date(2023, 1, 1)
        date_array = [start_date + timedelta(days=x) for x in range((date.today() - start_date).days + 90)]
        # disabled_days = [x for x in date_array if x.weekday() < 5] + [x for x in date_array if x.weekday() == 6]
        disabled_days = [x for x in date_array if x.weekday() != 4]

        session_factory = self.session_factory
        session_factory.config.override({'ro_or_rw': 'ro'})

        session_factory.config_schema.override({'schema_name': 'mssql'})
        check_server_accessible = is_server_accessible(
            session_factory.db_conn_provider().get_server_name(),
            session_factory.db_conn_provider().get_server_port()
        )

        if check_server_accessible:
            session_factory.config_schema.override({'schema_name': 'mssql_async'})
            my_logger.debug(f"type of async engine: {type(session_factory.db_conn_provider().create_async_engine())}")
            results = asyncio.run(test_code(session_factory))
            my_logger.debug(results)

        session_factory.config_schema.override({'schema_name': 'mssql'})
        if check_server_accessible:
            project_list = [
                'Plat', 'Plat-jazz', 'CP_PLAT_CI', 'Plat_NEST', 'Plat Stress Test',
                'Plat-CODA', 'Plat-Lotus',
                'Plat Production Support', 'Plat Managed Services', 'Plat-NBL', 'CoreCardIndia',
                'CoreProcessing', 'Credit Processing', 'CP_CBC_Corecredit', 'Prepaid Global',
                'PrePaidProcessing', 'Customer Kabbage', 'PEX', 'Posting 2.0',
            ]
            with session_factory.db_conn_provider().create_engine() as engine:
                try:

                    # Create a list of bind parameters
                    bind_params = ', '.join([f":project{i}" for i in range(len(project_list))])
                    # Create a dictionary of parameters to bind to the query
                    params = {f"project{i}": project for i, project in enumerate(project_list)}

                    # with self.ms_db_engine.create_engine() as connection:
                    # with session_factory.db_conn_provider().create_engine() as connection:
                    with engine.connect() as conn:
                        try:
                            query = text(
                                f"SELECT ProjectCode, projectname, ClientId FROM PROJECT with (nolock) WHERE projectname in ({bind_params})"
                            )
                            result = conn.execute(query, params)
                            rows = result.all()

                            query = conn.execute(
                                text(
                                    f"SELECT WeekId from week with (nolock) where WeekBegDate = '{date.fromisoformat('2023-04-01').strftime('%m/%d/%Y')}'")
                            )
                            begin_date = query.all()
                            my_logger.info(f'date_id = {begin_date}')
                            print(rows)

                            sorted_result_set = sorted(
                                rows, key=lambda x: canonical_form(x[1]),
                                reverse=False
                            )

                            project_option = [
                                {'label': project_name, 'value': int(project_code)}
                                for project_code, project_name, _ in sorted_result_set
                            ]
                            project_client_map = {int(project_code): client for project_code, _, client in
                                                  sorted_result_set}
                            my_logger.info(f'project_client_map = {project_client_map}')
                            project_value = [1106044, 1106082, 1106163, 1106223, 1106271, 1106272, 1106089]

                        except Exception as e:
                            my_logger.info(f'Connection failed. Error - {e}')
                except InterfaceError as e:
                    print(f"type = {type(e.orig)}")
                    error_code = e.orig.args[0]
                    error_message = e.orig.args[1]
                    if error_code == '28000':  # ODBC error code for "Login failed"
                        my_logger.debug("The login password has expired.")
                    else:
                        my_logger.debug(f"Database error: {error_message}")

        else:
            project_option = [
                {'label': 'Plat', 'value': 1106044},
                {'label': 'CP_PLAT_CI', 'value': 1106082},
                {'label': 'Plat-Jazz', 'value': 1106163},
                {'label': 'Plat_NEST', 'value': 1106223},
                {'label': 'Plat-Lotus', 'value': 1106271},
                {'label': 'Plat-CODA', 'value': 1106272},
                {'label': 'Plat Stress Test', 'value': 1106089},
            ]
            project_value = [1106044, 1106082, 1106163, 1106223, 1106271, 1106272, 1106089]
            project_client_map = {project: 8033 for project in project_value}

        defaultColDef = {
            'flex': 1,
            "filter": "agNumberColumnFilter",
            "editable": False,
            "floatingFilter": True,
            # 'wrapText': True,
            # 'autoHeight': True,
            # 'dangerously_allow_html': True,
            # 'pagination': True,
            # 'paginationPageSize': 25,
            # 'enableSorting': True,
            "minWidth": 100
        }

        columnDefs = [
            {'headerName': 'Project Name', 'field': 'ProjectName', 'filter': True, },
            {'headerName': 'Feature Name', 'field': 'feature', 'filter': True, },
            {'headerName': 'Task Name', 'field': 'TaskName', 'filter': True, },
            {'headerName': 'Employee Name', 'field': 'EeLogin', 'filter': True, },
            {'headerName': 'Employee Email', 'field': 'email_id', 'filter': True, },
            {'headerName': 'Team', 'field': 'team_name', 'filter': True, },
            {'headerName': 'Week Ending', 'field': 'WeekEndDate', 'filter': True},
            {'headerName': 'Billing Period', 'field': 'BillingPeriod', 'filter': False},
            {'headerName': 'Total Hours', 'field': 'Totalhrs', 'filter': True, },
            {'headerName': 'TS Status', 'field': 'Status', 'filter': False, },
            {'headerName': 'CC Issue Key', 'field': 'jira_issue_ids', 'filter': True, },
            {'headerName': 'CC Epic#', 'field': 'JiraFeaturekey', 'filter': True},
            {'headerName': 'Client JIRA#', 'field': 'ClientJiraNo', 'filter': True},
            {'headerName': 'Client Summary', 'field': 'gs_summary', 'filter': True},
            {'headerName': 'Client FixVersion', 'field': 'gs_fixversion', 'filter': True},
            {
                'headerName': 'Effort Estimate', 'field': 'effort_estimates', 'filter': True,
                # "valueFormatter": {"function": "params.value = isNaN(params.value) ? '' : params.value"}
            },
            {'headerName': 'GS JIRA Status', 'field': 'gs_status', 'filter': True},
            {'headerName': 'Standard Key', 'field': 'standard_key', 'filter': True},
            {'headerName': 'Standard Issue Type', 'field': 'standard_issuetype', 'filter': True},
            # {'headerName': 'Time Spent', 'field': 'aggregatetimespent', 'filter': True},
            # {'headerName': 'CC Issue Key', 'field': 'cc_issue_key', 'filter': True},
            # {'headerName': 'CC Issue Summary', 'field': 'cc_issue_summary', 'filter': True},
            # {'headerName': 'CC Issue Type', 'field': 'cc_issuetype', 'filter': True},
            # {'headerName': 'CC Issue Status', 'field': 'cc_issue_status', 'filter': True},
            # {'headerName': 'CC Epic Key', 'field': 'cc_epic_key', 'filter': True},
            # {'headerName': 'CC Epic Summary', 'field': 'cc_epic_summary', 'filter': True},
            {'headerName': 'Custom Project', 'field': 'custom_project', 'filter': True},
            # {'headerName': 'Fix Version', 'field': 'fixVersions', 'filter': True},
            # {'headerName': 'Affected Versions', 'field': 'versions', 'filter': True},
            # {'headerName': 'Priority', 'field': 'priority', 'filter': True},
            # {'headerName': 'Severity', 'field': 'severity', 'filter': True},
            {'headerName': 'Source', 'field': 'wd_time_source', 'filter': True},
            {'headerName': 'Classification', 'field': 'classification', 'filter': True},
            {'headerName': 'Key', 'field': 'ClientKey', 'filter': True},
            # {'headerName': 'Comments', 'field': 'comments', 'filter': False},

        ]
        child_element = [
            html.Div(
                children=[
                    html.Label("Selection Panel"),
                    html.I(className="line-divider"),
                    dcc.Checklist(
                        options=[
                            {
                                'label': [html.Span('Set End Date = Start Date', style={"padding-left": 10})],
                                'value': 1
                            },
                        ], inline=True,
                        id="id-check-same-day",
                        labelStyle={"display": "flex", "align-items": "center", 'width': '200px'},
                        # style={'color': 'white', 'font-family': 'Rajadhani', 'font-size': 'x-small'}
                        className="container-check-box",
                    ),
                    dcc.DatePickerRange(
                        start_date_placeholder_text='From Week Ending',
                        end_date_placeholder_text='To Week Ending',
                        # end_date=date.today(),
                        display_format='Do MMMM YY',
                        id='id-isc-date-picker-range',
                        # max_date_allowed=date.today(),
                        initial_visible_month=date.today(),
                        show_outside_days=True,
                        disabled_days=disabled_days
                    ),

                    html.Label("Projects"),
                    dcc.Dropdown(
                        id="id-ts-project-dropdown",
                        className="dcc-dropdown ps-4",
                        disabled=False,
                        multi=True, clearable=False,
                        options=project_option,
                        value=project_value
                    ),
                    dcc.Dropdown(
                        id="id-ts-issuetype",
                        className="dcc-dropdown ps-4",
                        multi=False, clearable=False,
                        options=[
                            {'label': 'Development Defect Resolution', 'value': 'Development Defect Resolution'},
                        ],
                        value='Development Defect Resolution'
                    ),
                    html.Button(
                        id="id-get-ts-details", children="Get Details",
                        className="format_button ms-4",
                        disabled=True, n_clicks=0,
                        title="Get Details",
                        **{"aria-label": "Get Details"}
                    ),
                    html.I(className="line-divider"),
                    html.Label("File Upload"),
                    dcc.Upload(
                        [
                            'Drag and Drop or ',
                            html.A('Select a File'),
                            html.P("Only csv, xls, xlsx file types allowed")
                        ],
                        multiple=False,
                        disabled=False,
                        style={
                            'width': '250px',
                            'lineHeight': '30px',
                            'borderWidth': '1px',
                            'borderStyle': 'dashed',
                            'borderRadius': '5px',
                            'textAlign': 'center',
                            'border-style': 'dashed',
                            'background-color': 'gray',
                            'margin-bottom': '5px',
                            'margin-left': '25px',
                        },
                        id="id-gs-jira-upload",
                    ),
                    html.Div(
                        children=[
                            html.Button(
                                id="id-run-gs-jira", children="Process",
                                className="format_button ms-4 hidden",
                                disabled=True, n_clicks=0,
                                title="Process the file",
                                **{"aria-label": "Process the file"}
                            ),
                            html.Button(
                                id="id-cancel-gs-jira", children="Cancel",
                                className="format_button ms-4", disabled=True,
                                title="Cancel file processing",
                                **{"aria-label": "Cancel file processing"}
                            ),
                        ],
                        className="vstack gap-3 container-login-button"
                    ),
                    dcc.Store(id="id-gs-jira-details", storage_type='session'),
                    dcc.Store(id="id-project-client", data=project_client_map, storage_type='session'),
                    dbc.Alert(id="id-gs-jira-msg", style={'width': '250px'})
                ],
                className="side-menu",
                id=dict(type='side-panel', index='isctimesheet')
            ),
            html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Button(
                                className="format-button",
                                id=dict(type='toggle-panel', index="isctimesheet"),
                                n_clicks=0
                            ),
                        ],
                    ),
                    html.Div(
                        children=[html.Label("WD Timesheet"), ], className="header-menu",
                        id="id-wd-ts-main-header"
                    ),
                    dcc.Store(id="id-wd-df-list-store"),
                    html.Div(
                        children=[
                            html.Button(
                                id=dict(type='file-download-btn', index="wd-ts"),
                                className="format-export-button"
                            ),
                            dcc.Download(id=dict(type="file-download-prop", index="wd-ts"))
                        ], className="header-right-side"),
                    html.Div("", className="middle-vacant"),
                    html.Article(
                        children=[
                            html.Div(
                                children=[
                                    # dcc.Markdown("Click on the download button below to export data to CSV"),
                                    html.Button(
                                        "Download CSV", id="csv-button", n_clicks=0,
                                        className='format_button'
                                    ),
                                    dcc.Loading(
                                        dag.AgGrid(
                                            id="my-isc-timesheet-grid",
                                            className="ag-theme-quartz-dark",
                                            rowData=None,
                                            columnDefs=columnDefs,
                                            columnSize="sizeToFit",
                                            defaultColDef=defaultColDef,
                                            dashGridOptions={
                                                "undoRedoCellEditing": True, "rowSelection": "single",
                                                "loadingOverlayComponent": "CustomLoadingOverlay",
                                                "loadingOverlayComponentParams": {
                                                    "loadingMessage": "One moment please...",
                                                    "color": "white",
                                                },
                                                "pagination": True,
                                                "paginationAutoPageSize": True,
                                                "animateRows": False,
                                                "paginationPageSizeSelector": True,
                                                # `reactiveCustomComponents = true` is deprecated from Version 32
                                                "reactiveCustomComponents": True

                                            },
                                            csvExportParams={
                                                "fileName": "wd_timesheet.csv",
                                            },
                                        ),
                                    ),
                                ],
                                className="layer two show",
                                id="id-isc-ts-layer-2"
                            ),
                            html.Div(
                                children=[
                                    dcc.Loading(dcc.Graph(id="id-cc-timesheet-graph")),
                                ],
                                className="layer three",
                                id="id-isc-ts-layer-3"
                            ),
                            html.Div(
                                children=[
                                    dcc.Loading(dcc.Graph(id="id-cc-issuetype-graph")),
                                ],
                                className="layer five",
                                id="id-isc-ts-layer-5"
                            ),
                        ],
                        id="id-isc-ts-details",
                        className="main-area",
                    ),
                    html.Div("", className="side-vacant", id=""),
                    html.Div("", className="bottom-left"),
                    html.Div(children=[
                        html.Ul(
                            children=[
                                html.Li(className="active", id="id-isc-ts-bullet-2"),
                                html.Li(className="", id="id-isc-ts-bullet-3"),
                                html.Li(className="", id="id-isc-ts-bullet-5"),
                            ],
                            className="bullets"
                        )],
                        className="bottom-middle"
                    ),
                    html.Div("", className="bottom-right"),
                ],
                className="display-area",
                id=dict(type='main-panel', index='isctimesheet')
            ),
        ]

        if error_code == "28000":
            child_element.append(
                dmc.NotificationProvider(
                    position="top-center",
                    containerWidth="50%",
                    autoClose=False
                )
            )
            child_element.append(
                dmc.Notification(
                    title="DB Error",
                    id="simple-notify",
                    action="show",
                    message="Please contact System Admin!!!!",
                    icon=DashIconify(icon="akar-icons:circle-check"),
                    autoClose=False,
                    color="orange",
                )

            )

        return dmc.MantineProvider(html.Div(
            children=child_element,
            className="page-grid"
        ))
