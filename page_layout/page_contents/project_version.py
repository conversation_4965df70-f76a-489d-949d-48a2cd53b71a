import time

from dash import html, dcc
import dash_bootstrap_components as dbc
from page_layout.page_contents import PageContent
from data import get_from_db as db



class VersionPageContent(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            versions: list = db.get_all_active_versions(pg_session)
            options = [
                dict(label=name, value=name) for name in versions
            ]

        return html.Div(
            children=[
                html.Div(
                    children=[
                        dcc.Clipboard(
                            id="id-copy-epic", title="Copy",
                            style={
                                "position": "absolute",
                                "top": 0,
                                "right": 20,
                                "fontSize": 20,
                                "color": "white"
                            },
                        ),
                        html.Label("Selection Panel"),
                        html.I(className="line-divider"),
                        html.Details(
                            children=[
                                html.Summary("Version State", className="format_summary"),
                                html.Label("Released", className="format_summary"),
                                dcc.Dropdown(
                                    options=[
                                        dict(label='Yes', value=1),
                                        dict(label='No', value=0),
                                    ],
                                    className="dcc-dropdown",
                                    clearable=False,
                                    searchable=False,
                                    id="id-version-released-value",
                                    value=0
                                ),
                                html.Label("Archived", className="format_summary"),
                                dcc.Dropdown(
                                    options=[
                                        dict(label='Yes', value=1),
                                        dict(label='No', value=0),
                                    ],
                                    className="dcc-dropdown",
                                    clearable=False,
                                    searchable=False,
                                    id="id-version-archive-value",
                                    value=0
                                ),
                            ],
                            id='details',
                            open=False,
                            className="hide"
                        ),
                        html.I(className="line-divider hide"),
                        html.Label("Releases"),
                        dcc.Loading(
                            dcc.Dropdown(
                                id="id-versions", className="dcc-dropdown", multi=True, searchable=True,
                                options=options
                            )
                        ),
                        html.Label("Releases: Pattern Search"),
                        html.Div(children=[
                            dcc.Input(id="id-version-search", type="text", debounce=True, value=''),
                            html.Button(id="id-version-serach-button", className="format-serach-button")
                        ]),
                        html.I(className="line-divider"),

                        # html.Label("Projects", className="hide", id="id-release-project"),
                        # dcc.Dropdown(
                        #     id="id-release-project-dropdown",
                        #     className="dcc-dropdown hide",
                        #     disabled=True,
                        #     multi=True
                        # ),
                        html.Label("Filter by Status", className="hide", id="id-release-status-label"),
                        dcc.Dropdown(
                            id="id-release-status-value",
                            className="dcc-dropdown hide",
                            disabled=True,
                            clearable=False
                        ),
                        html.Label("Filter by Priority Group", className="hide", id="id-release-priority-label"),
                        dcc.Dropdown(
                            id="id-release-priority-value",
                            className="dcc-dropdown hide",
                            disabled=True,
                            clearable=False,
                            options=[
                                {'label': 'ALL', "value": 'ALL'},
                                {"label": '>=HIGH', "value": 'FILTER_HIGH'},
                                {"label": '<HIGH', "value": 'FILTER_OUT_HIGH'}
                            ]
                        ),
                        html.Label("Type", className="hide", id="id-release-type"),
                        dcc.Dropdown(
                            id="id-release-selector",
                            className="dcc-dropdown hide",
                            options=[
                                {'label': 'AffectsVersion', 'value': 0},
                                {'label': 'FixVersion', 'value': 1},

                            ],
                            value=0
                        ),

                        # html.Label("Charts", className="hide", id="id-release-chart-label"),
                        # dcc.Dropdown(
                        #     id="id-release-project-charts",
                        #     className="dcc-dropdown hide",
                        #     options=dict(
                        #         opendefects='Open Defects',
                        #         bypriority='By Priority',
                        #         details='Details'
                        #     ),
                        #     disabled=True,
                        #     value='details',
                        #     clearable=False
                        # ),

                        # html.Label("Hide Done Issues", className="hide"),
                        # dcc.Dropdown(
                        #     id="id-filter-done", className="dcc-dropdown hide", searchable=False, clearable=False,
                        #     options=[dict(label='No', value='No'), dict(label='Yes', value='Yes')],
                        #     value='No'
                        # ),
                        html.I(className="line-divider"),
                        html.Label("Counts: Priority & Severity", className="hide", id="id-release-counts"),
                        dbc.Alert(id="id-release-alert", className="hide", color='info'),
                        dbc.Alert(id="id-release-urgency", className="hide", color='info')
                        # html.Label("Time Taken"),
                        # dcc.Loading(id="id-fetch-time-versions"),
                    ],
                    className="side-menu",
                    id=dict(type='side-panel', index='version')
                ),
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.Button(
                                    className="format-button",
                                    # id="id-toggle-button-version"
                                    # id={'type': 'toggle-panel', 'index': "version"}
                                    id=dict(type='toggle-panel', index="version")
                                ),
                            ],
                        ),
                        html.Div(children=[
                            html.Label("Release Roadmap"),
                        ], className="header-menu",
                            id="id-version-main-header"),
                        # html.Div(children=[html.A(id="id-excel-download")], className="header-right-side"),
                        html.Div(children=[
                            html.Button(
                                id="id-excel-download",
                                className="format-export-button"
                                # children=[html.I(className="fas fa-file-export")],
                            ),
                            dcc.Download(id="id-download")
                        ], className="header-right-side"),
                        html.Div("", className="middle-vacant"),
                        html.Article(
                            children=[
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer one",
                                    id="id-version-layer-1"
                                ), type='graph'),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer two",
                                    id="id-version-layer-2"
                                ), type='circle'),
                                html.Div(
                                    children=[""],
                                    className="layer three show",
                                    id="id-version-layer-3"
                                ),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer five",
                                    id="id-version-layer-5"
                                )),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer six",
                                    id="id-version-layer-6"
                                )),
                                dcc.Loading(html.Div(
                                    children=[""],
                                    className="layer seven",
                                    id="id-version-layer-7"
                                )),
                                html.Div(
                                    children=[
                                        html.Table(
                                            children=[
                                                html.Thead(
                                                    html.Tr(children=[
                                                        html.Th("Key"),
                                                        html.Th("summary"),
                                                        html.Th("status"),
                                                        html.Th("priority"),
                                                        html.Th("severity"),
                                                        html.Th("CVSS Score"),
                                                        html.Th("issuetype"),
                                                        html.Th("project"),
                                                        html.Th("AffectsVersion"),
                                                        html.Th("FixVersion"),
                                                        html.Th("Assigned To"),
                                                        html.Th("Reporter"),
                                                        html.Th("aging"),
                                                        html.Th("Team"),
                                                        html.Th("components"),
                                                        html.Th("Test Case#"),
                                                        html.Th("Test_name"),
                                                        html.Th("Test Automation status"),
                                                    ]),
                                                ),
                                                html.Tbody(
                                                    children=[
                                                        html.Tr(
                                                            children=[
                                                                         html.Td("")
                                                                     ] * 18,
                                                        )
                                                    ],
                                                    id="id-version-bugs-table-body",
                                                )
                                            ],
                                            className="format-table",
                                            id="id-version-bugs-table"
                                        )
                                    ],
                                    className="layer eight",
                                    id="id-version-layer-8"
                                ),
                            ],
                            id="id-version-details",
                            className="main-area",
                        ),
                        html.Div("", className="side-vacant", id=""),
                        html.Div("", className="bottom-left"),
                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(className="active", id="id-version-bullet-3"),
                                    html.Li(id="id-version-bullet-8"),
                                    html.Li(id="id-version-bullet-7"),
                                    html.Li(id="id-version-bullet-5"),
                                    html.Li(id="id-version-bullet-6"),
                                    html.Li(id="id-version-bullet-2"),
                                    html.Li(id="id-version-bullet-1"),

                                ],
                                className="bullets"
                            )],
                            className="bottom-middle"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                    className="display-area",
                    # className="display-area-3level",
                    # id="id-main-display-panel-version"
                    id=dict(type='main-panel', index='version')
                ),
            ],
            className="page-grid"
        )
