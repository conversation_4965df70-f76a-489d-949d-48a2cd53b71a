import asyncio
import os
import ssl
# from textwrap import dedent

from dependency_injector.wiring import inject, Provide
from dash import html, dcc, callback, clientside_callback, _dash_renderer
from dash.dependencies import Input, Output
# These imports are required to make uwsgi work

from flask import render_template, redirect, url_for, current_app
from flask_login import current_user, logout_user
from flask_principal import identity_changed, Identity, AnonymousIdentity

import custom_container
from app import dash_app, session, server

# TODO incorporate Flask-SeaSurf & flask-talisman
# TODO Read https://community.plotly.com/t/sidebar-with-icons-expands-on-hover-and-other-cool-sidebars/67318
# TODO Read below link for database partitioning
# Source: https://stackoverflow.com/questions/61545680/postgresql-partition-and-sqlalchemy
# from dash_app import socketio

# import layouts
# import callbacks

# from data import helper, get_from_db as db

# import flask_monitoringdashboard as monitor

# monitor.config.init_from(envvar='FLASK_MONITORING_DASHBOARD_CONFIG')
# monitor.bind(server)


# Login screen
from page_layout.html_layouts import generate_layout

# login = html.Div(
#     children=[
#         html.Div(children=[], className="text-center"),
#         # dcc.Store(id="id-store-projects", storage_type='session'),
#         dcc.Location(id='url_login', refresh=True),
#
#         html.Ul(children=[
#             html.Li(children=[
#                 html.Div(
#                     children=[
#                         html.Div(
#                             children=[
#                                 dcc.Input(
#                                     type='email', id="id-login-main",
#                                     placeholder="O365 email id",
#                                     className="input-creds",
#                                     debounce=True,
#                                     persistence="true"
#                                 ),
#                                 html.Span(className='focus-input-creds'),
#                                 html.Span(children=[html.I(className="fa fa-envelope")],
#                                           className="symbol-input-creds"),
#                             ],
#                             className='wrap-input-creds',
#                         ),
#                     ]),
#             ]),
#         ], className="login-container-ul"),
#         html.Div(
#             html.Ul(
#                 children=[
#                     html.Li(children=[
#                         dcc.Input(type='password', id="id-login-passwd-main", placeholder="Password",
#                                   className="input-creds", debounce=True, persistence="true"),
#                         html.Span(className='focus-input-creds'),
#                         html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds")
#                     ], className='wrap-input-creds'),
#                 ], className="login-container-ul"
#             ),
#         ),
#         html.Div(
#             children=[
#                 html.Button(
#                     id="id-button-login-main", children="login",
#                     className="format_button", n_clicks=0, autoFocus='AUTOFOCUS'
#                 ),
#             ],
#             className="container-login-button"
#         ),
#     ], className="container_custom"
#     # [dcc.Location(id='url_login', refresh=True),
#     #  html.H2('''Please log in to continue:''', id='h1'),
#     #  dcc.Input(placeholder='Enter your username', type='text', id='uname-box'),
#     #  dcc.Input(placeholder='Enter your password', type='password', id='pwd-box'),
#     #  html.Button(children='Login', n_clicks=0, type='submit', id='login-button'),
#     #  html.Div(children='', id='output-state'),
#     #  html.Br(),
#     #  dcc.Link('Home', href='/')]
# )

# login_2fa_old = html.Div(
#     children=[
#         html.Div(
#             children=[
#                 html.Div(
#                     children=[
#                         html.Label("Generated Token"),
#                     ],
#                     className="row"
#                 ),
#                 html.Div(id="id-dummy-token-response"),
#                 dcc.Location(id='url_login_2fa', refresh=True),
#
#                 html.Div(className="element-spacer"),
#                 dcc.Input(id='id-input-token', autoFocus=True, debounce=True),
#                 html.Div(
#                     children=[
#                         html.Button(id="id-button-token", children="Authenticate", className="format_button",
#                                     n_clicks=0),
#                         html.Button(id="id-button-reset-pin", children="Reset PIN", className="format_button",
#                                     n_clicks=0),
#                     ],
#                     className="element-spacer container-login-button"
#                 ),
#
#             ], className="container-2fa"
#         ),
#
#         dbc.Container(
#             children=[
#                 html.H2("2FA", className="white"),
#                 html.H4("Set and Authenticate 2FA"),
#
#                 html.H5("Instructions!"),
#                 html.Ul(
#                     children=[
#                         html.Li(
#                             children=[
#                                 html.Label("Download "),
#                                 html.A(
#                                     'Google Authenticator',
#                                     href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en&gl=US",
#                                     target="_blank",
#                                 ),
#                                 html.Label("on your mobile.")
#                             ]
#                         ),
#                         html.Li(
#                             children=[
#                                 html.Label("Create a new account with"),
#                                 html.Strong("setup key"),
#                                 html.Label("method.")
#                             ]
#                         ),
#                         html.Li(
#                             children=[
#                                 html.Label("Provide the required details (name, secret key).")
#                             ]
#                         ),
#                         html.Li(
#                             children=[
#                                 html.Label("Select time-based authentication.")
#                             ]
#                         ),
#                         html.Li(
#                             children=[
#                                 html.Label("Submit the generated key in the form")
#                             ]
#                         )
#                     ]
#                 ),
#                 html.Label("Secret Token"),
#                 dcc.Input(type="text", readOnly=True, id='id-secret-token-key'),
#
#             ]
#         ),
#
#     ],
# )


_dash_renderer._set_react_version('18.2.0')

dash_app.layout = html.Div(
    children=[
        html.Header(id="header-page-content"),
        html.Main(id="main-page-content"),
        dcc.Location(id='url', refresh=False),
    ]
)


@server.route(f"{os.getenv('DASH_BASE_PATH', '/')}logout")
def logout_current_user():
    print(f"logout called from index.py")
    if current_user.is_authenticated:
        print(f"Value of logout_reason: {session.get('logout_reason')}")
        if session.get('logout_reason') != 'inactivity':
            session['logged_in'] = False
            session['logout_reason'] = 'user_initiated'
            session['projects'] = ''
        logout_user()

        for key in (
                'email_id', 'displayName', 'projects', 'avatarUrls16x16', 'api_token',
                '2fa_enabled', 'identity.name', 'identity.auth_type', 'identity.id',
                'last_activity', 'encoded_image', 'encoded_image_status', '_permanent'
        ):
            session.pop(key, None)
        # Remove identity from session explicitly

        # Check for remaining keys
        for key, value in session.items():
            print(f"Reamining key: {key} -> {value}")

        identity_changed.send(current_app._get_current_object(), identity=AnonymousIdentity())

        # flash("You have been successfully logged off", "success")
        # return f"You have been successfully logged off. To log back in click<a href={request.root_url}>here</a>"
        return render_template("logout_page.html", dash_base_path=os.getenv('DASH_BASE_PATH', '/'))
    else:
        print("user not authenticated in logout_current_user")
        session['logout_reason'] = 'not_logged'
        return render_template("logout_page.html")
        # return redirect("/")


@server.route("/test")
def render_2fa_page():
    session['new_registration'] = 'existing'
    return render_template("login_2fa.html")


# @dash_app.server.route("/getAToken")
# def get_token():
#     print("URLs")
#     print(request.root_url)
#     print(request.base_url)
#     print(request.url)
#     print("Query String")
#     print(request.query_string)
#     # request.args.get('param')
#     code = request.args.get('code')
#     client_info = request.args.get('client_info')
#     state = request.args.get('client_info')
#     session_state = request.args.get('session_state')
#     auth_response = request.args.to_dict()
#     print("Value of code from session")
#     print(code)
#     print(client_info)
#     print(state)
#     print(session_state)
#     print("Auth Response is:")
#     print(auth_response)
#     return "<h1 id='id-get-a-token'>Success<h1>"

# def clicked(ctx: callback_context):
#     if not ctx.triggered or not ctx.triggered[0]['value']:
#         return None
#     else:
#         return ctx.triggered[0]['prop_id'].split('.')[0]


# @dash_app.callback(
#     Output("id-regen-header", "children"),
#     Input("id-my-project-count", "value"),
#     State("id-session-project", "data")
# )
# def update_header(counter, data):
#     user_clicked = clicked(callback_context)
#     if user_clicked is None:
#         raise PreventUpdate
#     print(f'counter = {counter}')
#     print(f'project = {data}')
#     start = counter - 1 if counter == 1 else (counter - 1) * 5 + 1
#     end = min(counter * 5, len(data))
#     slice_list = tuple(data[start:end])
#     return generate_layout(slice_list)

# Modifying logic.
# if any issue revert to below code
# @callback(
#     Output(component_id="header-page-content", component_property="children"),
#     Output(component_id="main-page-content", component_property="children"),
#     [
#         Input(component_id="url", component_property="pathname"),
#     ],
# )
# @inject
# def render_page(
#         pathname,
#         session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
# ):
#     from page_layout import (
#         TestPageLayoutStrategy, LoginPageLayoutStrategy,
#         Login2faPageStrategy, SuccessPageStrategy, LogoutPageStrategy,
#         LoggedInPageStrategy
#     )
#
#     # TODO: Understand how to use URL rewriting to track sessions in plotly dash
#     # TODO: How to incorporate OpenTelemetry
#     # TODO: Use dynaconf for managing the configuration.
#     # TODO: Read about zope component architecture
#
#     page_strategies = {
#         f"{os.getenv('DASH_BASE_PATH', '/')}test_page": TestPageLayoutStrategy(),
#         f"{os.getenv('DASH_BASE_PATH', '/')}login": LoginPageLayoutStrategy(),
#         f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa": Login2faPageStrategy(),
#         f"{os.getenv('DASH_BASE_PATH', '/')}success": SuccessPageStrategy(),
#         # f"{os.getenv('DASH_BASE_PATH', '/')}logout": LogoutPageStrategy(),
#     }
#     return page_strategies.get(pathname, LoggedInPageStrategy(pathname, session_factory)).render()

@callback(
    Output(component_id="header-page-content", component_property="children"),
    Input(component_id="url", component_property="pathname"),
)
@inject
def render_page_header(
        pathname,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    from page_layout import (
        TestPageLayoutStrategy, LoginPageLayoutStrategy,
        Login2faPageStrategy, SuccessPageStrategy, LogoutPageStrategy,
        LoggedInPageStrategy
    )

    # TODO: Understand how to use URL rewriting to track sessions in plotly dash
    # TODO: How to incorporate OpenTelemetry
    # TODO: Use dynaconf for managing the configuration.
    # TODO: Read about zope component architecture

    page_strategies = {
        f"{os.getenv('DASH_BASE_PATH', '/')}test_page": TestPageLayoutStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}login": LoginPageLayoutStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa": Login2faPageStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}success": SuccessPageStrategy(),
        # f"{os.getenv('DASH_BASE_PATH', '/')}logout": LogoutPageStrategy(),
    }
    return page_strategies.get(pathname, LoggedInPageStrategy(pathname, session_factory)).render_header()


@callback(
    Output(component_id="main-page-content", component_property="children"),
    Input(component_id="url", component_property="pathname"),
)
@inject
def render_page_header(
        pathname,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    from page_layout import (
        TestPageLayoutStrategy, LoginPageLayoutStrategy,
        Login2faPageStrategy, SuccessPageStrategy, LogoutPageStrategy,
        LoggedInPageStrategy
    )

    # TODO: Understand how to use URL rewriting to track sessions in plotly dash
    # TODO: How to incorporate OpenTelemetry
    # TODO: Use dynaconf for managing the configuration.
    # TODO: Read about zope component architecture

    page_strategies = {
        f"{os.getenv('DASH_BASE_PATH', '/')}test_page": TestPageLayoutStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}login": LoginPageLayoutStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa": Login2faPageStrategy(),
        f"{os.getenv('DASH_BASE_PATH', '/')}success": SuccessPageStrategy(),
        # f"{os.getenv('DASH_BASE_PATH', '/')}logout": LogoutPageStrategy(),
    }
    return page_strategies.get(pathname, LoggedInPageStrategy(pathname, session_factory)).render_content()


# @socketio.on('connect')
# def handle_connect():
#     my_logger = MyLogger().get_logger()
#     my_logger.info(f"{current_user} User closed browser")
#     print(f'Client connected')


# @socketio.on('disconnect')
# def handle_disconnect():
#     my_logger = MyLogger().get_logger()
#     my_logger.info(f"{current_user} User closed browser")
#     print('Client disconnected')


# @socketio.on('message')
# def handle_message(message):
#     print('Received message: ', message)
#     socketio.send('Echo: ' + message)


# @dash_app.server.route(f"{os.getenv('DASH_BASE_PATH', '/')}admin")
# def admin_index():
#     print(f"Admin route: {dash_app.server.url_map}")
#     return 'admin.index'


container = custom_container.AppContainer()
container.wire(modules=["callbacks", __name__])

dash_app.container = container

if __name__ == '__main__':
    # TODO check out redlines python package. it gives diff between 2 lines.
    # TODO using https://dashboard.guardrails.io/bb/visby8em/repos for code analysis
    if os.name == "nt":
        # This variable is required to be set for Celery to run on Windows
        # os.environ.setdefault('FORKED_BY_MULTIPROCESSING', '1')

        # aiodns (used in aiohttp) library requires asyncio loop to be a SelectorEventLoop,
        # which is not the default on Windows since Python 3.8
        # The default is changed as follows. This should be done very early in your application

        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        # context.load_cert_chain(
        #     certfile=r'c:\vishal\certs\DESKTOP-RBFFEPG+4.pem',
        #     keyfile=r'c:\vishal\certs\DESKTOP-RBFFEPG+4-key.pem'
        # )

        # dash_app.run(
        #     debug=True, port=443, host="localhost",
        #     ssl_context=context
        # )
        dash_app.run(debug=True, host="localhost", port="1951")
        # server.run(debug=True, port=1951, host="localhost")
        # server.run(host='localhost', port=1951, debug=True)
        # socketio.run(dash_app.server, host='localhost', port=1951, allow_unsafe_werkzeug=True)
    else:
        # server.run(debug=True, port=1951, threaded=True, use_reloader=True)
        dash_app.run_server(debug=True, port=1951, host="localhost")
