/* Modern Header Styles */

/* Reset and base styles for modern header */
.modern-header {
    position: relative;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 70px;
}

/* Left Section - Brand Logo */
.header-left {
    display: flex;
    align-items: center;
    min-width: 200px;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.brand-logo:hover {
    color: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
}

.brand-icon {
    font-size: 2rem;
    color: #fff;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.brand-text {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: -0.5px;
}

/* Center Section - Navigation */
.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 800px;
}

.main-navigation {
    width: 100%;
    position: relative;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
    width: 24px;
    height: 3px;
    background: white;
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Navigation Menu - Override existing styles */
.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    justify-content: center;
    gap: 0;
    background: transparent !important;
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
}

.nav-menu > li {
    position: relative;
    display: inline-block;
}

.nav-menu > li > a {
    display: flex;
    align-items: center;
    padding: 12px 20px !important;
    color: white !important;
    text-decoration: none;
    font-size: 14px !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 2px;
    border: none !important;
    background: transparent !important;
}

.nav-menu > li > a:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    transform: translateY(-1px);
}

.nav-menu > li > a.mega-menu {
    position: relative;
}

.nav-menu > li > a.mega-menu > span:after {
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid white;
    margin-left: 8px;
    display: inline-block;
    vertical-align: middle;
    transition: all 0.3s ease;
}

.nav-menu > li:hover > a.mega-menu > span:after {
    border-top: 0;
    border-bottom: 4px solid white;
    transform: rotate(180deg);
}

/* Sub Menu Styles */
.nav-menu > li > div.sub-menu-block {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-width: 600px;
    max-width: 800px;
    padding: 2rem;
    margin-top: 8px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-menu > li:hover > div.sub-menu-block {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.sub-menu-block .row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.sub-menu-block .col-md-4 {
    flex: 1;
    min-width: 180px;
}

.sub-menu-head {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.sub-menu-lists {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sub-menu-lists > li {
    margin-bottom: 0.5rem;
}

.sub-menu-lists > li > a {
    display: block;
    padding: 8px 12px;
    color: #666 !important;
    text-decoration: none;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.sub-menu-lists > li > a:hover {
    background: #f8f9fa;
    color: #1890ff !important;
    transform: translateX(4px);
}

/* Right Section - Actions and User Menu */
.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 200px;
    justify-content: flex-end;
}

.header-action-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.action-icon {
    font-size: 1.2rem;
    color: white;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ff4d4f;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

/* User Menu Positioning in Header */
.header-right .user-menu-container {
    position: relative;
}

.header-right .user-dropdown-menu {
    right: 0;
    left: auto;
    transform: translateY(-10px);
    margin-top: 8px;
}

.header-right .user-dropdown-menu.show {
    transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 1rem;
    }
    
    .brand-text {
        display: none;
    }
    
    .nav-menu > li > a {
        padding: 12px 16px !important;
        font-size: 13px !important;
    }
    
    .sub-menu-block {
        min-width: 500px;
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .header-container {
        height: 60px;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .nav-menu {
        position: fixed !important;
        top: 60px;
        left: 0;
        right: 0;
        background: white !important;
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        max-height: calc(100vh - 60px);
        overflow-y: auto;
    }
    
    .nav-menu.mobile-open {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-menu > li {
        width: 100%;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .nav-menu > li:last-child {
        border-bottom: none;
    }
    
    .nav-menu > li > a {
        color: #333 !important;
        padding: 16px 0 !important;
        margin: 0;
        border-radius: 0;
    }
    
    .nav-menu > li > a:hover {
        background: #f8f9fa !important;
        color: #1890ff !important;
    }
    
    .nav-menu > li > div.sub-menu-block {
        position: static;
        transform: none;
        box-shadow: none;
        border: none;
        border-radius: 0;
        padding: 1rem 0;
        margin: 0;
        background: #f8f9fa;
        opacity: 1;
        visibility: visible;
        min-width: auto;
        max-width: none;
    }
    
    .sub-menu-block .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-right {
        gap: 0.5rem;
    }
    
    .header-action-btn {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 0.5rem;
    }
    
    .brand-icon {
        font-size: 1.5rem;
    }
    
    .header-action-btn {
        display: none;
    }
}
