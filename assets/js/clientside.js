const publicKeyPem = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArnl8hOURAy10XBO1l4NOnN9fnUjnJM6ONIyzqXvpC5SAsgu0XwP7e9exsWg+rHIs4fBhCpaHq3fAK79UxEs8HQ7BOgAmnv6l9e2NQC7SShWUCAxRS591QFfYf+1F9zkJcXL9v2GN+dRHYjPhZ8yaZ0hWdiE4Vb39O1s6PYC8m77MrUPiHrFB+/qQ4+q/pzskFKHV2y2jq29Ili2LlxxFDdSZR/2LEOSqpWYcZefrTJslZuXliJD70UWxSyU0iwtMext0RocM7Fh/85TEQGPfjLEwqD1H6CcODMqtYJ/6aXfXtuIw6oQRp5wFqQDrWmmVzy5fRWXA3gK+7y745AVVLwIDAQAB
-----END PUBLIC KEY-----`;


function str2ab(str) {
    const buf = new ArrayBuffer(str.length);
    const bufView = new Uint8Array(buf);
    for (let i = 0, strLen = str.length; i < strLen; i++) {
        bufView[i] = str.charCodeAt(i);
    }
    return buf;
}

function ab2hex(buf) {
    return Array.prototype.map.call(new Uint8Array(buf), x => ('00' + x.toString(16)).slice(-2)).join('');
}

async function encryptWithPublicKey(modulusB64, exponentB64, data) {

// New addition
var publicKeyDERBase64 = "MIIBCgKCAQEA0YGVOOxqq4pIdLdobenEmaKrnJJV72gzdLNfngJMnY0qXC5lLXVPr6EMIYwOFxLQP5DpV9Sdg58ah2m3H0tJ896umGKWO/EiLgRtkl+IlTd03UqKZ6GAC3Fa8qOwzxVQ3psQ0CidZvyewoar2mnAkOBYS+/nxN8Vxujs2jVQDp6KoUWGJWpofpSEjCXgKAmN12b35Nv/1lPwp0NLumZm2q1XUHV89fXDK6DDZkuiwBdxL81rG3yvUPQUvWJg06udUfSYAu4tfN2Ghrnb00LQJTZkNBmLzbnygp/o8vE2WaN++LaTG7AEYwODhtqw4n5CAIB4noFs/BMigvkKYT3oiQIDAQAB";

var publicKeyDERBytes = new Uint8Array(atob(publicKeyDERBase64).split('').map(c => c.charCodeAt(0)));
console.log(Array.prototype.map.call(publicKeyDERBytes, x => ('00' + x.toString(16)).slice(-2)).join(''),);
// Parse the DER-encoded public key
var publicKey = KEYUTIL.getKey({
    hex: Array.prototype.map.call(publicKeyDERBytes, x => ('00' + x.toString(16)).slice(-2)).join(''),
    format: 'pkcs8pub'
});

console.log(publicKey);

var dataToEncrypt = "Hello, World!";

// Encrypt data using the public key
//var encryptedDataHex = rsaEncrypt(publicKey, dataToEncrypt);
//console.log("Encrypted data:", encryptedDataHex);

// Function to encrypt data with RSA public key
function rsaEncrypt(publicKey, data) {
    var cipher = new KJUR.crypto.Cipher();
    cipher.setPublicKey(publicKey);
    var encryptedDataHex = cipher.encrypt(data);
    return encryptedDataHex;
}

// Function to convert Uint8Array to hexadecimal string
function hexArrayBufferToHex(arrayBuffer) {
    var uint8Array = new Uint8Array(arrayBuffer);
    var hexString = '';
    for (var i = 0; i < uint8Array.length; i++) {
        var hex = (uint8Array[i]).toString(16);
        hex = hex.length === 1 ? '0' + hex : hex;
        hexString += hex;
    }
    return hexString.toUpperCase();
}

// End addition

    // Decode base64 strings to bytes
    function base64ToHex(str) {
        var raw = atob(str);
        var result = '';
        for (var i = 0; i < raw.length; i++) {
            var hex = raw.charCodeAt(i).toString(16);
            result += (hex.length === 2 ? hex : '0' + hex);
        }
        return result.toUpperCase();
    }

    // Decode base64 to hexadecimal
    var modulusHex = base64ToHex(modulusB64);
    var exponentHex = base64ToHex(exponentB64);

    // Create RSAKey object and set public key
        var rsa = new RSAKey();
        rsa.setPublic(modulusHex, exponentHex);
        console.log("set public key" + rsa);

        var encrypted = rsa.encrypt(data);
        console.log(encrypted);
        return encrypted;
    }

async function encryptPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);

    // Convert PEM to binary DER format
    const pemHeader = "-----BEGIN PUBLIC KEY-----";
    const pemFooter = "-----END PUBLIC KEY-----";
    let pemContents = publicKeyPem.substring(pemHeader.length, publicKeyPem.length - pemFooter.length);
//    pemContents = pemContents.replace(/(\r\n|\n|\r)/gm, '');
//    let pemContents = publicKeyPem;

    const binaryDerString = window.atob(pemContents);
    const binaryDer = str2ab(binaryDerString);

    const key = await window.crypto.subtle.importKey(
        "spki",
        binaryDer,
        {
            name: "RSA-OAEP",
            hash: { name: "SHA-256" }
        },
        false,
        ["encrypt"]
    );

    const encrypted = await window.crypto.subtle.encrypt(
        {
            name: "RSA-OAEP"
        },
        key,
        data
    );
    console.log("encrypted=" + encrypted);

    return ab2hex(encrypted);
}

function customCastStatus(val) {
    if(val == 'Open') {
        return 1;
    }
    else if(val == 'In Progress') {
        return 2;
    }
    else if(val == 'Rejected') {
        return 3;
    }
    else if(val == 'Fixed') {
        return 4;
    }
    else if(val == 'Verified') {
        return 5;
    }
    else if(val == 'Future Release') {
        return 6;
    }
    else if(val == 'On Hold') {
        return 7;
    }
    else
        return 10;
}

function customCastPriority(val) {
    if( val == 'Show Stopper') {
        return 1;
    }
    else if(val == 'Critical') {
        return 2;
    }
    else if(val == 'Urgent') {
        return 2;
    }
    else if(val == 'High') {
        return 3;
    }
    else if(val == 'Medium') {
        return 4;
    }
    else if(val == 'Low') {
        return 5;
    }
    else
        return 6;
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
    clientside: {
//        socket_connection: function(id) {
//            var socket = io({pingTimeout: 60000});
//            socket.on('connect', function() {
//                socket.emit('connection', {data: 'I am connected!'});
//            });
//        },
        swiper_credit_card: function(id) {
            console.log("Hello World");
            const swiper = new Swiper('.swiper', {
                speed: 400,
                spaceBetween: 100,
                direction: 'horizontal',
                loop: true,
                scrollbar: {
                    el: '.swiper-scrollbar',
                    draggable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
//                    renderBullet: function (index, className) {
//                        return '<span class="' + className + '">' + (index + 1) + '</span>';
//                    }
                }
            });
            return window.dash_clientside.no_update;
        },

        filter_table: function(id) {
            var tfConfig = {
                base_path: 'https://unpkg.com/tablefilter@latest/dist/tablefilter/',
                linked_filters: true,
                col_0: 'none',
                col_2: 'select',
                col_3: 'select',
                col_4: 'select',
                col_6: 'select',
                col_7: 'select',
//                col_8: 'none',
//                col_9: 'none',
//                col_12: 'select',
                col_13: 'select',
                col_14: 'none',
                // enables as you type filter behavior
                auto_filter: {
                    delay: 1100 //milliseconds
                },
                msg_filter: 'Filtering...',
                watermark: [, 'Summary', , , ,'>7', , ,'Affects Version', 'Fix Version', , , , ,'Components'],
                status_bar: true,
                btn_reset: true,
                clear_filter_text: '< Reset >',
                enable_default_theme: true,

                rows_counter: {
                    text: 'issues: '
                },
                no_results_message: true,

                // highlight matched keyword
                highlight_keywords: true,

                col_types: [
                    'string', 'string', 'custom_status', 'custom',
                    'custom', 'number', 'string', 'string', 'string',
                    'string', 'string', 'string', 'number',
                    'string', 'string', 'string', 'string', 'string',
                ],
                extensions: [
                {
                    name: 'sort',
                    on_sort_loaded: function(o, sort) {
                        sort.addSortType('custom', customCastPriority);
                        sort.addSortType('custom_status', customCastStatus);
                    }
                }
                ],
            }
            var tf = new TableFilter(document.getElementById(id), tfConfig);
            tf.init();
            return window.dash_clientside.no_update;
        },

        streaming_GPT: async function streamingGPT(n_clicks, prompt) {

            // id of the window we want to write the response to
            // you may use dynamically created id's here if you have multiple windows
            // eg "#response-window-${element_id}"
            const responseWindow = document.querySelector("#response-window");

            // "marked.js" is used to parse the incoming stream
            // it is also a good idea to state in the prompt that the "response should be markdown formatted"
            // this definition changes the color scheme of the parsed code. If your use-case does not include parsing code, you can remove this part, as well as "asssets/external/highlight.min.js" and "asssets/external/markdown-code.css"
            // if your application use-case includes parsing code and wish to change color scheme of the parsed code, you can do so in "asssets/external/markdown-code.css"
            // alternatively, you can go to "https://highlightjs.org/static/demo/" to find a theme you like and then download it from "https://github.com/highlightjs/highlight.js/tree/main/src/styles"
            marked.setOptions({
                highlight: function(code) {
                    return hljs.highlightAuto(code).value;
                }
            });

            // Send the messages to the server to get the streaming response
            // if you have more parameters python side, you can add them to the body
            // eg. body: JSON.stringify({ prompt, parameter1, parameter2 }),
            const response = await fetch("/streaming-chat", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ prompt }),
            });

            // Create a new TextDecoder to decode the streamed response text
            const decoder = new TextDecoder();

            // Set up a new ReadableStream to read the response body
            const reader = response.body.getReader();
            let chunks = "";

            // Read the response stream as chunks and append them to the chat log
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                chunks += decoder.decode(value);
                const htmlText = marked.parse(chunks); // this line will parse the incoming stream as markdown text, using "marked.js" package
                responseWindow.innerHTML = htmlText;
            }

            // return false to enable the submit button again (disabled=false)
            return false;
          },
        toggle_password: function(input_value) {
            var pwd = document.getElementById('id-login-passwd-main');
            var toggleIcon = document.getElementById('toggle-password');
            if (pwd.type === 'password') {
                pwd.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                pwd.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    }
});