/**
 * Modern Logout Menu JavaScript
 * Handles dropdown menu interactions and logout functionality
 */

// Logout menu namespace
window.modernLogout = {
    // Configuration
    config: {
        countdownDuration: 10, // seconds
        autoRedirect: true
    },

    // State
    state: {
        dropdownOpen: false,
        countdownTimer: null,
        countdownSeconds: 10
    },

    // Initialize logout functionality
    init: function() {
        this.bindEvents();
        this.initCountdown();
        this.setupAccessibility();
    },

    // Bind event listeners
    bindEvents: function() {
        // Dropdown toggle
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('#user-menu-trigger');
            const dropdown = document.getElementById('user-dropdown-menu');
            
            if (trigger) {
                e.preventDefault();
                this.toggleDropdown();
            } else if (!e.target.closest('.user-dropdown-menu')) {
                this.closeDropdown();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDropdown();
            }
        });

        // Logout button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#logout-button')) {
                e.preventDefault();
                this.confirmLogout();
            }
        });

        // Profile and settings links (placeholder functionality)
        document.addEventListener('click', (e) => {
            if (e.target.closest('#profile-link')) {
                e.preventDefault();
                this.showNotification('Profile functionality coming soon!', 'info');
            }
            
            if (e.target.closest('#settings-link')) {
                e.preventDefault();
                this.showNotification('Settings functionality coming soon!', 'info');
            }
        });
    },

    // Toggle dropdown menu
    toggleDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        if (this.state.dropdownOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    },

    // Open dropdown menu
    openDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        this.state.dropdownOpen = true;
        trigger.classList.add('active');
        dropdown.classList.add('show');
        
        // Focus first menu item for accessibility
        const firstItem = dropdown.querySelector('.dropdown-item');
        if (firstItem) {
            setTimeout(() => firstItem.focus(), 100);
        }
    },

    // Close dropdown menu
    closeDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        this.state.dropdownOpen = false;
        trigger.classList.remove('active');
        dropdown.classList.remove('show');
    },

    // Confirm logout with modern dialog
    confirmLogout: function() {
        // Create modern confirmation dialog
        const dialog = this.createConfirmDialog();
        document.body.appendChild(dialog);
        
        // Show dialog with animation
        setTimeout(() => {
            dialog.classList.add('show');
        }, 10);
    },

    // Create confirmation dialog
    createConfirmDialog: function() {
        const dialog = document.createElement('div');
        dialog.className = 'logout-confirm-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay"></div>
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="iconify" data-icon="mdi:logout" style="font-size: 2rem; color: #ff4d4f;"></i>
                </div>
                <h3 class="dialog-title">Sign Out</h3>
                <p class="dialog-message">Are you sure you want to sign out of your account?</p>
                <div class="dialog-actions">
                    <button class="dialog-button secondary" onclick="modernLogout.cancelLogout(this)">
                        Cancel
                    </button>
                    <button class="dialog-button primary" onclick="modernLogout.performLogout(this)">
                        Sign Out
                    </button>
                </div>
            </div>
        `;
        
        // Add styles if not already present
        if (!document.getElementById('logout-dialog-styles')) {
            const style = document.createElement('style');
            style.id = 'logout-dialog-styles';
            style.textContent = `
                .logout-confirm-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                
                .logout-confirm-dialog.show {
                    opacity: 1;
                    visibility: visible;
                }
                
                .dialog-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(4px);
                }
                
                .dialog-content {
                    background: white;
                    border-radius: 12px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    position: relative;
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                }
                
                .logout-confirm-dialog.show .dialog-content {
                    transform: scale(1);
                }
                
                .dialog-icon {
                    margin-bottom: 1rem;
                }
                
                .dialog-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                    color: #1a1a1a;
                }
                
                .dialog-message {
                    color: #666;
                    margin-bottom: 2rem;
                    line-height: 1.5;
                }
                
                .dialog-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                }
                
                .dialog-button {
                    padding: 10px 20px;
                    border-radius: 6px;
                    border: none;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 100px;
                }
                
                .dialog-button.primary {
                    background: #ff4d4f;
                    color: white;
                }
                
                .dialog-button.primary:hover {
                    background: #ff7875;
                }
                
                .dialog-button.secondary {
                    background: #f5f5f5;
                    color: #666;
                }
                
                .dialog-button.secondary:hover {
                    background: #e6f7ff;
                    color: #1890ff;
                }
            `;
            document.head.appendChild(style);
        }
        
        return dialog;
    },

    // Cancel logout
    cancelLogout: function(button) {
        const dialog = button.closest('.logout-confirm-dialog');
        dialog.classList.remove('show');
        setTimeout(() => {
            dialog.remove();
        }, 300);
    },

    // Perform logout
    performLogout: function(button) {
        const dialog = button.closest('.logout-confirm-dialog');
        
        // Show loading state
        button.innerHTML = '<i class="iconify" data-icon="mdi:loading" style="animation: spin 1s linear infinite;"></i> Signing out...';
        button.disabled = true;
        
        // Redirect to logout endpoint
        setTimeout(() => {
            const basePath = window.dashBasePath || '/';
            window.location.href = basePath + 'logout';
        }, 1000);
    },

    // Initialize countdown on logout page
    initCountdown: function() {
        const countdownElement = document.getElementById('logout-countdown');
        if (!countdownElement) return;

        this.state.countdownSeconds = this.config.countdownDuration;
        this.updateCountdown();
        
        this.state.countdownTimer = setInterval(() => {
            this.state.countdownSeconds--;
            this.updateCountdown();
            
            if (this.state.countdownSeconds <= 0) {
                this.redirectToLogin();
            }
        }, 1000);
    },

    // Update countdown display
    updateCountdown: function() {
        const countdownElement = document.getElementById('logout-countdown');
        if (!countdownElement) return;

        if (this.state.countdownSeconds > 0) {
            countdownElement.textContent = `Redirecting to login in ${this.state.countdownSeconds} seconds...`;
        } else {
            countdownElement.textContent = 'Redirecting...';
        }
    },

    // Redirect to login page
    redirectToLogin: function() {
        if (this.state.countdownTimer) {
            clearInterval(this.state.countdownTimer);
        }
        
        const basePath = window.dashBasePath || '/';
        window.location.href = basePath + 'login';
    },

    // Setup accessibility features
    setupAccessibility: function() {
        // Add ARIA attributes
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (trigger && dropdown) {
            trigger.setAttribute('aria-haspopup', 'true');
            trigger.setAttribute('aria-expanded', 'false');
            dropdown.setAttribute('role', 'menu');
            
            // Update aria-expanded when dropdown state changes
            const observer = new MutationObserver(() => {
                const isOpen = dropdown.classList.contains('show');
                trigger.setAttribute('aria-expanded', isOpen.toString());
            });
            
            observer.observe(dropdown, { attributes: true, attributeFilter: ['class'] });
        }
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `modern-notification ${type}`;
        notification.innerHTML = `
            <i class="iconify" data-icon="mdi:information" style="margin-right: 8px;"></i>
            ${message}
        `;
        
        // Add styles if not present
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .modern-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    border-left: 4px solid #1890ff;
                    z-index: 10001;
                    animation: slideInRight 0.3s ease-out;
                }
                
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.modernLogout.init();
    });
} else {
    window.modernLogout.init();
}

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernLogout = window.modernLogout;
