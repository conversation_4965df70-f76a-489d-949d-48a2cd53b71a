# -*- coding: utf-8 -*-
"""
callback functions attached to dash_app layout
"""
import base64
import re
import sys
import os
import uuid
import io

import openai
from dateutil.relativedelta import relativedelta
import flask
# import jwt
from flask import send_file, request, Response, session, jsonify

import numpy as np
import logging

# from sklearn.feature_extraction.text import TfidfVectorizer
# from sklearn.pipeline import make_pipeline
# from sklearn.svm import SVC
from sqlalchemy import select, func
from sqlalchemy.exc import IntegrityError, ProgrammingError
from werkzeug.utils import secure_filename

import custom_container
import data.custom_logger
# from dash_app import dash_app, cache, session
from dash import (
    html, dash_table, callback_context, Patch, clientside_callback, callback
)
from dash.dependencies import Input, Output, State, MATCH, ALL, ClientsideFunction
from dash.exceptions import PreventUpdate

import data.restapi as api
import data.get_from_db as db
import pandas as pd

import time
from data import helper
from datetime import datetime, timedelta, date
import plotly.express as px
from dash import dcc
import plotly.graph_objs as go
from plotly.subplots import make_subplots
from collections import defaultdict, namedtuple

from data.decorators import cache
from data.helper import make_progress_graph

from bs4 import BeautifulSoup
import requests
from cryptography.hazmat.primitives import serialization

from dbmodels import PGStatActivity, RequestTracker

from pykeepass import PyKeePass as pkp
from data.report import CreateReport, get_data
from dependency_injector.wiring import inject, Provide
from dependency_injector import containers
from data.custom_logger import MyLogger
from redis import Redis
import magic

# from dash_extensions.javascript import assign

num_to_words = ('zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine')

keepass = custom_container.KPDatabaseOpener(
    os.getenv("DATABASE_PATH"),
    master_keyfile=os.getenv("MASTER_KEYFILE")
)
kpk_inst = custom_container.KeePassPasswordManager(keepass)

# MSAL helper_function
home_path = os.getenv('HOME', 'c:/vishal/KeyPass')
ref = pkp(
    filename=os.path.join(home_path, 'Database.kdbx'),
    keyfile=os.path.join(home_path, 'Database.key')
)

# dash_app = get_app()
# cache = Cache(
#     dash_app.server,
#     config={
#         "DEBUG": True,
#         'CACHE_TYPE': RedisCache.__name__,
#         'CACHE_REDIS_URL': os.environ.get('REDIS_URL', ''),
#         "CACHE_DEFAULT_TIMEOUT": 600
#     }
# )



# lottie_notfound = "https://assets5.lottiefiles.com/packages/lf20_0zomy8eb.json"
# options = dict(loop=True, autoplay=True, rendererSettings=dict(preserveAspectRatio='xMidYMid slice'))


# Toggle side menu panel

        # return "format-button active"


# @callback(
#     Output("id-side-menu-panel", "className"),
#     Output("id-main-display-panel", "className"),
#     Output("id-toggle-button", "className"),
#     Input("id-toggle-button", "n_clicks"),
# )
# def toggle_side_panel(count):
#     user_clicked = clicked(dash.callback_context)
#     if user_clicked not in ['id-toggle-button', 'id-toggle-button-version']:
#         raise PreventUpdate
#     if user_clicked == 'id-toggle-button':
#         if count % 2 == 0:
#             return "side-menu", "display-area", "format-button"
#         else:
#             return "side-menu hidden", "display-area expand-grid", "format-button active"


# Based on the selection of board fetch Active, Future and Closed sprints
@callback(
    Output("id-active-sprint", "options"),
    Output("id-future-sprint", "options"),
    Output("id-closed-sprint", "options"),
    # Output("id-fetch-time-sprints", "children"),
    Input("id-sprint-board-values", "value"),
)
@cache.memoize(helper.SHORT_TIME_OUT)
def update_sprint_values(board: int):
    if board is None:
        raise PreventUpdate
    start_time_ns = time.perf_counter_ns()

    nfr = helper.TimeTaken()
    my_logger = MyLogger().get_logger()

    nfr.func_name = "update_sprint_values"

    sprint_list, nfr.api_time['get_sprint_details'] = api.get_sprint_details(board)

    active_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "active"]
    future_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "future"]
    closed_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "closed"]

    print(f'update_sprint_values Time Taken: {(time.perf_counter_ns() - start_time_ns) * pow(10, -9):.3f} seconds')
    nfr.total_time = f'{(time.perf_counter_ns() - start_time_ns) * pow(10, -9):.3f}'
    my_logger.info(f'{nfr}')
    return active_sprint, future_sprint, closed_sprint  # , daq.LEDDisplay(value=time_secs, color="#088A08", size=16)


# Update title tab with Sprint name
@callback(
    Output("id-overview-main-header", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    [
        State("id-sprint-board-values", "value"),
        State("id-sprint-board-values", "options"),
        State("id-active-sprint", "options"),
        State("id-future-sprint", "options"),
        State("id-closed-sprint", "options"),
    ]
)
def update_overview_header(active_value, future_value, closed_value, board_value, board_state, active_state,
                           future_state, closed_state):
    user_clicked = clicked(callback_context)

    if user_clicked is None:
        raise PreventUpdate

    value_dict = {
        "id-active-sprint": active_value, "id-future-sprint": future_value, "id-closed-sprint": closed_value
    }

    board_label = [x['label'] for x in board_state if x['value'] == board_value]
    sprint_label = [x['label'] for x in active_state + future_state + closed_state if
                    x['value'] == value_dict[user_clicked]]

    return html.Label(
        children=[
            f"{board_label[0]} (Board Id: {board_value}) and {sprint_label[0]} (Sprint Id: {value_dict[user_clicked]})"],
        className="format-text-header"
    )


@callback(
    Output("id-active-sprint", "value"),
    Output("id-future-sprint", "value"),
    Output("id-closed-sprint", "value"),
    Output("id-fetch-time-sprints", "value"),
    Output("id-overview-layer-1", "children"),
    Output("id-overview-layer-2", "children"),
    Output("id-overview-layer-3", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    Input("url", "pathname"),
    [
        State("id-sprint-board-values", "value"),
    ]
)
@cache.memoize(timeout=helper.TIME_OUT)
def update_sprint_details(active, future, closed, pathname, boardId):
    user_clicked = clicked(callback_context)

    if user_clicked is None:
        raise PreventUpdate
    my_logger = MyLogger().get_logger()

    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    value_dict = {
        "id-active-sprint": [active, [active, "", ""]],
        "id-future-sprint": [future, ["", future, ""]],
        "id-closed-sprint": [closed, ["", "", closed]]
    }

    start_time_ns = time.perf_counter_ns()
    sprintId = value_dict[user_clicked][0]
    retValue = value_dict[user_clicked][1]

    resp = api.get_scope_change_burndown_chart(boardId, sprintId)

    for key in ["changes", "statisticField", "issueToParentKeys", "issueToSummary", "workRateData", "openCloseChanges"]:
        resp.pop(key, None)

    parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
    for key, value in resp.items():
        if 'lastUserWhoClosedHtml' in key:
            parser.feed(resp['lastUserWhoClosedHtml'])
            resp[key] = ",".join(parser.all_data)
        elif "warningMessage" in key:
            pass
        else:
            resp[key] = time.strftime('%d %b %Y', time.localtime(value / 1000))

    if 'lastUserWhoClosedHtml' not in resp:
        resp['lastUserWhoClosedHtml'] = 'NA'
    if 'completeTime' not in resp:
        resp['completeTime'] = 'NA'

    epic_list, issue_list, aggregatetimeoriginalestimate, storypoint = \
        api.get_board_issues_for_sprint(boardId, sprintId)
    my_logger.info(
        f'Time taken in get_board_issues_for_sprint: {(time.perf_counter_ns() - start_time_ns) * pow(10, -9)} seconds')

    # df = db.get_epic_linked_issues(project, epic_list)

    time_secs = 0
    retValue.append(time_secs)

    df_issue_list = db.get_issue_counts(project)

    df_issue_list = df_issue_list[(df_issue_list['sprintid'] == sprintId)].copy(deep=True)
    df_story = df_issue_list[(df_issue_list['issuetype'] == 'Story')].copy(deep=True)
    df_task = df_issue_list[(df_issue_list['issuetype'] == 'Task')].copy(deep=True)
    df_bug = df_issue_list[(df_issue_list['issuetype'].isin(['Bug']))].copy(deep=True)
    df_story = df_story[['statuscategory', 'count']]
    df_story = df_story.groupby('statuscategory').sum().reset_index()
    # df_story.drop(columns=['sprint', 'issuetype', 'components'], inplace=True)
    df_task = df_task[['statuscategory', 'count']].groupby('statuscategory').sum().reset_index()

    # df_task.drop(columns=['sprint', 'issuetype', 'components'], inplace=True)
    df_bug = df_bug[['statuscategory', 'count']].groupby('statuscategory').sum().reset_index()
    # df_bug.drop(columns=['sprint', 'issuetype', 'components'], inplace=True)

    # df_components = df_issue_list[(df_issue_list['sprint'] == the_label[0])].copy(deep=True)
    df_components = df_issue_list[['components', 'count']].groupby('components').sum().reset_index()
    df_estimate = df_issue_list[['components', 'originalestimate']].groupby('components').sum().reset_index()
    row_estimate, col_estimate = df_estimate.shape

    df_storypoints = df_issue_list[['components', 'storypoints']].groupby('components').sum().reset_index()
    row_sp, _ = df_storypoints.shape

    df_initiative_estimate = df_issue_list[['initiative', 'originalestimate']].groupby('initiative').sum().reset_index()
    row_init_est, _ = df_initiative_estimate.shape

    df_initiative_sp = df_issue_list[['initiative', 'storypoints']].groupby('initiative').sum().reset_index()
    row_init_sp, _ = df_initiative_sp.shape

    start_issue_ns = time.perf_counter_ns()
    fig_height = 200
    fig1 = px.bar(df_story, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  height=fig_height, width=350, text='count'
                  )
    fig2 = px.bar(df_task, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  title="Task",
                  height=fig_height, width=350, text='count'
                  )
    fig3 = px.bar(df_bug, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  title="Bug",
                  height=fig_height, width=350, text='count'
                  )
    fig4 = px.bar(df_components, x='components', y='count',
                  title="Count of Components", text='count', height=300,
                  )
    fig4.layout._config = dict(responsive=True)
    fig5 = {}
    if row_estimate > 0:
        fig5 = px.bar(df_estimate, x='components', y='originalestimate',
                      title="Original Effort Estimate by Components", height=300, text='originalestimate', width=1000
                      )
        fig5.update_layout(
            title=dict(x=0.5, y=0.9, xanchor='center', yanchor='top'),
            font_family="Courier New", title_font_family="Arial"
        )
        fig5.update_xaxes(type='category', showgrid=False)
        fig5.update_traces(width=0.2)

    fig6 = {}
    if row_sp > 0:
        fig6 = px.bar(df_storypoints, x='components', y='storypoints',
                      title="Story points by Components", height=300, text='storypoints', width=1000
                      )
        fig6.update_layout(
            title=dict(x=0.5, y=0.9, xanchor='center', yanchor='top'),
            font_family="Rajdhani", title_font_family="Arial"
        )
        fig6.update_xaxes(type='category', showgrid=False)
        fig6.update_traces(width=0.2)

    # Add code
    fig7 = {}
    if row_init_est > 0:
        fig7 = px.bar(df_initiative_estimate, x='initiative', y='originalestimate',
                      title="Estimation by Initiative", height=300, text='originalestimate', width=1000
                      )
    fig8 = {}
    if row_init_sp > 0:
        fig8 = px.bar(df_initiative_sp, x='initiative', y='storypoints',
                      title="Story points by Initiative", height=300, text='storypoints', width=1000
                      )
    # end code add

    fig1.update_layout(
        title=dict(text='Story', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig1.update_xaxes(type='category', showgrid=False)
    fig1.update_traces(width=0.2)

    fig2.update_layout(
        title=dict(text='Task', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig2.update_xaxes(type='category', showgrid=False)
    fig2.update_traces(width=0.2)

    fig3.update_layout(
        title=dict(text='Bug', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig3.update_xaxes(type='category', showgrid=False)
    fig3.update_traces(width=0.2)

    fig4.update_layout(
        title=dict(text='Components', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial", autosize=True
    )
    fig4.update_xaxes(type='category', showgrid=False)
    fig4.update_traces(width=0.2)

    main_child_list = [
        html.Table(children=[
            html.Tr(children=[
                html.Td(html.Div(f"Start Date: {resp['startTime']}")),
                html.Td(html.Div(f"End Date: {resp['endTime']}")),
                html.Td(f"Completion Date: {resp['completeTime']}"),
                html.Td(f"Completed by: {resp['lastUserWhoClosedHtml']}"),
                html.Td(f"Total Original estimate(h): {aggregatetimeoriginalestimate}"),
                html.Td(f"Total story points: {storypoint}")
            ]),
        ]),
        html.Table(children=[
            html.Tr(children=[
                html.Td(html.Div(dcc.Graph(figure=fig1)), ),
                html.Td(html.Div(dcc.Graph(figure=fig2)), ),
                html.Td(html.Div(dcc.Graph(figure=fig3)), ),
            ]),
        ]),
        html.Table(children=[
            html.Tr(children=[
                html.Td(dcc.Graph(figure=fig4), ),
            ]),
        ]),
    ]

    retValue.append(main_child_list)

    retValue.append(html.Table(children=[
        html.Tr(children=[html.Td(dcc.Graph(figure=fig5))]),
        html.Tr(children=[html.Td(dcc.Graph(figure=fig6))])
    ]))

    retValue.append(html.Table(children=[
        html.Tr(children=[html.Td(dcc.Graph(figure=fig7))]),
        html.Tr(children=[html.Td(dcc.Graph(figure=fig8))])
    ]))
    # _row, _col = df.shape
    # if _row > 0:
    #     df2 = df[[
    #         "parentkey", "key", "issuetype", "components", "assignee", "Team",
    #         "statusCategory", "status", "aggregatetimeoriginalestimate", "aggregatetimespent",
    #         "aggregateprogress_percent", "progress_percent"]].copy(deep=True)
    #     df2.rename(columns={
    #         "parentkey": "Epic#",
    #         "key": "Issue#",
    #         "aggregatetimeoriginalestimate": "Σ Original Estimate",
    #         "aggregatetimespent": "Σ Time Spent",
    #         "aggregateprogress_percent": "Σ progress %"
    #     }, inplace=True)
    #
    #     col_list = []
    #     percentage = dash_table.FormatTemplate.percentage(0)
    #     for c in df2.columns:
    #         if c in ['Σ progress %', 'progress_percent']:
    #             col_list.append(
    #                 dict(id=c, name=c, selectable=True, type='numeric', format=percentage)
    #             )
    #         else:
    #             col_list.append(
    #                 {"name": c, "id": c, "selectable": True}
    #             )
    #
    #     retValue.append([
    #         dash_table.DataTable(
    #             id='datatable-interactivity',
    #             # columns=[{"name": i, "id": i, "deletable": True, "selectable": True, } for i in df2.columns],
    #             columns=col_list,
    #             data=df2.to_dict(orient='records'),
    #             export_format="csv",
    #             # editable=False,
    #             filter_action="native",
    #             sort_action="native",
    #             sort_mode="multi",
    #             # column_selectable="single",
    #             # row_selectable="multi",
    #             page_action="native",
    #             page_current=0,
    #             page_size=15,
    #             style_cell_conditional=[
    #                 {
    #                     'if': {'column_id': c},
    #                     'textAlign': 'right',
    #                     'format': 'percentage',
    #                     'type': 'numeric'
    #                 } for c in ['Σ progress %', 'progress_percent']
    #             ],
    #         )
    #     ])
    # else:
    #     retValue.append(["No data!!!"])

    end_time_ns = time.perf_counter_ns()

    return retValue


# update layer 4 & 5
@callback(
    Output("id-overview-layer-5", "children"),
    Output("id-overview-layer-4", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    Input("id-team-sprint", "value"),
    Input("id-status-sprint", "value"),
    Input("url", "pathname"),
    [
        # State("id-team-sprint", "value"),
        # State("id-status-sprint", "value"),
        State("id-sprint-board-values", "value"),
        # State("id-active-sprint", "options"),
        # State("id-future-sprint", "options"),
        # State("id-closed-sprint", "options"),
        State("id-active-sprint", "value"),
        State("id-future-sprint", "value"),
        State("id-closed-sprint", "value"),
    ]
)
@inject
def update_overview_layer_4_5(
        active, future, closed, team_name, status, pathname,
        boardId,  # active_chosen, future_chosen, closed_chosen,
        active_value, future_value, closed_value,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    user_clicked = clicked(callback_context)
    if user_clicked is None:
        raise PreventUpdate
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    value_dict = {
        "id-active-sprint": active_value, "id-future-sprint": future_value, "id-closed-sprint": closed_value,
        "id-status-sprint": [v for v in [active_value, future_value, closed_value] if v != ""][0],
        "id-team-sprint": [v for v in [active_value, future_value, closed_value] if v != ""][0]
    }

    sprintId = value_dict[user_clicked] if user_clicked in value_dict.keys() else None

    # if sprint is not selected but Team or status drop down is selected.
    # raise PreventUpdate exception
    if sprintId is None:
        raise PreventUpdate
    with session_factory.db_ro().session() as pg_session:

        updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
        pg_session.bind = updated_engine
        resp = api.get_scope_change_burndown_chart(boardId, sprintId)
        start_date = time.strftime("%Y-%m-%d", time.localtime(resp['startTime'] / 1000))
        end_date = time.strftime("%Y-%m-%d", time.localtime(resp['endTime'] / 1000))

        time_taken_start = time.perf_counter_ns()
        df = db.get_worklog_details(start_date, end_date, pg_session)

        df = df[df['Team Name'].notna()]

        start_date = datetime.fromtimestamp(resp['startTime'] / 1000)
        end_date = datetime.fromtimestamp(resp['endTime'] / 1000)

        df_team = df.loc[(df['Team Name'] == team_name)].copy(deep=True)
        df_team = df_team[['Name', status, 'Time Spent (h)']].copy(deep=True)

        df_team = df_team.groupby(['Name', status]).sum().reset_index()

        format_list = []
        date_list = [html.Div(children=["Weekday"], className="days-grid")]
        month_list = [html.Div(children=["month"], className="days-grid")]
        for single_date in helper.daterange(start_date, end_date):
            weekno = single_date.weekday()
            if weekno < 5:
                date_list.append(html.Div(children=[single_date.strftime("%a")], className="days-grid"))
                month_list.append(html.Div(children=[single_date.strftime("%b")], className="days-grid"))
            else:
                date_list.append(html.Div(children=[single_date.strftime("%a")], className="days-grid weekend"))
                month_list.append(html.Div(children=[single_date.strftime("%b")], className="days-grid weekend"))

        date_list.append(html.Div(children=[""], className="days-grid"))
        month_list.append(html.Div(children=[''], className="days-grid"))

        format_list.extend(date_list)
        format_list.extend(month_list)
        date_list = format_list

        date_list.append(html.Div(["Name/Day"], className="days-grid highlightrow"))

        for single_date in helper.daterange(start_date, end_date):
            weekno = single_date.weekday()
            if weekno < 5:
                date_list.append(
                    html.Div(children=[single_date.strftime("%d")], className="days-grid highlightrow")
                )
            else:
                date_list.append(
                    html.Div(children=[single_date.strftime("%d")], className="days-grid weekend highlightrow")
                )
        date_list.append(html.Div(["Comp %"], className="days-grid highlightrow"))

        df_team[status] = pd.to_datetime(df_team[status])

        for name in df_team['Name'].unique():
            df_name = df_team[(df_team['Name'] == name)].copy(deep=True)
            name_list = [html.Div(children=[name], className="days-grid")]
            print(f'{name}:{df_name.shape}')
            count = 0

            for single_date in helper.daterange(start_date, end_date):
                time_spent = df_name[(df_name[status].dt.date == single_date.date())]['Time Spent (h)'].sum()
                if time_spent > 0:
                    count += 1
                weekno = single_date.weekday()
                if weekno < 5:
                    name_list.append(html.Div(children=[time_spent], className="days-grid"))
                else:
                    name_list.append(html.Div(children=[time_spent], className="days-grid weekend"))
            if end_date.date() < datetime.now().date():
                total_days = np.busday_count(start_date.date(), end_date.date())
            else:
                total_days = np.busday_count(start_date.date(), datetime.now().date())

            print(f'{total_days} - {start_date}:{end_date}')
            name_list.append(html.Div(children=["{:.2%}".format(count / total_days)], className="days-grid"))
            date_list.extend(name_list)

    return [dash_table.DataTable(
        id='datatable-worklog-details',
        columns=[
            {"name": i, "id": i, "selectable": True, } for i in df.columns
        ],
        data=df[(df['Team Name'] == team_name)].to_dict(orient="records"),
        export_format="csv",
        # editable=False,
        filter_action="native",
        sort_action="native",
        # sort_mode="multi",
        # column_selectable="single",
        # row_selectable="multi",
        page_action="native",
        page_current=0,
        page_size=10,
        style_cell={'textAlign': 'left'},
        style_cell_conditional=[
            {
                'if': {'column_id': 'Time Spent (h)'},
                'textAlign': 'right'
            }
        ],
        style_as_list_view=True,
        style_header={
            'backgroundColor': 'rgb(210, 210, 210)',
            'color': 'black',
            'fontWeight': 'bold'
        },
        style_data={
            'color': 'black',
            'backgroundColor': 'white'
        },
        style_data_conditional=[
            {
                'if': {'row_index': 'odd'},
                'backgroundColor': 'rgb(220, 220, 220)',
            }
        ],
    )], date_list


@callback(
    Output("id-overview-bullet-1", "className"),
    Output("id-overview-bullet-2", "className"),
    Output("id-overview-bullet-3", "className"),
    Output("id-overview-bullet-4", "className"),
    Output("id-overview-bullet-5", "className"),
    Output("id-overview-layer-1", "className"),
    Output("id-overview-layer-2", "className"),
    Output("id-overview-layer-3", "className"),
    Output("id-overview-layer-4", "className"),
    Output("id-overview-layer-5", "className"),
    Output("id-label-team", "className"),
    Output("id-team-sprint", "className"),
    Output("id-label-view", "className"),
    Output("id-status-sprint", "className"),
    Input("id-overview-bullet-1", "n_clicks"),
    Input("id-overview-bullet-2", "n_clicks"),
    Input("id-overview-bullet-3", "n_clicks"),
    Input("id-overview-bullet-4", "n_clicks"),
    Input("id-overview-bullet-5", "n_clicks")
)
def toggle_cards(id_1, id_2, id_3, id_4, id_5):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    if click_event == "id-overview-bullet-1":
        return "active", "", "", "", "", "layer one show", "layer two", "layer three", "layer four", "layer five", "hide", "dcc-dropdown hide", "hide", "dcc-dropdown hide"
    elif click_event == "id-overview-bullet-2":
        return "", "active", "", "", "", "layer one", "layer two show", "layer three", "layer four", "layer five", "hide", "dcc-dropdown hide", "hide", "dcc-dropdown hide"
    elif click_event == "id-overview-bullet-3":
        return "", "", "active", "", "", "layer one", "layer two", "layer three show", "layer four", "layer five", "", "dcc-dropdown", "", "dcc-dropdown"
    elif click_event == "id-overview-bullet-4":
        return "", "", "", "active", "", "layer one", "layer two", "layer three", "layer four show", "layer five", "", "dcc-dropdown", "", "dcc-dropdown"
    elif click_event == "id-overview-bullet-5":
        return "", "", "", "", "active", "layer one", "layer two", "layer three", "layer four", "layer five show", "", "dcc-dropdown", "", "dcc-dropdown"


# This callback styles the data table
# @callback(
#     Output('datatable-interactivity', 'style_data_conditional'),
#     Input('datatable-interactivity', 'selected_columns')
# )
# def update_styles(selected_columns):
#     if selected_columns is None:
#         raise PreventUpdate
#     print(selected_columns)
#     return [{
#         'if': {'column_id': i},
#         'background_color': '#D2F3FF'
#     } for i in selected_columns]


# Changes done for /plat/version

@server.route("/plat/version/download")
@inject
def download_excel_version(
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    version_lbl = flask.request.args.get('value').split(",")

    if version_lbl is None:
        raise PreventUpdate
    with session_factory.db_connections()['ro']()()['plat'].session() as pg_session:
        df = db.get_release_status(version_lbl, pg_session)

    buf = io.BytesIO()
    excel_writer = pd.ExcelWriter(buf, engine="xlsxwriter")
    df.to_excel(excel_writer, sheet_name="sheet1", index=False)
    excel_writer.save()
    excel_data = buf.getvalue()
    buf.seek(0)
    print(df.shape)
    return send_file(
        buf,  # mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        attachment_filename="version_details.xlsx",
        as_attachment=True,
        cache_timeout=0
    )


# Match if table row is clicked

@callback(
    Output({'type': 'child-row', 'index': MATCH}, "className"),
    Output({'type': 'main-row', 'index': MATCH}, "className"),
    Input({'type': 'main-row', 'index': MATCH}, "n_clicks")
)
def show_hidden_rows(click_value):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    if click_value % 2 == 0:
        return "fold", "view"
    else:
        return "show", "view open"


# @callback(
#     Output("id-version-search", "value"),
#     Output("id-versions", "value"),
#     Input("id-version-released-value", "value"),
#     Input("id-version-archive-value", "value"),
#     Input("keyboard", "n_keydowns"),
#     State("id-version-search+", "value"),
#     State("keyboard", "keydown"),
#     State("url", "pathname")
# )
# def get_versions_by_pattern_keyboard(released: int, archived: int, n_keydowns, pattern: str, event, pathname: str):
#     click_event = clicked(dash.callback_context)
#
#     if pattern is None:
#         raise PreventUpdate
#     print(click_event)
#
#     if event['key'] != 'Enter':
#         raise PreventUpdate
#     print(f'Type: {type(n_keydowns)}:{type(event)}')
#     project = pathname.split("/")[1]
#     pattern = pattern if '%' in pattern else pattern + '%'
#     pattern_list = db.get_matching_versions(pattern, bool(released), bool(archived), project)
#     return "", pattern_list

@callback(
    Output({'type': 'status-rpt-drop-down', 'index': MATCH}, 'value'),
    Output({'type': 'status-rpt-header', 'index': MATCH}, 'value'),
    Input({'type': 'status-rpt-drop-down-pattern', 'index': MATCH}, 'n_submit'),
    Input({'type': 'status-rpt-search', 'index': MATCH}, "n_clicks"),
    Input("url", "pathname"),
    State({'type': 'status-rpt-drop-down-pattern', 'index': MATCH}, 'value'),
)
@inject
def populate_selected_version_pattern(
        n_submit, n_clicks, pathname: str, filter_string: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    result_add = []
    result_remove = []

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
    with session_factory.db_conn_provider().session() as pg_session:
        final_list = db.get_all_active_versions(pg_session)

    filter_list = filter_string.split(",")
    filter_list = [x.strip() for x in filter_list]
    for f in filter_list:
        if f.strip().startswith('~'):
            result_remove += [x for x in final_list if x.startswith(f[1:])]
        else:
            result_add += [x for x in final_list if x.startswith(f)]

    result = list(set(result_add) - set(result_remove))
    versions = [tuple(map(int, v.split('.'))) for v in result]
    # Find the minimum and maximum versions
    min_version = min(versions)
    max_version = max(versions)

    # Convert the tuples back to strings
    min_version_str = '.'.join(map(str, min_version))
    max_version_str = '.'.join(map(str, max_version))

    return result, f"Release Status - {min_version_str} to {max_version_str}"


# Show issue aging
@callback(
    Output("id-issuetype-graph-1", "figure"),
    Output("id-issuetype-graph-2", "figure"),
    Output("id-issuetype-graph-3", "figure"),
    Output("id-epic-issue", "value"),
    Output("id-standard-issue", "value"),
    Output("id-subtask-issue", "value"),
    Input("id-epic-issue", "value"),
    Input("id-standard-issue", "value"),
    Input("id-subtask-issue", "value"),
    Input("id-issuetype-when", "value"),
    Input("url", "pathname")
)
def show_issue_aging_graph(
        epic, standard, subtask, timeframe, pathname

):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    ret_dropdown = {
        "id-epic-issue": [epic, "", ""],
        "id-standard-issue": ["", standard, ""],
        "id-subtask-issue": ["", "", subtask],
        "id-issuetype-when": [epic, standard, subtask]
    }

    ret_value = {
        "id-epic-issue": epic,
        "id-standard-issue": standard,
        "id-subtask-issue": subtask,
        "id-issuetype-when": epic if epic != "" else standard if standard != "" else subtask
    }

    selected_issue = ret_value[click_event]

    df = db.get_open_issues(project)
    # filter value and show graph
    dff = df[(df['issuetype'] == selected_issue)].copy(deep=True)

    dff[timeframe] = datetime.now().date() - dff[timeframe]

    dff[timeframe] = dff[timeframe].dt.days
    # round off to nearest 10s
    dff[timeframe + '_bin'] = dff[timeframe].apply(lambda x: helper.custom_round(x, base=30))

    dff_bubble = dff[['status', 'initiative']].copy()
    dff_bubble = dff_bubble.groupby(['status', 'initiative']).size().reset_index(name='Total')

    dff_bubble_bin = dff[['initiative', timeframe + '_bin']].copy()
    dff_bubble_bin = dff[dff[timeframe + '_bin'] > 0].copy()
    dff_bubble_bin = dff_bubble_bin.groupby(['initiative', timeframe + '_bin']).size().reset_index(name='Total')

    fig1 = px.scatter(dff_bubble_bin, x='initiative', y=timeframe + '_bin', size='Total', color='initiative', )
    fig2 = px.scatter(dff_bubble, x='status', y='initiative', size='Total', color='initiative',
                      hover_name='initiative')
    fig3 = px.scatter(dff, x='status', y=timeframe)
    return fig1, fig2, fig3, *ret_dropdown[click_event]


@callback(
    Output("id-aging-bullet-1", "className"),
    Output("id-aging-bullet-2", "className"),
    Output("id-aging-bullet-3", "className"),
    Output("id-issuetype-graph-1", "className"),
    Output("id-issuetype-graph-2", "className"),
    Output("id-issuetype-graph-3", "className"),
    Input("id-aging-bullet-1", "n_clicks"),
    Input("id-aging-bullet-2", "n_clicks"),
    Input("id-aging-bullet-3", "n_clicks"),
)
def toggle_aging_cards(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    if click_event == 'id-aging-bullet-1':
        return "active", "", "", "layer one show", "layer two", "layer three"
    elif click_event == 'id-aging-bullet-2':
        return "", "active", "", "layer one", "layer two show", "layer three"
    elif click_event == 'id-aging-bullet-3':
        return "", "", "active", "layer one", "layer two", "layer three show"


@callback(
    Output("id-team-member-desc", "options"),
    Output("id-team-member-desc", "value"),
    Input("id-team-desc", "value"),
    Input("url", "pathname"),
)
@inject
def populate_team_member(
        team_name: str, pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': project})
    with session_factory.db_conn_provider().session() as pg_session:
        df = db.get_team_members(team_name, project, pg_session)

    options = [{'label': 'ALL', 'value': 'ALL'}] + [{'value': row.accountId, 'label': row.displayName} for row in
                                                    df.itertuples()]

    return options, 'ALL'


@callback(
    Output("id-comp-description-5", "children"),
    Input("id-team-desc", "value"),
    Input("url", "pathname"),
    Input("id-desc-date-picker-range", "start_date"),
    Input("id-desc-date-picker-range", "end_date")

)
def compliance_description_stats(
        team_name: str, pathname: str,
        start_date: datetime.date, end_date: datetime.date
):
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    df = db.get_subtask_issue_description_stats(project, start_date, end_date)

    prev_sat = datetime.now().date() - timedelta(days=datetime.now().weekday()) + timedelta(days=5, weeks=-2)
    last_fri = datetime.now().date() - timedelta(days=datetime.now().weekday()) + timedelta(days=4, weeks=-1)
    last_sat = datetime.now().date() - timedelta(days=datetime.now().weekday()) + timedelta(days=5, weeks=-1)

    first_day_of_current_month = datetime.now().date().replace(day=1)
    last_month_last_day = first_day_of_current_month - timedelta(days=1)
    start_day_of_prev_month = datetime.today().date().replace(day=1) - timedelta(days=last_month_last_day.day)

    df_last_week = df[df['created'].between(prev_sat, last_fri)]
    df_curr_week = df[df['created'] > last_fri]

    df_last_month = df[df['created'].between(start_day_of_prev_month, last_month_last_day)]
    df_curr_month = df[df['created'] >= first_day_of_current_month]

    table_last_week = pd.pivot_table(df_last_week, index=['Team'], columns=['Quality'], values=['Count'],
                                     aggfunc=np.sum, fill_value=0)
    table_last_week.reset_index(inplace=True)

    table_curr_week = pd.pivot_table(df_curr_week, index=['Team'], columns=['Quality'], values=['Count'],
                                     aggfunc=np.sum, fill_value=0)
    table_curr_week.reset_index(inplace=True)

    table_last_month = pd.pivot_table(df_last_month, index=['Team'], columns=['Quality'], values=['Count'],
                                      aggfunc=np.sum, fill_value=0)
    table_last_month.reset_index(inplace=True)

    table_curr_month = pd.pivot_table(df_curr_month, index=['Team'], columns=['Quality'], values=['Count'],
                                      aggfunc=np.sum, fill_value=0)
    table_curr_month.reset_index(inplace=True)

    for col in table_last_week.columns:
        table_last_week[col] = table_last_week[col].apply(apply_td)

    for col in table_curr_week.columns:
        table_curr_week[col] = table_curr_week[col].apply(apply_td)

    for col in table_last_month.columns:
        table_last_month[col] = table_last_month[col].apply(apply_td)

    for col in table_curr_month.columns:
        table_curr_month[col] = table_curr_month[col].apply(apply_td)

    return html.Div(
        children=[
            html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Table(
                                children=[
                                    html.Caption(
                                        children=[
                                            html.Span(
                                                children=[
                                                    html.Time(
                                                        dateTime=prev_sat.strftime('%Y-%m-%d'),
                                                        className="date-as-calendar position-em size0_75x",
                                                        children=[
                                                            html.Span(prev_sat.strftime('%a'), className="weekday"),
                                                            html.Span(prev_sat.strftime('%d'), className="day"),
                                                            html.Span(prev_sat.strftime('%B'), className="month"),
                                                            html.Span(prev_sat.strftime('%Y'), className="year"),
                                                        ]
                                                    ), ], className='column'
                                            ),
                                            html.Span(
                                                children=[html.Time(
                                                    dateTime=datetime.today().strftime('%Y-%m-%d'),
                                                    className="date-as-calendar position-em size0_75x",
                                                    children=[
                                                        html.Span(last_fri.strftime('%a'), className="weekday"),
                                                        html.Span(last_fri.strftime('%d'), className="day"),
                                                        html.Span(last_fri.strftime('%B'), className="month"),
                                                        html.Span(last_fri.strftime('%Y'), className="year"),
                                                    ]
                                                )], className='column'
                                            ),
                                        ]
                                        # children=[
                                        #     html.Label(f"Last Week - From: {prev_sat.strftime('%a, %b %d')} To: {last_fri.strftime('%a, %b %d')}")
                                        # ]
                                    ),
                                    html.Tr([html.Th(col) for col in table_last_week.columns]),
                                    *[html.Tr(i) for i in table_last_week.values]
                                ],
                                className='format-table'
                            ),
                        ], className="column"
                    ),
                    html.Div(
                        children=[
                            html.Table(
                                children=[
                                    html.Caption(
                                        children=[
                                            html.Span(
                                                children=[html.Time(
                                                    dateTime=last_sat.strftime('%Y-%m-%d'),
                                                    className="date-as-calendar position-em size0_75x",
                                                    children=[
                                                        html.Span(last_sat.strftime('%a'), className="weekday"),
                                                        html.Span(last_sat.strftime('%d'), className="day"),
                                                        html.Span(last_sat.strftime('%B'), className="month"),
                                                        html.Span(last_sat.strftime('%Y'), className="year"),
                                                    ]
                                                ), ], className='column'
                                            ),
                                            html.Span(
                                                children=[html.Time(
                                                    dateTime=datetime.today().strftime('%Y-%m-%d'),
                                                    className="date-as-calendar position-em size0_75x",
                                                    children=[
                                                        html.Span(datetime.today().strftime('%a'), className="weekday"),
                                                        html.Span(datetime.today().strftime('%d'), className="day"),
                                                        html.Span(datetime.today().strftime('%B'), className="month"),
                                                        html.Span(last_sat.strftime('%Y'), className="year"),
                                                    ]
                                                )], className='column'
                                            ),
                                        ]
                                    ),
                                    html.Tr([html.Th(col) for col in table_curr_week.columns]),
                                    *[html.Tr(i) for i in table_curr_week.values]
                                ],
                                className='format-table'
                            ),

                        ], className="column"
                    ),
                ], className="row"
            ),
            html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Table(
                                children=[
                                    html.Caption(
                                        children=[
                                            html.Label(
                                                f"Last Month: {start_day_of_prev_month.strftime('%b %y')}")
                                        ]

                                    ),
                                    html.Tr([html.Th(col) for col in table_last_month.columns]),
                                    *[html.Tr(i) for i in table_last_month.values]
                                ],
                                className='format-table'
                            ),
                        ], className="column"
                    ),
                    html.Div(
                        children=[
                            html.Table(
                                children=[
                                    html.Caption(
                                        children=[
                                            html.Label(
                                                f"Current Month: {first_day_of_current_month.strftime('%b %y')}")
                                        ]
                                    ),
                                    html.Tr([html.Th(col) for col in table_curr_month.columns]),
                                    *[html.Tr(i) for i in table_curr_month.values]
                                ],
                                className='format-table'
                            ),

                        ], className="column"
                    ),
                ], className="row"
            ),
        ]
    )


@callback(
    Output("id-comp-description-3", "children"),
    Input("id-team-desc", "value"),
    Input("url", "pathname"),
    Input("id-desc-date-picker-range", "start_date"),
    Input("id-desc-date-picker-range", "end_date"),
    Input("id-team-member-desc", "value")
)
@inject
def compliance_description(
        team_name: str, pathname: str,
        start_date: datetime.date, end_date: datetime.date, account_id: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    # start_date_tzaware = datetime(2022, 5, 1, 0, 0, 0, 0, pytz.timezone("UTC"))
    # end_date_tzaware = datetime(2022, 5, 25, 0, 0, 0, 0, pytz.timezone("UTC"))
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': project})
    with session_factory.db_conn_provider().session() as pg_session:
        df = db.get_subtask_issue_description(project, start_date, end_date, pg_session)

    dff = df[(df['team'] == team_name)]
    if account_id != 'ALL':
        dff = dff[dff['accountId'] == account_id]
    dff.sort_values(['assignee', 'created'], inplace=True)
    dff.drop(columns=["team", "accountId"], inplace=True)
    if account_id != 'ALL':
        dff.drop(columns=['assignee'], inplace=True)
    dff['description'] = dff['description'].apply(apply_markdown)
    dff['key'] = dff['key'].apply(apply_href)

    for col in dff.columns:
        dff[col] = dff[col].apply(apply_td)

    return html.Div(html.Table(
        [html.Tr([html.Th(col) for col in dff.columns])] +
        [html.Tr(i) for i in dff.values.tolist()],
        className='format-table'
    ))


@callback(
    Output("id-timelog-bullet-2", "className"),
    Output("id-timelog-bullet-3", "className"),
    Output("id-timelog-2", "className"),
    Output("id-timelog-3", "className"),
    Input("id-timelog-bullet-2", "n_clicks"),
    Input("id-timelog-bullet-3", "n_clicks"),
)
def timelog_switch(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    layers = ["two", "three"]
    layer_name, dict_layer, dict_toggle_bullet = helper.generate_layer_bullet(layers, int(click_event.split('-')[3]))
    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name]


@callback(
    Output("id-timelog-team-member", "options", allow_duplicate=True),
    Input("id-timelog-team-name", "value"),
    State("store-timelog-df", "data"),
    prevent_initial_call=True
)
def timelog_update_team_member(
        team_name: str, df_json
):
    df = pd.read_json(df_json, orient="split")

    if team_name != 'ALL':
        df.query("team_name == @team_name", inplace=True)
    df_select = df[['author', 'team_member']].drop_duplicates(keep='first').sort_values(
        by=['team_member']).copy()

    team_member_option = [{'label': 'ALL', 'value': 'ALL'}] + [
        {'label': display_name, 'value': author} for author, display_name in df_select.values.tolist()
    ]

    return team_member_option


@callback(
    Output("id-timelog-2", "children", allow_duplicate=True),
    Output("id-time-log-data", "rowData", allow_duplicate=True),
    Input("id-get-timelog-filter", "n_clicks"),
    State("id-timelog-release-selector", "value"),
    State("id-timelog-team-name", "value"),
    State("id-timelog-team-member", "value"),
    State("store-timelog-df", "data"),
    prevent_initial_call=True
)
def update_graph_timelog(
        n_click, release: str, team_name: str, team_member: str,
        df_json
):
    if n_click > 0:
        df = pd.read_json(df_json, orient="split")
        df['started'] = pd.to_datetime(df['started']).dt.date
        df['week_start'] = pd.to_datetime(df['week_start']).dt.date

        if release != 'ALL':
            df.query("release == @release", inplace=True)

        if team_name != 'ALL':
            df.query("team_name == @team_name", inplace=True)

        if team_member != 'ALL':
            df.query("author == @team_member", inplace=True)

        if df.shape[0] > 0:
            # fig = px.histogram(
            #     df, x='week_start', y='effort_spent', color="release",
            #     barmode="group", text_auto=True,
            #     labels={'week_start': 'Day'},
            #     histfunc='sum'  # sum is the default function
            # )
            # fig.update_layout(bargap=0.2, yaxis_title_text='Hours')

            fig = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)
            dff = df.groupby(by=['week_start', 'release']).agg({'effort_spent': 'sum'}).sort_values(
                by=['week_start', 'release']).reset_index()

            if release == 'ALL':
                release_list = ["Professional Services", "Production Support", "Managed Services", "Non Billable"]
            else:
                release_list = release.split(",")

            for i, key in enumerate(release_list):
                if release == 'ALL':
                    dff_filter = df.query("release == @key").groupby(by=['week_start', 'release']).agg(
                        {'effort_spent': 'sum'}).sort_values(
                        by=['week_start', 'release']).reset_index()
                else:
                    dff_filter = df.groupby(by=['week_start', 'release']).agg({'effort_spent': 'sum'}).sort_values(
                        by=['week_start', 'release']).reset_index()

                fig.add_trace(
                    go.Bar(
                        x=dff_filter['week_start'],
                        y=dff_filter['effort_spent'],
                        text=dff_filter['effort_spent'],
                        name=key, offsetgroup=i
                    ), secondary_y=False
                )

            if team_member == 'ALL':
                dff = df[['started', 'week_start', 'team_name', 'author']].drop_duplicates(keep='first')
                dff_size = dff.groupby(['week_start']).agg({'author': 'nunique'}).reset_index()

                fig.add_trace(
                    go.Scatter(
                        name="Headcount",
                        x=dff_size['week_start'], y=dff_size['author'],
                        mode="lines+markers+text", text=dff_size['author'],
                        xperiod=604800000,
                        xperiodalignment="start",
                        connectgaps=True,
                        texttemplate='%{text:.1f}',
                        textposition='top right',
                    ),
                    secondary_y=True
                )

                dff_size = dff.groupby(['started', 'week_start']).agg({'author': 'size'}).reset_index()
                dff_size = dff_size.groupby(['week_start']).agg({'author': 'mean'}).reset_index()

                fig.add_trace(
                    go.Scatter(
                        name="Avg. Headcount",
                        x=dff_size['week_start'], y=dff_size['author'],
                        mode="lines+markers+text", text=dff_size['author'],
                        xperiod=604800000,
                        xperiodalignment="start",
                        connectgaps=True,
                        texttemplate='%{text:.1f}',
                        textposition='top right',
                    ),
                    secondary_y=True
                )

            return dcc.Graph(figure=fig, style={'width': '100wh', 'height': '100%'}), df.to_dict("records")
    else:
        raise PreventUpdate


@callback(
    Output("id-time-log-data", "exportDataAsCsv"),
    Input("timelog-csv-button", "n_clicks"),
)
def export_data_as_csv(n_clicks):
    if n_clicks:
        return True
    return False


@callback(
    Output("id-timelog-team-name", "options"),
    Output("id-timelog-team-member", "options"),
    Output("id-timelog-team-name", "value"),
    Output("id-timelog-team-member", "value"),
    Output("id-timelog-team-name", "disabled"),
    Output("id-timelog-team-member", "disabled"),
    Output("store-timelog-df", "data"),
    Output("id-timelog-2", "children"),
    Output("id-time-log-data", "rowData"),
    Input("id-get-timelog-details", "n_clicks"),
    State("url", "pathname"),
    State("id-timelog-date-picker-range", "start_date"),
    State("id-timelog-date-picker-range", "end_date"),
)
@inject
def populate_timelog_dropdown(
        n_clicks, pathname, start_date, end_date,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': schema_name})

        with session_factory.db_conn_provider().session() as pg_session:
            df = db.get_worklog_details_daterange(start_date, end_date, pg_session)

        df['team_name'] = df['team_name'].fillna('Un-mapped')

        team_name_option = [{'label': 'ALL', 'value': 'ALL'}] + [
            {'label': team_name, 'value': team_name} for team_name in sorted(df['team_name'].unique())
        ]

        df_select = df[['author', 'team_member']].drop_duplicates(keep='first').sort_values(
            by=['team_member']).copy()

        team_member_option = [{'label': 'ALL', 'value': 'ALL'}] + [
            {'label': display_name, 'value': author} for author, display_name in df_select.values.tolist()
        ]
        fig = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)

        dff = df.groupby(by=['week_start', 'release']).agg({'effort_spent': 'sum'}).sort_values(
            by=['week_start', 'release']).reset_index()

        for i, key in enumerate(["Professional Services", "Production Support", "Managed Services", "Non Billable"]):
            dff_filter = dff.query('release == @key').copy()
            fig.add_trace(
                go.Bar(
                    x=dff_filter['week_start'],
                    y=dff_filter['effort_spent'],
                    text=dff_filter['effort_spent'],
                    name=key, offsetgroup=i
                ), secondary_y=False
            )

        # start_date = df.query('release == "Professional Services"')['week_start'].min() - timedelta(days=7)
        # end_date = df.query('release == "Professional Services"')['week_start'].max() + timedelta(days=7)
        # fig.add_trace(
        #     go.Histogram(
        #         histfunc="sum",
        #         x=df.query('release == "Professional Services"')['week_start'],
        #         y=df.query('release == "Professional Services"')['effort_spent'],
        #         name="Professional Services",
        #         xbins=dict(
        #             start=start_date,
        #             end=end_date,
        #             size=604800000  # milli seconds
        #         ),
        #     ), secondary_y= False
        # )

        fig.update_traces(
            textfont_size=12, textangle=0, textposition="outside", cliponaxis=True,
        )
        fig.update_xaxes(
            rangeslider_visible=False,
            rangeselector=dict(
                buttons=list([
                    dict(count=1, label="1m", step="month", stepmode="backward"),
                    dict(count=6, label="6m", step="month", stepmode="backward"),
                    dict(count=1, label="YTD", step="year", stepmode="todate"),
                    dict(count=1, label="1y", step="year", stepmode="backward"),
                    dict(step="all")
                ])
            ),
            title="Week Ending"
        )

        dff = df[['week_start', 'author']]
        dff = dff.groupby(['week_start']).agg({'author': 'nunique'}).reset_index()

        fig.add_trace(
            go.Scatter(
                name="Headcount",
                x=dff['week_start'], y=dff['author'],
                mode="lines+markers+text", text=dff['author'],
                xperiod=604800000,
                xperiodalignment="start",
                connectgaps=True,
                texttemplate='%{text:.1f}',
                textposition='top right',
            ),
            secondary_y=True
        )

        dff = df[['started', 'week_start', 'team_name', 'author']].drop_duplicates(keep='first')
        dff = dff.groupby(['started', 'week_start']).agg({'author': 'size'}).reset_index()
        dff = dff.groupby(['week_start']).agg({'author': 'mean'}).reset_index()

        fig.add_trace(
            go.Scatter(
                name="Avg. Headcount",
                x=dff['week_start'], y=dff['author'],
                mode="lines+markers+text", text=dff['author'],
                xperiod=604800000,
                xperiodalignment="start",
                connectgaps=True,
                texttemplate='%{text:.1f}',
                textposition='top right',
            ),
            secondary_y=True
        )

        # print(
        #     json.dumps(dcc.Graph(style={'width': '100wh', 'height': '100%'}, id="id-timelog-services-chart").to_plotly_json())
        # )
        # print(fig)

        return [team_name_option, team_member_option, 'ALL', 'ALL', False, False,
                df.to_json(date_format='iso', orient='split'),
                dcc.Graph(figure=fig, style={'width': '100wh', 'height': '100%'}, id="id-timelog-services-chart"),
                df.to_dict("records")
                ]

    else:
        raise PreventUpdate


@callback(
    Output("id-desc-bullet-3", "className"),
    Output("id-desc-bullet-5", "className"),
    Output("id-comp-description-3", "className"),
    Output("id-comp-description-5", "className"),
    Input("id-desc-bullet-3", "n_clicks"),
    Input("id-desc-bullet-5", "n_clicks"),
)
def compliance_desc(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    layers = ['three', 'five']
    layer_name, dict_layer, dict_toggle_bullet = helper.generate_layer_bullet(layers, int(click_event.split('-')[3]))
    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name]


@callback(
    Output("id-comp-graph-2", "figure"),
    Input("id-team-comp", "value"),
    Input("url", "pathname"),
)
def compliance_components(team_name: str, pathname):
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    df = db.get_component_count_by_issuetype(project)

    dff = df[(df['team_name'] == team_name)]
    dff = dff[['created_month', 'components_meeting_sla', 'rowno']] \
        .groupby(['created_month']) \
        .agg({'components_meeting_sla': 'sum', 'rowno': 'sum'}).reset_index()

    dff['percent'] = dff['components_meeting_sla'] / dff['rowno']

    # fig = go.Figure()
    fig = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)
    fig.add_trace(
        go.Bar(
            name="Total Components",
            x=dff['created_month'], y=dff['rowno'],
            xperiod="M1",
            xperiodalignment="middle",
            text=dff['rowno']
        ),
        secondary_y=False
    )
    fig.add_trace(
        go.Bar(
            name="Components Populated",
            x=dff['created_month'], y=dff['components_meeting_sla'],
            xperiod="M1",
            xperiodalignment="middle",
            text=dff['components_meeting_sla']
        ),
        secondary_y=False
    )

    fig.add_trace(
        go.Scatter(
            name="Percentage",
            x=dff['created_month'], y=dff['percent'],
            mode="lines+markers+text",
            text=dff['percent'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )
    fig.update_xaxes(showgrid=True, ticklabelmode='period', tickformat="%b\n%Y", dtick="M1", )
    fig.update_yaxes(tickformat=',.0%', secondary_y=True)

    return fig


@callback(
    Output("id-comp-graph-3", "figure"),
    Input("id-team-comp", "value"),
    Input("url", 'pathname')
)
def compliance_subtasks(*args):
    project = args[1].split("/")[1]
    # df = db.get_subtasks_with_no_originalestimates(project)
    df = db.get_dev_subtask_details(project)
    dff = df[(df['team_name'] == args[0])]
    dff = dff.groupby('created_month'). \
        agg({'estimated_effort_lt_8': 'sum', 'actual_effort_lt_8': 'sum', 'key': 'count'}) \
        .reset_index()
    dff['percent_estimated'] = dff['estimated_effort_lt_8'] / dff['key']
    dff['percent_actual'] = dff['actual_effort_lt_8'] / dff['key']

    fig = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)

    fig.add_trace(
        go.Bar(
            name='Total',
            x=dff['created_month'], y=dff['key'],
            xperiod="M1",
            xperiodalignment="middle",
            text=dff['key'],
        ), secondary_y=False
    )

    fig.add_trace(
        go.Bar(
            name='estimated effort',
            x=dff['created_month'], y=dff['estimated_effort_lt_8'],
            xperiod="M1",
            xperiodalignment="middle",
            text=dff['estimated_effort_lt_8'],
        ), secondary_y=False
    )

    fig.add_trace(
        go.Bar(
            name='actual effort',
            x=dff['created_month'], y=dff['actual_effort_lt_8'],
            xperiod="M1",
            xperiodalignment="middle",
            text=dff['actual_effort_lt_8'],
        ), secondary_y=False
    )

    fig.add_trace(
        go.Scatter(
            name="Percentage Estimated",
            x=dff['created_month'], y=dff['percent_estimated'],
            mode="lines+markers+text",
            text=dff['percent_estimated'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )

    fig.add_trace(
        go.Scatter(
            name="Percentage Actual",
            x=dff['created_month'], y=dff['percent_actual'],
            mode="lines+markers+text",
            text=dff['percent_actual'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )

    fig.update_layout(barmode='group')
    fig.update_xaxes(showgrid=False, ticklabelmode='period', tickformat="%b\n%Y", dtick="M1", )
    fig.update_yaxes(tickformat=',.0%', secondary_y=True)

    return fig


@callback(
    Output("id-comp-graph-5", "figure"),
    Input("id-team-comp", "value"),
    Input("id-comp-bullet-5", "n_clicks"),
    Input("url", "pathname")
)
def update_subtask_more_than_8_hours(teamname: str, bullet, pathname):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    df = db.get_subtasks_hourslogged(project)
    df = df[df['teamname'].notna()].copy(deep=True)
    dff = df[df['teamname'] == teamname].copy(deep=True)

    fig = px.scatter(dff, x="author", y="issuetype", size="timelogged", color="key")
    # fig = go.Figure()
    # for name in dff['author'].unique():
    #     print(f'name = {name}')
    #     fig.add_trace(
    #         go.Bar(
    #         name=name,
    #         x=dff['issuetype'],
    #         y=dff['timelogged']),
    #     )
    return fig


@callback(
    Output("id-comp-graph-6", "figure"),
    Input("id-team-comp", "value"),
    Input("id-comp-bullet-6", "n_clicks"),
    Input("url", "pathname")
)
def show_ts_compliance(team_name: str, bullet: str, pathname: str):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    df = db.get_ts_compliance(project)

    dff = df[df['team_name'] == team_name].copy(deep=True)
    dff = dff.groupby(['created_month']) \
        .agg(
        {
            'sla-sameday': 'sum',
            'sla-nextday': 'sum',
            'sla-missed': 'sum',
            'key': 'count'
        }
    ).reset_index()

    dff['percent-sameday'] = dff['sla-sameday'] / dff['key']
    dff['percent-nextday'] = dff['sla-nextday'] / dff['key']
    dff['percent-missed'] = dff['sla-missed'] / dff['key']

    fig = make_subplots(specs=[[{"secondary_y": True}]], shared_xaxes=True)

    fig.add_trace(
        go.Bar(
            name='Same Day',
            x=dff['created_month'], y=(dff['sla-sameday']),
            xperiod="M1",
            xperiodalignment="middle",
            text=(dff['sla-sameday']),
        ), secondary_y=False
    )

    fig.add_trace(
        go.Bar(
            name='Next Day',
            x=dff['created_month'], y=(dff['sla-nextday']),
            xperiod="M1",
            xperiodalignment="middle",
            text=(dff['sla-nextday']),
        ), secondary_y=False
    )
    fig.add_trace(
        go.Bar(
            name='Missed',
            x=dff['created_month'], y=(dff['sla-missed']),
            xperiod="M1",
            xperiodalignment="middle",
            text=(dff['sla-missed']),
        ), secondary_y=False
    )

    fig.add_trace(
        go.Scatter(
            name="sameday %",
            x=dff['created_month'], y=(dff['percent-sameday']),
            mode="lines+markers+text",
            text=dff['percent-sameday'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )

    fig.add_trace(
        go.Scatter(
            name="nextday %",
            x=dff['created_month'], y=(dff['percent-nextday']),
            mode="lines+markers+text",
            text=dff['percent-nextday'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )

    fig.add_trace(
        go.Scatter(
            name="missed %",
            x=dff['created_month'], y=(dff['percent-missed']),
            mode="lines+markers+text",
            text=dff['percent-missed'] * 100,
            xperiod="M1",
            xperiodalignment="end",
            connectgaps=True,
            texttemplate='%{text:.1f}',
            textposition='top right'
        ),
        secondary_y=True
    )

    return fig


@callback(
    Output("id-comp-bullet-2", "className"),
    Output("id-comp-bullet-3", "className"),
    Output("id-comp-bullet-5", "className"),
    Output("id-comp-bullet-6", "className"),
    Output("id-comp-graph-2", "className"),
    Output("id-comp-graph-3", "className"),
    Output("id-comp-graph-5", "className"),
    Output("id-comp-graph-6", "className"),
    Output("id-compliance-header", "children"),
    Input("id-comp-bullet-2", "n_clicks"),
    Input("id-comp-bullet-3", "n_clicks"),
    Input("id-comp-bullet-5", "n_clicks"),
    Input("id-comp-bullet-6", "n_clicks"),
)
def comp_update_view(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    layers = ['two', 'three', 'five', 'six']
    # layer_name = num_to_words[int(click_event.split('-')[3])]
    layer_name, dict_layer, dict_toggle_bullet = helper.generate_layer_bullet(layers, int(click_event.split('-')[3]))

    # dict_toggle_bullet = {}
    # dict_layer = {}
    # for _, y in enumerate(layers):
    #     dict_toggle_bullet[y] = ["active" if j == y else "" for _, j in enumerate(layers)]
    #     dict_layer[y] = [f"layer {j} show" if j == y else f"layer {j}" for _, j in enumerate(layers)]
    #
    dict_title = dict(
        two="Component Availability at Story Level - Compliance %",
        three="subtask missing original estimate",
        five="",
        six=""
    )
    # list unpacking
    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name], dict_title[layer_name]


# Toggle for WD Timesheet
@callback(
    Output("id-isc-ts-bullet-2", "className"),
    Output("id-isc-ts-bullet-3", "className"),
    Output("id-isc-ts-bullet-5", "className"),
    Output("id-isc-ts-layer-2", "className"),
    Output("id-isc-ts-layer-3", "className"),
    Output("id-isc-ts-layer-5", "className"),
    Output("id-wd-ts-main-header", "children"),
    Input("id-isc-ts-bullet-2", "n_clicks"),
    Input("id-isc-ts-bullet-3", "n_clicks"),
    Input("id-isc-ts-bullet-5", "n_clicks"),
)
def toggle_isc_timesheet_layer(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    layers = ["two", "three", "five"]
    layer_name, dict_layer, dict_toggle_bullet = helper.generate_layer_bullet(layers, int(click_event.split('-')[-1]))

    dict_title = defaultdict(
        lambda: "",
        two="WD Timesheet", three="WD Effort Spent Chart", five="WD subtask Effort Spent"
    )

    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name], dict_title[layer_name]





@callback(
    Output("my-data-grid", "rowData"),
    Input("id-submit-code-review", "n_clicks"),
    State("id-sql-date-picker-range", 'start_date'),
    State("id-sql-date-picker-range", 'end_date'),
)
@inject
def sql_code_review_table(
        n_click,
        start_date, end_date,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    my_logger = MyLogger().get_logger()
    my_logger.debug(f"n_click = {n_click}")
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    if n_click > 0:
        print(f'Check if clicked {n_click}')
        my_logger.debug(f'Start date = {start_date}')
        my_logger.debug(f'End date = {end_date}')
        with session_factory.db_connections()['ro']()()['plat'].session() as pg_session:
            rows = db.get_sql_code_review_items(pg_session, start_date=start_date, end_date=end_date)
            if len(rows) > 0:
                df = pd.DataFrame(rows)
                my_logger.debug(f"Columns = {df.columns}")
                df['created'] = pd.to_datetime(df['created']).dt.date
            else:
                df = pd.DataFrame(
                    columns=['key', 'summary', 'description_markdown', 'Team', 'status', 'reporter',
                             'assignee', 'last_assignee', 'created']
                )

            my_logger.info(f'rows returned: {df.shape}')
            return df.to_dict("records")


# @callback(
#     Output("id-review-graph-child-1", "children"),
#     Output("id-review-graph-child-2", "children"),
#     Output("id-review-graph-child-3", "children"),
#     Output("id-review-graph-child-4", "children"),
#     Output("id-review-graph-3", "children"),
#     Input("id-code-version", "value")
# )
# @inject
# def sql_code_review(
#         version: str,
#         session_factory: db.AppContainer = Provide[db.AppContainer.session_factory],
# ):
#     click_event = clicked(dash.callback_context)
#     if click_event is None:
#         raise PreventUpdate
#
#     fig = [go.Figure(), go.Figure(), go.Figure(), go.Figure()]
#     with session_factory.db_connections()['ro']['plat'].session() as pg_session:
#         df = db.get_open_code_reviews(pg_session)
#     df_count = df[(df['affected'] == version)].copy()
#
#     create_dash_table = dash_table.DataTable(
#         id="id-dash-datatable-code-review",
#         columns=[
#             {"name": i, "id": i, "selectable": True, } for i in df_count.columns
#         ],
#         data=df_count.to_dict(orient="records"),
#         export_format="csv",
#         filter_action="native",
#         sort_action="native",
#         page_action="native",
#         page_current=0,
#         page_size=10,
#         style_cell={'textAlign': 'left', 'whiteSpace': 'normal', 'height': 'auto'},
#         style_cell_conditional=[
#             {
#                 'if': {'column_id': 'Time Spent (h)'},
#                 'textAlign': 'right'
#             }
#         ],
#         style_as_list_view=True,
#         style_header={
#             'backgroundColor': 'rgb(210, 210, 210)',
#             'color': 'black',
#             'fontWeight': 'bold'
#         },
#         style_data={
#             'color': 'black',
#             'backgroundColor': 'white'
#         },
#         style_data_conditional=[
#             {
#                 'if': {'row_index': 'odd'},
#                 'backgroundColor': 'rgb(220, 220, 220)',
#             }
#         ],
#     )
#
#     df_priority = {}
#     for idx, priority in enumerate(['Critical', 'High', 'Medium', 'Low']):
#         df_priority[idx] = df_count[(df_count['priority'] == priority)].copy()
#         df_priority[idx] = df_priority[idx][['status', 'key']].groupby(['status']).count().reset_index()
#         print(f'{idx} df of priority {priority}: {df_priority[idx].shape}')
#         row_count, _ = df_priority[idx].shape
#         if row_count > 0:
#             fig[idx] = px.bar(
#                 df_priority[idx], x='status', y='key',
#                 labels={'status': 'Status', 'key': 'Count'},
#                 title=f'{priority} Priority Open Issues', height=250, width=450, text='key',
#                 category_orders={"status": ['Open', 'In Progress', 'Fixed', 'Closed', 'Rejected', 'Cancelled']}
#             )
#             fig[idx].update_layout(
#                 title=dict(x=0.5, y=0.9, xanchor='center', yanchor='top'),
#                 font_family='Roboto', title_font_family="Rajdhani", font_size=12
#             )
#         else:
#             # https://community.plotly.com/t/replacing-an-empty-graph-with-a-message/31497/10
#             fig[idx] = {
#                 "layout": {
#                     "title": f"{priority} Priority Open Issues",
#                     "height": 250,
#                     "width": 470,
#                     "xaxis": {
#                         "visible": False
#                     },
#                     "yaxis": {
#                         "visible": False
#                     },
#                     "annotations": [
#                         {
#                             "text": "None",
#                             "xref": "paper",
#                             "yref": "paper",
#                             "showarrow": False,
#                             "font": {
#                                 "size": 12
#                             },
#
#                         }
#                     ]
#                 }
#             }
#
#     return [
#         dcc.Graph(figure=fig[0], className='box'), dcc.Graph(figure=fig[1], className='box'),
#         dcc.Graph(figure=fig[2], className='box'), dcc.Graph(figure=fig[3], className='box'),
#         html.Div(create_dash_table)
#     ]


@callback(
    # Output("id-review-bullet-3", "className"),
    Output("id-review-bullet-8", "className"),
    # Output("id-review-bullet-10", "className"),
    # Output("id-review-graph-3", "className"),
    Output("id-review-graph-8", "className"),
    # Output("id-review-graph-10", "className"),
    Output("id-sql-code-header", "children"),
    # Input("id-review-bullet-3", "n_clicks"),
    Input("id-review-bullet-8", "n_clicks"),
    # Input("id-review-bullet-10", "n_clicks"),
)
def code_review_update_view(*args):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    layers = ['eight']
    layer_name, dict_layer, dict_toggle_bullet = helper.generate_layer_bullet(layers, int(click_event.split('-')[3]))

    dict_title = dict(
        three="SQL Code Review Tasks Details",
        eight="SQL Code Review Details",
        ten="SQL Code Review Tasks Status",
    )

    return *dict_toggle_bullet[layer_name], *dict_layer[layer_name], dict_title[layer_name]


@callback(
    Output("id-file-output", "children"),
    Output("id-store-epic-list", "data"),
    Input("id-file-upload-epic", "contents"),
    Input("id-file-upload-epic", "filename")
)
def update_epic(contents, filename):
    if contents is None:
        raise PreventUpdate

    content_type, content_string = contents.split(',')
    decoded = base64.b64decode(content_string)

    df = None
    colnames = ['epic']
    try:
        if 'txt' in filename:
            df = pd.read_csv(
                io.StringIO(decoded.decode('unicode_escape')), sep=" ", header=None, names=colnames
            )
        elif 'csv' in filename:
            df = pd.read_csv(io.StringIO(decoded.decode('utf-8')), names=colnames)
        elif 'xlsx' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='openpyxl', names=colnames)
        elif 'xls' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='xlrd', names=colnames)
        elif 'ods' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='odf', names=colnames)

        cols = 10
        id_list = df['epic'].values
        id_list_new = [id_list[i:i + cols] for i in range(0, len(id_list), cols)]

        dff = pd.DataFrame(id_list_new)

        for col in dff.columns:
            dff[col] = dff[col].apply(apply_href)
            dff[col] = dff[col].apply(apply_td)
        return html.Div(
            children=[
                html.Table(
                    [html.Tr(i) for i in dff.values], className='format-table'
                )
            ]
        ), df.to_json(orient='split')
    except Exception as e:
        return html.Div(
            children=[
                html.Table(
                    children=[
                        html.Tr(children=[html.Th("Filename"), html.Th("Content Type"), html.Th("Error message")]),
                        html.Tr(children=[html.Td(filename), html.Td(content_type), html.Td(str(e))])
                    ], className="format-table"
                )
            ]
        ), None


# @callback(
#     Output("id-valid-login-name-epic", "children"),
#     Output("id-1st-level-epic", "className"),
#     Input("id-login-user-epic", "value")
# )
# def check_input_login_name(login_id: str):
#     if login_id == "":
#         raise PreventUpdate
#     my_logger = data.custom_logger.MyLogger().get_logger()
#     my_logger.debug("Check if valid login entered")
#
#     df = db.get_user_detail()
#     dff = df[df['emailAddress'] == login_id]
#     my_logger.debug(f"user name rows: {dff.shape[0]}")
#     if dff.shape[0] == 1:
#         return html.I(className="fa fa-check fa-3x green"), "active"
#     else:
#         return html.I(className="fa fa-times fa-3x red"), ""


# Check if able to login to jira with user id and password given
# @callback(
#     Output("id-valid-jira-cred-epic", "children"),
#     Output("id-2nd-level-epic", "className"),
#     Output("id-file-upload-epic", "disabled"),
#     Input("id-login-passwd-epic", "value"),
#     State("id-login-user-epic", "value"),
# )
# def check_jira_login(passwd, login):
#     click_event = clicked(dash.callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     my_logger = data.custom_logger.MyLogger().get_logger()
#     resp = api.get_myself(login, passwd)
#     my_logger.info(f"Response code: {resp}")
#     if resp.status_code == 200:
#         return html.I(className="fa fa-check fa-3x green"), "active", False
#     else:
#         return html.I(className="fa fa-times fa-3x red"), "", True


# Change long_callback to background callback
@callback(
    output=Output("id-progress-status", "children"),
    inputs=[
        Input("id-run-button-id", "n_clicks"),

    ],
    state=[
        # State("id-login-user-epic", "value"),
        # State("id-login-passwd-epic", "value"),
        State("id-store-epic-list", "data")
    ],
    running=[
        (Output("id-run-button-id", "disabled"), True, False),
        (Output("id_cancel_button_id", "disabled"), False, True),
    ],
    cancel=[Input("id_cancel_button_id", "n_clicks")],
    progress=Output("id-progress-bar-graph-epic", "figure"),
    progress_default=make_progress_graph(0, 10),
    interval=100,
    background=True,
    prevent_initial_call=True
)
def update_estimates(
        set_progress, n_clicks,
        # user, passwd,
        df_json
):
    my_logger = MyLogger().get_logger()
    df = pd.DataFrame()
    my_logger.debug(f"# of epics to be updated: {df.shape[0]}")

    if df_json is not None:
        df = pd.read_json(df_json, orient='split')
        total = df.shape[0]
        i = 0
        for row in (df['epic'].values.tolist()):
            try:
                ret_list = api.update_epic_estimates(row)
                set_progress(make_progress_graph((i := i + 1), total))
            except Exception as e:
                print(f'{row}: {e}')
                pass
        return [f"Records processed: {df.shape[0]}"]
    else:
        return [f"No files uploaded!!. Button clicked {n_clicks} times"]


# Check if login id is valid
# @callback(
#     Output("id-valid-login-name", "children"),
#     Output("id-1st-level", "className"),
#     Input("id-login-user", "value")
# )
# def check_input_login_name(login_id: str):
#     print(f"Login: {login_id}")
#     if login_id == "":
#         raise PreventUpdate
#     df = db.get_user_detail()
#     dff = df[df['emailAddress'] == login_id]
#     print(dff.shape[1])
#     if dff.shape[0] == 1:
#         return html.I(className="fa fa-check fa-3x green"), "active"
#     else:
#         return html.I(className="fa fa-times fa-3x red"), ""


# Check if able to login to jira with user id and password given
# @callback(
#     Output("id-valid-jira-cred", "children"),
#     Output("id-2nd-level", "className"),
#     Output("id-file-upload-transition", "disabled"),
#     Input("id-login-passwd", "value"),
#     State("id-login-user", "value"),
# )
# def check_jira_login(passwd, login):
#     click_event = clicked(dash.callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     my_logger = data.custom_logger.MyLogger().get_logger()
#     resp = api.get_myself(login, passwd)
#     my_logger.debug(f"response code = {resp}")
#     if resp.status_code == 200:
#         return html.I(className="fa fa-check fa-3x green"), "active", False
#     else:
#         return html.I(className="fa fa-times fa-3x red"), "", True


@callback(
    Output("id-file-output-transition", "children"),
    Output("id-store-issue-list", "data"),
    Output("id-3rd-level", "className"),
    Output("id-run-button-transition", "disabled"),
    Input("id-file-upload-transition", "contents"),
    Input("id-file-upload-transition", "filename"),
    # Input("id-login-user", "value"),
    # Input("id-login-passwd", "value"),
)
def get_issues_to_transition(
        contents, filename,
        # user, passwd
):
    if contents is None:
        raise PreventUpdate

    content_type, content_string = contents.split(',')
    decoded = base64.b64decode(content_string)

    df = None
    colnames = ['key']
    try:
        if 'txt' in filename:
            df = pd.read_csv(
                io.StringIO(decoded.decode('unicode_escape')), sep=" ", skiprows=1, names=colnames
            )
        elif 'csv' in filename:
            df = pd.read_csv(io.StringIO(decoded.decode('utf-8')), names=colnames, skiprows=1)
        elif 'xlsx' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='openpyxl', names=colnames)
        elif 'xls' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='xlrd', names=colnames)
        elif 'ods' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='odf', names=colnames)

        id_list = df['key'].values
        cols = 10 if len(id_list) > 10 else 1
        id_list_new = [id_list[i:i + cols] for i in range(0, len(id_list), cols)]
        dff = pd.DataFrame(id_list_new)

        for col in dff.columns:
            dff[col] = dff[col].apply(apply_href)
            dff[col] = dff[col].apply(apply_td)
        return html.Div(
            children=[
                html.Table(
                    [html.Tr(i) for i in dff.values], className='format-table'
                )
            ]
        ), df.to_json(orient='split'), "active", False
    except Exception as e:
        return html.Div(
            children=[
                html.Table(
                    children=[
                        html.Tr(children=[html.Th("Filename"), html.Th("Content Type"), html.Th("Error message")]),
                        html.Tr(children=[html.Td(filename), html.Td(content_type), html.Td(str(e))])
                    ], className="format-table"
                )
            ]
        ), None, "", True





# @callback(Output('output-data-upload', 'children'),
#               Input('id-file-upload-transition', 'contents'),
#               State('id-file-upload-transition', 'filename'),
#               State('id-file-upload-transition', 'last_modified'))
# def update_output(list_of_contents, list_of_names, list_of_dates):
#     if list_of_contents is not None:
#         children, df = parse_contents(list_of_contents, list_of_names, list_of_dates)
#         return children








# Change long_callback to background
@callback(
    output=Output("id-progress-status-transition", "children"),
    inputs=[
        Input("id-run-button-transition", "n_clicks"),
    ],
    state=[
        # State("id-login-user", "value"),
        # State("id-login-passwd", "value"),
        State("url", "pathname"),
        State("id-store-issue-list", "data")
    ],
    running=[
        (Output("id-run-button-transition", "disabled"), True, False),
        (Output("id_cancel_button-transition", "disabled"), False, True),
    ],
    cancel=[Input("id_cancel_button-transition", "n_clicks")],
    progress=Output("progress_bar_graph", "figure"),
    progress_default=make_progress_graph(0, 10),
    interval=100,
    background=True,
    prevent_initial_call=True
)
def transition_issue(
        set_progress, n_clicks,
        # user, passwd,
        pathname, df_json
):
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    if n_clicks is not None:
        df = pd.read_json(df_json, orient='split')
        total = df.shape[0]
        i = 0
        for row in (df['key'].values.tolist()):
            try:
                api.transition_issue(row, project)
                set_progress(make_progress_graph((i := i + 1), total))
            except Exception as e:
                print("Caught Exception!!!")
                print(f'{row}: {e}')
        return [f"Clicked {n_clicks} times. No of records to process: {df.shape[0]}"]


# Start Code block for Core Track
# @callback(
#     Output("id-file-ct-uploaded", "children"),
#     Output("id-ct-details", "data"),
#     Output("id-file-ct-uploaded", "className"),
#     Input("id-ct-file-upload", "contents"),
#     State("id-ct-file-upload", "filename"),
#     State("id-ct-file-upload", "last_modified"),
# )
# def get_ct_details(contents: str, filename: str, last_modified: str):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     child_element, df = parse_contents(contents, filename, last_modified)
#     return child_element, df, "fade-txt-message"


# Change long_callback to background
# @callback(
#     output=[
#         Output("id-coretrack-alert", "children"),
#         Output("id-download-ct", "data")
#     ],
#     inputs=[
#         Input("id-run-button-ct", "n_clicks"),
#     ],
#     state=[
#         State("id-ct-user", "value"),
#         State("id-ct-passwd", "value"),
#         State("url", "pathname"),
#         State("id-ct-details", "data"),
#         State("id-ct-env", "value"),
#         State("id-ct-options", "value")
#     ],
#     running=[
#         (Output("id-run-button-ct", "disabled"), True, False),
#         (Output("id-cancel-button-ct", "disabled"), False, True),
#         (
#                 Output("id-progress-bar-graph-ct", "style"),
#                 {"display": "inline"},
#                 {"display": "none"},
#         ),
#         (
#                 Output("id-file-ct-uploaded", "style"),
#                 {"display": "none"},
#                 {"display": "inline"},
#         )
#     ],
#     cancel=[Input("id-cancel-button-ct", "n_clicks")],
#     progress=Output("id-progress-bar-graph-ct", "figure"),
#     progress_default=make_progress_graph(0, 10),
#     interval=500,
#     background=True,
#     prevent_initial_call=True
# )
# def process_file(set_progress, n_clicks, user, passwd, pathname, df_json, env, operation):
#     if df_json is not None and user is not None and passwd is not None and n_clicks is not None:
#         if n_clicks > 0:
#             my_logger = MyLogger().get_logger()
#             my_logger.debug(f"Button clicked: {n_clicks} times")
#             df = pd.read_json(df_json, orient='split')
#
#             set_progress(make_progress_graph(0, df.shape[0]))
#             time_taken = None
#             with CoreTrackAutomation(user, passwd, prod=env == "prod", headless=True) as ct:
#                 ct.init()
#                 try:
#                     ct.login()
#                     my_logger.debug("Login success")
#                 except Exception as e:
#                     my_logger.exception(e)
#                     df = pd.DataFrame(None)
#                     return f"Unable to connect to {ct.url}: {e}", df.to_dict(orient='split')
#                 for record_count, row in enumerate(df.itertuples(), 1):
#                     if operation == "Create":
#                         ct_no = create_ct_single(user, passwd, row, env)
#                         df.at[getattr(row, "Index"), 'CoreTrack Id'] = ct_no
#                         set_progress(make_progress_graph(record_count, df.shape[0]))
#                     elif operation == "Search":
#                         try:
#                             my_logger.debug("Performing Search operation")
#                             found = ct.search_description_ct("search.aspx", getattr(row, "TS_No"))
#                             my_logger.debug(f'Found = {found}')
#                         except Exception as e:
#                             df = pd.DataFrame(None)
#                             return f"{e}: {ct.url}", df.to_dict(orient='split')
#                         df.at[getattr(row, "Index"), 'Found'] = found
#                         set_progress(make_progress_graph(record_count, df.shape[0]))
#             time_taken = ct.time_taken
#             print(time_taken)
#             df['Found'] = df['Found'].map({True: 'Found', False: 'Not Found'})
#             return html.Span(
#                 "Success. Press Download button to download the o/p file",
#                 className="fade-txt-message"), df.to_json(orient='split')


# @callback(
#     Output("id-download-ct-df-xlsx", "data"),
#     Input("id-download-ct", "n_clicks"),
#     State("id-download-ct", "data"),
#     State("id-ct-file-upload", "filename"),
#     prevent_initial_call=True
# )
# def download_ct(n_click, df_json, filename):
#     df = pd.read_json(df_json, orient='split')
#     return dcc.send_data_frame(df.to_excel, filename, sheet_name="Sheet1", index=False)


# End Code block for Core track

# Start Code for clock
# @callback(
#     Output({'type': 'date-details', 'index': MATCH}, "children"),
#     Output({'type': 'day-details', 'index': MATCH}, "children"),
#     Output({'type': 'hour', 'index': MATCH}, "style"),
#     Output({'type': 'minute', 'index': MATCH}, "style"),
#     Output({'type': 'second', 'index': MATCH}, "style"),
#     Output({'type': 'time', 'index': MATCH}, "children"),
#     Output({'type': 'country', 'index': MATCH}, "children"),
#     Input({'type': 'interval', 'index': MATCH}, "n_intervals"),
#     Input({'type': 'interval', 'index': MATCH}, "id")
# )
# def update_clock_layout(n, id):
#     zonename_dict = {
#         '0_0': 'America/New_York', '0_1': 'America/North_Dakota/Center', '0_2': 'America/Los_Angeles',
#         # '0_3': 'America/Bogota',
#         '0_3': 'America/Phoenix',
#         '1_0': 'Asia/Kolkata', '1_1': 'Asia/Dubai', '1_2': 'Europe/Bucharest', '1_3': 'Atlantic/Reykjavik'
#     }
#
#     # zonename_dict = {
#     #     (0, 0): 'America/New_York',
#     #     (0, 1): 'America/North_Dakota/Center',
#     #     (0, 2): 'America/Los_Angeles',
#     #     (0, 3): 'America/Bogota',
#     #     (1, 0): 'Asia/Kolkata',
#     #     (1, 1): 'Asia/Dubai',
#     #     (1, 2): 'Europe/Bucharest',
#     #     (1, 3): 'Atlantic/Reykjavik'
#     # }
#
#     # Asia/Dubai
#     # Asia/Kolkata
#     # Europe/Bucharest (Romania)
#     # Colombia has same timezone as Cuba
#     # current_time = datetime.now().astimezone()
#     if os.name == "nt":
#         epoch = helper.getNTPTime()
#         current_time = datetime.fromtimestamp(epoch).astimezone()
#     else:
#         epoch = helper.getNTPTime('time1.google.com')
#         current_time = datetime.fromtimestamp(epoch).astimezone()
#         # current_time = datetime.now().astimezone()
#     local_time = current_time.astimezone(pytz.timezone(zonename_dict[
#                                                            id['index']
#                                                        ]))
#     h = local_time.hour
#     m = local_time.minute
#     s = local_time.second
#
#     hDeg = h * 30 + m * (360 / 720)
#     mDeg = m * 6 + s * (360 / 3600)
#     sDeg = s * 6
#
#     tz_name = zonename_dict[id['index']]
#     country_name = [country['name'] for country in countries if tz_name in country['timezones']]
#
#     return local_time.strftime("%a, %b %d"), local_time.strftime('%Z'), {"transform": f"rotate({hDeg}deg)"}, {
#         "transform": f"rotate({mDeg}deg)"}, {"transform": f"rotate({sDeg}deg)"}, local_time.strftime(
#         "%I:%M %p"), country_name


# @callback(
#     Output("id-hours", "style"),
#     Output("id-minutes", "style"),
#     Output("id-seconds", "style"),
#     # Output("id-dateime-data", "data"),
#     Input("interval-component", "n_intervals"),
#     # State("id-dateime-data", "data"),
# )
# def update_clock(n):
#     # curr_time = datetime.now()
#     curr_time = datetime.fromtimestamp(helper.getNTPTime()).astimezone()
#     print(f'Time: {curr_time}')
#
#     sec_angle = curr_time.second * 6
#
#     min_angle = curr_time.minute * 6
#
#     hour_angle = (curr_time.hour * 30) + (curr_time.minute / 2)
#
#     return {"transform": f"rotateZ({hour_angle}deg)", 'webkitTransform': f'rotateZ({hour_angle}deg)'}, {
#         "transform": f"rotateZ({min_angle}deg)"}, {"transform": f"rotateZ({sec_angle}deg)"}


# End Code for clock

# @callback(
#     Output("id-update-details", "children"),
#     Input("id-update-init-attribs", "n_clicks"),
#     Input("url", "pathname"),
#     State("id-data-table-init-attrib", "data")
# )
# def update_init_attribs(n_click, pathname, data):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#     df = pd.DataFrame(data)
#     df.drop(columns=['summary', 'status'], inplace=True)
#     db_session = db.start_session(project)
#     db.upsert(db_session, db.InitiativeAttribute, df, 'initiative_id')
#     db_session.commit()
#     return html.Div("Update done!!!")


# @callback(
#     Output("id-teams-table", "data"),
#     Output("id-teams-table", "columns"),
#     Output("id-team-admin", "value"),
#     Output("id-individual-admin", "value"),
#     # Output("id-teams-table", "dropdown"),
#     Input("id-team-admin", "value"),
#     Input("id-individual-admin", "value"),
#     Input("id-add-teams", "n_clicks"),
#     Input("url", "pathname"),
#     State("id-teams-table", "data"),
#     State("id-teams-table", "columns")
# )
# def populate_team_details(team_name, individual_name, add_click, pathname, rows, columns):
#     click_event = clicked(callback_context)
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#
#     if click_event == "id-add-teams":
#         if individual_name == "":
#             print("Need individual name to proceed")
#         else:
#             dff = db.get_user_detail()
#             dff = dff[dff["accountId"] == individual_name]
#             dff['id'] = str(db.get_snowflake_id())
#
#             # rows.append({c['id']: dff[c['name']] if c['name'] in ["accountId", "displayName", "emailAddress", "Status"] else '' for c in columns})
#             rows.append(
#                 {c['id']: dff.to_dict(orient="list")[c['name']][0] if c['name'] in ["id", "accountId", "displayName",
#                                                                                     "emailAddress", "Status"] else ''
#                  for c in columns})
#             return rows, columns, "", individual_name
#
#     if click_event == "id-individual-admin":
#         df = db.get_teams_details(project, None, individual_name)
#         ret_value = ["", individual_name]
#     else:
#         df = db.get_teams_details(project, team_name, None)
#         ret_value = [team_name, ""]
#
#     df['id'] = df['id'].map(str)
#     df['active'] = df['active'].map({True: 'Active', False: 'Inactive'})
#
#     return df.to_dict('records'), [
#         {"name": "id", "id": "id", "editable": False, "type": "text"},
#         {"name": "team_name", "id": "team_name", "editable": True, "type": "text"},
#         {"name": "accountId", "id": "accountId", "editable": False, "type": "text"},
#         {"name": "startDate", "id": "startDate", "editable": True, "type": "datetime"},
#         {"name": "endDate", "id": "endDate", "editable": True, "type": "datetime"},
#         {"name": "active", "id": "active", "editable": True, "presentation": "dropdown", "type": "text"},
#         {"name": "role", "id": "designation", "editable": True, "type": "text"},
#         {"name": "Account Type", "id": "Account Type", "editable": False, "type": "text"},
#         {"name": "Status", "id": "Status", "editable": True, },
#         {"name": "displayName", "id": "displayName", "editable": False, "type": "text"},
#         {"name": "emailAddress", "id": "emailAddress", "editable": False, "type": "text"},
#
#         # {"name": i, "id": i, "editable": j, "type": k} for i, j, k in zip(
#         #     df.columns.to_numpy(),
#         #     [False, True, False, True, True, True, True, False, False, False, False],
#         #     ['text', 'text', 'text', 'datetime', 'datetime', 'text', 'text', 'text', 'text', 'text', 'text']
#         # )
#     ], *ret_value


# @callback(
#     Output("id-update-team-alert", "children"),
#     Input("id-update-teams", "n_clicks"),
#     State("url", "pathname"),
#     State("id-teams-table", "data")
# )
# def update_teams_table(n, pathname, data):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
#
#     df = pd.DataFrame(data)
#     df.drop(columns=['Account Type', 'Status', 'displayName', 'emailAddress'], inplace=True)
#     print(df.dtypes)
#     df[['startDate', "endDate"]] = df[['startDate', "endDate"]].apply(pd.to_datetime)
#     df['startDate'] = df['startDate'].fillna(pd.to_datetime('1970-01-01'))
#     df['endDate'] = df['endDate'].fillna(pd.to_datetime('2050-12-31'))
#
#     df['active'] = df['active'].map({'Active': True, 'Inactive': False})
#     df['active'] = df['active'].astype('bool')
#
#     with db.start_session(project) as db_session:
#         db.upsert(db_session, db.Teams, df, 'accountId')
#         db_session.commit()
#
#     db_session.close()
#
#     return html.Div(children=[
#         html.P("Update done!!!"),
#         html.I(id="icon", **{"data-feather": "shopping-bag"}),
#     ], id="content"
#     )


# Update SVN repo
# @callback(
#     output=Output("id-svn-alert", "children"),
#     inputs=Input("id-refresh-svn", "n_clicks"),
#     background=True,
#     running=[
#         (Output("id-refresh-svn", "disabled"), True, False),
#         (Output("id-cancel-refresh-svn", "disabled"), False, True),
#     ],
#     cancel=Input("id-cancel-refresh-svn", "n_clicks"),
#     config_prevent_initial_callbacks=True
# )
# def update_directory(n_clicks):
#     if n_clicks is None:
#         raise PreventUpdate
#
#     home_path = os.getenv('HOME', 'E:/KeyPass')
#     ref = pkp(
#         filename=os.path.join(home_path, 'Database.kdbx'),
#         keyfile=os.path.join(home_path, 'Database.key')
#     )
#
#     entry = ref.find_entries(title='SVN', first=True)
#     user = entry.username
#     pwd = entry.password
#
#     my_logger = MyLogger().get_logger()
#     if platform.uname()[1] == "DESKTOP-RBFFEPG":
#         path = os.path.join("e:", "\\", "SVN Checkout")
#         os.chdir(path)
#         print(os.getcwd())
#         command = f'svn update'
#     elif platform.uname()[1] == "dashboard":
#         command = f"cd /opt/svn/Plat/; svn update --no-auth-cache --non-interactive --username {user} --password {pwd}"
#     else:
#         command = None
#
#     ret = subprocess.run(command, capture_output=False, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
#     my_logger.info(f"{ret}")
#     my_logger.info(f"{type(ret)}:{ret.stdout.decode()}")
#     print(ret.stdout.decode())
#     return html.Div(children=[ret.stdout.decode("utf-8")])


# @callback(
#     Output("id-svn-df", "data"),
#     Input("id-svn-file-upload", "contents"),
#     State("id-svn-file-upload", "filename"),
#     State("id-svn-file-upload", "last_modified")
# )
# def get_file_details(contents: str, filename, last_modified):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#
#     _, df = parse_contents(contents, filename, last_modified)
#     return df


# @callback(
#     output=Output("id-download-svn-data", "data"),
#     inputs=Input("id-get-matching-ids", "n_clicks"),
#     state=[
#         State("id-svn-df", "data"),
#     ],
#     background=True,
#     running=[
#         (Output("id-get-matching-ids", "disabled"), True, False),
#         (Output("id-cancel-matching-ids", "disabled"), False, True),
#     ],
#     cancel=Input("id-cancel-matching-ids", "n_clicks"),
#     prevent_initial_call=True
# )
# def populate_svn_match(n_clicks, df_json):
#     my_logger = MyLogger().get_logger()
#     import re
#
#     home_path = os.getenv('HOME', 'E:/KeyPass')
#     ref = pkp(
#         filename=os.path.join(home_path, 'Database.kdbx'),
#         keyfile=os.path.join(home_path, 'Database.key')
#     )
#
#     entry = ref.find_entries(title='SVN', first=True)
#     user = entry.username
#     pwd = entry.password
#
#     if n_clicks is None:
#         raise PreventUpdate
#
#     df = pd.read_json(df_json, orient='split')
#     if platform.uname()[1] == "DESKTOP-RBFFEPG":
#         path = "e:/SVN Checkout"
#     elif platform.uname()[1] == "dashboard":
#         path = "/opt/svn/Plat/"
#     else:
#         path = None
#
#     files = [f for f in os.listdir(path) if os.path.isfile(os.path.join(path, f))]
#     df['TCID'] = df['TCID'].map(str)
#     pattern = df['TCID'].tolist()
#
#     x = ((any(re.match(d, b) for b in files) for d in pattern))
#     result = [list((re.match(d, b)) for b in files) for d in pattern]
#
#     from re import match
#     results = []
#     for d in pattern:
#         filtered_values = list(filter(lambda v: match(d, v), files))
#         if filtered_values:
#             cmd = "awk {print $2}"
#             if platform.uname()[1] == "DESKTOP-RBFFEPG":
#                 path = os.path.join("e:", "\\", "SVN Checkout")
#                 command = f'cd {path}; svn log --incremental  --limit 1 -r HEAD:1 "{"".join(filtered_values)}"'
#             elif platform.uname()[1] == "dashboard":
#                 command = f"cd /opt/svn/Plat/; svn log --incremental  --limit 1 -r HEAD:1 --no-auth-cache --non-interactive --username {user} --password {pwd} '{''.join(filtered_values)}' |head -2|tail -1|awk '{{print $3}}'"
#                 my_logger.info(f'{command}')
#             else:
#                 command = None
#             ret = subprocess.run(command, capture_output=False, shell=True, stdout=subprocess.PIPE,
#                                  stderr=subprocess.STDOUT)
#             my_logger.info(f'{ret.stdout.decode()}')
#             results.append(
#                 [d, ''.join(filtered_values), os.path.splitext(''.join(filtered_values))[1], ret.stdout.decode()])
#
#     # for d in pattern:
#     #     for b in files:
#     #         y = re.match(str(d), b)
#     #         if y:
#     #             results.append([y.group(), b, os.path.splitext(b)[1]])
#     #             print(f'{y.group()}, {b}, {os.path.splitext(b)[1]}')
#
#     df_out = pd.DataFrame(results)
#     return df_out.to_json(orient='records')


# @callback(
#     Output("id-download-svn-xlsx", "data"),
#     Input("id-download-svn", "n_clicks"),
#     State("id-download-svn-data", "data"),
# )
# def download_svn_file(n_clicks, df_json):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#
#     df = pd.read_json(df_json, orient='records')
#
#     df.columns = ['TCID', 'Filename', 'Extension', 'Author']
#
#
#     return dcc.send_data_frame(df.to_excel, 'outfile.xlsx', sheet_name="Sheet1", index=False)


@callback(
    Output("id-rn-cookie-details", "className"),
    Output("id-rn-jazz-details", "className"),
    Input("id-rn-project", "value")
)
def show_project_panel(value):
    cookie_class = jazz_class = "rn-details details-hide"
    if 'Cookie' in value:
        cookie_class = "rn-details"
    if 'Jazz' in value:
        jazz_class = "rn-details"

    return cookie_class, jazz_class


@callback(
    Output("id-to-dash_app-version", "options"),
    Input("id-from-dash_app-version", "value"),
    Input("url", "pathname")
)
@inject
def populate_to_version(
        from_version: str, pathname: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if from_version is None:
        raise PreventUpdate
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        df = db.get_version_after(from_version, pg_session)
    options = [dict(label=name, value=name) for name, _ in df[['name', 'id']].values]
    return options


@callback(
    Output("id-label-textarea", "value"),
    Input("id-from-dash_app-version", "value"),
    Input("id-to-dash_app-version", "value"),
    Input("url", "pathname")
)
@inject
def populate_text_area(
        from_version, to_version, pathname,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> str:
    click_event = clicked(callback_context)
    if click_event is None or from_version is None or to_version is None:
        raise PreventUpdate
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        df = db.get_version_range(from_version, to_version, pg_session)
    ret_value = ", ".join(sorted(df['name'].values, key=lambda x: list(map(int, x.split('.')))))

    return ret_value


# @callback(
#     Output("id-pf-version-output", "value"),
#     Input("id-rn-pf-version", "value")
# )
# def update_cookie_pf_version(pf_version: str):
#     click_event = clicked(dash.callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     dash.callback_context.response.set_cookie('pf_cookie', pf_version, expires=1)
#     print(flask.request.cookies)
#
#     return flask.request.cookies


@callback(
    Output("id-download-rn", "disabled"),
    Input("id-label-textarea", "n_clicks"),
    Input("id-label-textarea", "value")
)
def enable_rn_button(n_clicks, value):
    click_event = clicked(callback_context)
    if click_event is None or value is None:
        raise PreventUpdate
    return False


@callback(
    Output("id-download-rn-xlsx", "data"),
    Input("id-download-rn", "n_clicks"),
    Input("url", "pathname"),
    State("id-from-dash_app-version", "value"),
    State("id-label-textarea", "value"),
    State("id-rn-project", "value"),
    State("id-rn-pf-version", "value"),
    State("id-rn-kms-version", "value"),
    State("id-rn-cookie-rpt-version", "value"),
    State("id-rn-cookie-kafka-version", "value"),
    State("id-rn-cookie-rpt-delivery-version", "value"),
    State("id-rn-jazz-rpt-version", "value"),
    State("id-rn-jazz-kafka-version", "value"),
    State("id-rn-jazz-rpt-delivery-version", "value"),
    State("id-gen-pf-rn", "value"),
    State("id-gen-kms-rn", "value"),
    State("id-svn-branch", "value"),
    State("id-svn-package", "value"),
)
@inject
def generate_rn(
        n_clicks: int, pathname: str,
        from_version: str, version_list: str, rn_project: list, pf_version: list, kms_version: str,
        cookie_report_version: str, cookie_karfa_verion: str, cookie_report_deliver_version: str,
        jazz_report_version: str, jazz_karfa_verion: str, jazz_report_deliver_version: str,
        pf_rn_flag: list, kms_rn_flag: list, branch_name: list, package_name: list,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    print(f'click_event in generate_rn = {callback_context}')

    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    # Method to get all cookies that are set
    # all_cookies = dict(flask.request.cookies)
    all_values = locals()
    for k, v in all_values.items():
        print(f'{k}: {v} : {type(v)}')

    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    # Define xlswriter object
    output = io.BytesIO()
    xslx_writer = pd.ExcelWriter(output, engine="xlsxwriter")
    workbook = xslx_writer.book
    cover_page_list = []
    counter = 0
    for prj in rn_project:
        cover_page_list.append(
            [counter := counter + 1, prj + "Application", from_version, version_list.rsplit(',', 1)[-1]])

    pf_version = sorted(pf_version, key=lambda x: list(map(int, x.split('.'))))

    cover_page_list.append(
        [
            counter := counter + 1, 'Platform Version',
            pf_version[-1] if pf_version else '', pf_version[0] if pf_version else None
        ]
    )
    cover_page_list.append(
        [
            counter := counter + 1, 'KMS Version',
            kms_version.split(',')[0] if kms_version else None, kms_version.rsplit(',', 1)[-1] if kms_version else None
        ]
    )

    # x = version.parse(pf_version.split('-')[0])
    # y = version.parse(pf_version.rsplit('-', 1)[-1])

    if 'Cookie' in rn_project:
        cover_page_list.append([counter := counter + 1, 'Cookie Reporting', cookie_report_version.split('-')[0],
                                cookie_report_version.rsplit('-', 1)[-1]])
        cover_page_list.append([counter := counter + 1, 'Cookie PDF Kafka', cookie_karfa_verion.split('-')[0],
                                cookie_karfa_verion.rsplit('-', 1)[-1]])
        cover_page_list.append(
            [counter := counter + 1, 'Cookie Report Delivery', cookie_report_deliver_version.split('-')[0],
             cookie_report_deliver_version.rsplit('-', 1)[-1]])

    if 'Jazz' in rn_project:
        cover_page_list.append([counter := counter + 1, 'Jazz Reporting', jazz_report_version.split('-')[0],
                                jazz_report_version.rsplit('-', 1)[-1]])
        cover_page_list.append([counter := counter + 1, 'Jazz PDF Kafka', jazz_karfa_verion.split('-')[0],
                                jazz_karfa_verion.rsplit('-', 1)[-1]])
        cover_page_list.append(
            [counter := counter + 1, 'Jazz Report Delivery', jazz_report_deliver_version.split('-')[0],
             jazz_report_deliver_version.rsplit('-', 1)[-1]])

    # dff = pd.DataFrame([[1, 'Jazz', value.split(',')[0], value.rsplit(',', 1)[-1]]],
    #                    columns=["#", "Module", "From", "To"])
    dff = pd.DataFrame(cover_page_list, columns=["#", "Module", "From", "To"])
    dff.to_excel(xslx_writer, sheet_name="Title", startrow=1, header=False, index=False)
    # Format title page
    worksheet = xslx_writer.sheets['Title']
    (max_row, max_col) = dff.shape

    # Create a list of column headers, to use in add_table().
    column_settings = [{'header': column} for column in dff.columns]

    # Add the Excel table structure. Pandas will add the data.
    worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings})
    # Make the columns wider for clarity.
    # worksheet.set_column(0, max_col - 1, 12)
    wrap_format = workbook.add_format({'text_wrap': True})
    format_sheet(dff, worksheet, wrap_format)

    versions = [x.strip() for x in version_list.split(",")]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': project})
    with session_factory.db_conn_provider().session() as pg_session:
        df = db.get_epic_details_in_fixversion(versions, project, pg_session)
        for key in ['app_sql', 'app_index', 'app_schema', 'rd_index']:
            df_sql = db.get_sql_object(branch_name, key, project, pg_session)
            df = pd.merge(df, df_sql, how="left", left_on='Internal JIRA#', right_on='epic_key')
            df.drop(columns=['epic_key'], inplace=True, errors='ignore')
        (max_row, max_col) = df.shape

        # Formatting Feature details
        df.to_excel(xslx_writer, index=False, sheet_name="Feature  Details")
        worksheet = xslx_writer.sheets['Feature  Details']

        column_settings = [{'header': column} for column in df.columns]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
        format_sheet(df, worksheet, wrap_format)

        df = db.get_issue_epic_mismatch_in_fixversion(versions, project, pg_session)
        df.to_excel(xslx_writer, index=False, sheet_name="Epic Missing Version")
        worksheet = xslx_writer.sheets['Epic Missing Version']
        (max_row, max_col) = df.shape
        column_settings = [{'header': column} for column in df.columns]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
        wrap_format = workbook.add_format({'text_wrap': True})
        format_sheet(df, worksheet, wrap_format)

        # Add bug details
        df = db.get_bug_details_in_fixversion(versions, project, pg_session)
        for key in ['app_sql', 'app_index', 'app_schema', 'rd_index']:
            df_sql = db.get_sql_object_bugs(branch_name, key, project)
            df = pd.merge(df, df_sql, how="left", left_on='Internal JIRA#', right_on='cc_jira')
            df.drop(columns=['cc_jira'], inplace=True, errors='ignore')

        df.to_excel(xslx_writer, index=False, sheet_name="Fixed Defect Details", startrow=1, header=False)
        worksheet = xslx_writer.sheets['Fixed Defect Details']
        (max_row, max_col) = df.shape
        column_settings = [{'header': column} for column in df.columns]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
        wrap_format = workbook.add_format({'text_wrap': True})
        format_sheet(df, worksheet, wrap_format)

        # Add Summary sheet for package and branch
        df1, df2 = db.get_branch_package_summary(branch_name, package_name, project, pg_session)
        df1.to_excel(xslx_writer, index=False, sheet_name="Package Summary", startrow=1, header=False)
        worksheet = xslx_writer.sheets['Package Summary']
        (max_row, max_col) = df1.shape
        column_settings = [{'header': column} for column in df1.columns]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'style': 'Table Style Medium 4'}, )
        wrap_format = workbook.add_format({'text_wrap': True})
        format_sheet(df1, worksheet, wrap_format)

        df2.to_excel(xslx_writer, index=False, sheet_name="Branch Summary", startrow=1, header=False)
        worksheet = xslx_writer.sheets['Branch Summary']
        (max_row, max_col) = df2.shape
        column_settings = [{'header': column} for column in df2.columns]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
        wrap_format = workbook.add_format({'text_wrap': True})
        format_sheet(df2, worksheet, wrap_format)

        # Generate Platform RN

        if 'Yes' in pf_rn_flag:
            print(f'Platform version: {pf_version}')
            df = db.get_platform_description(pf_version, project, pg_session)

            # base_url = 'http://*************/'
            # word_list = []
            # for pf in pf_version[:-1]:
            #     my_logger.info(f"url = {base_url}{pf.strip()}.html")
            #     page = requests.get(f'{base_url}{pf.strip()}.html')
            #     my_logger.info(f'RN Status code: {page.status_code}')
            #     if page.status_code == 200:
            #         soup = BeautifulSoup(page.content, 'html.parser')
            #         links = soup.select('pre')
            #         parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
            #         parser.feed(str(links[0]))
            #         parser.all_data.remove('\n')
            #         x = filter(None, [re.sub(r"\d line[s]*", r"", i) for i in parser.all_data])
            #         x = filter(None, [re.sub(r"Merged revision.*", r"", i) for i in x])
            #
            #         for line in (list(x)):
            #             word_list.append([pf, line.split('|')[-1].translate({ord('\n'): None})])
            # my_logger.info(f'Platform Lenght of arrary: {len(word_list)}')
            # if len(word_list) > 0:
            #     df = pd.DataFrame(word_list, columns=['Platform Version', 'Summary'])

            df.to_excel(xslx_writer, index=False, sheet_name="Platform", startrow=1, header=False)
            worksheet = xslx_writer.sheets['Platform']
            (max_row, max_col) = df.shape
            column_settings = [{'header': column} for column in df.columns]
            worksheet.add_table(0, 0, max_row, max_col - 1,
                                {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
            wrap_format = workbook.add_format({'text_wrap': True})
            format_sheet(df, worksheet, wrap_format)

        # Generate KMS RN
        # my_logger.info("Generating KMS RN")
        word_list = []
        if 'Yes' in kms_rn_flag:
            page = requests.get(url="http://*************/cgi-bin/wiki.pl/KMS_Releases")
            soup = BeautifulSoup(page.content, 'html.parser')
            links_heading = soup.select('h2')
            links_ul = soup.select('ul')
            parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
            for kms in kms_version.split(','):
                for count, link in enumerate(links_heading):
                    if f'KMS {kms.strip()}' in link.text:
                        parser.feed(str(links_ul[count]))
                        for line in parser.all_data:
                            word_list.append([kms, line])
            if len(word_list) > 0:
                df = pd.DataFrame(word_list, columns=['KMS Version', 'Summary'])
                df.to_excel(xslx_writer, index=False, sheet_name="KMS", startrow=1, header=False)
                worksheet = xslx_writer.sheets['KMS']
                (max_row, max_col) = df.shape
                column_settings = [{'header': column} for column in df.columns]
                worksheet.add_table(0, 0, max_row, max_col - 1,
                                    {'columns': column_settings, 'style': 'Table Style Medium 2'}, )
                wrap_format = workbook.add_format({'text_wrap': True})
                format_sheet(df, worksheet, wrap_format)
        # my_logger.debug("Completed all generation")
        xslx_writer.close()
    return dcc.send_bytes(output.getvalue(), "release_notes.xlsx")


@callback(
    Output("id-svn-branch-dash", "value"),
    Output("id-svn-package-dash", "value"),
    Input("id-svn-branch-dash", "value"),
    Input("id-svn-package-dash", "value"),
)
def update_svn_branch_dcc(branch: str, package: str):
    """
    Callback function to clear selected value in branch drop dropdown
    Args:
        branch:
        package:
    """
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    if click_event == 'id-svn-branch-dash':
        return [branch, ""]
    else:
        return ["", package]


@callback(
    Output("id-svn-layer-2", "children"),
    Output("id-svn-show-counts", "children"),
    Input("id-svn-branch-dash", "value"),
    Input("id-svn-package-dash", "value"),
    Input("url", "pathname"),
    Input("id-svn-issuetype", "value"),
    Input("id-svn-fixversion", "value"),
)
@inject
def display_svn_table(
        svn_branch: str, svn_package: str, pathname: str,
        issuetype: str, fixversion: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        if svn_branch:
            df = db.get_cc_jira_details(svn_branch, pg_session, isbranch=True)
        elif svn_package:
            df = db.get_cc_jira_details(svn_package, pg_session, isbranch=False)
        else:
            df = pd.DataFrame([None])

    if issuetype == "epic":
        df.query("issuetype == 'Epic'", inplace=True)

        df.drop(columns=[
            'isSubTask', 'standard Key', 'standard issuetype',
            'standard status', 'standard fixVersions',
            'epic key', 'epic status', 'epic fixVersions'
        ], inplace=True)

    elif issuetype == 'bugs':
        df.query("issuetype.isin(['Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect'])", inplace=True)
        df.drop(columns=[
            'isSubTask', 'standard Key', 'standard issuetype',
            'standard status', 'standard fixVersions',
        ], inplace=True)
    elif issuetype == 'standard':
        df.query("(isSubTask == False) and issuetype!= ['Bug', 'In-sprint Defects', 'UAT Defect', 'Production "
                 "Defect', 'Epic', 'Observation']", inplace=True)
        df.drop(columns=[
            'isSubTask', 'standard Key', 'standard issuetype',
            'standard status', 'standard fixVersions',
        ], inplace=True)
    elif issuetype == 'subtask':
        df.query("isSubTask == True", inplace=True)
    elif issuetype == 'invalid':
        df.query("key == 'Not Found'", inplace=True)
        df.drop(columns=[
            'key', 'issuetype', 'status', 'fixVersions',
            'isSubTask', 'standard Key', 'standard issuetype',
            'standard status', 'standard fixVersions', 'project', 'release', 'feature',
            'epic key', 'epic status', 'epic fixVersions'
        ], inplace=True)

    if fixversion == 'missing':
        for col in ['fixVersions', 'epic fixVersions', 'standard fixVersion']:
            if col in df.columns:
                df = df[df[col].apply(lambda x: len(x) == 0)]

    df['svn details'] = df['svn details'].apply(apply_table)

    for col in ['cc_jira', 'key', 'epic key', 'standard Key']:
        if col in df.columns:
            df[col] = df[col].apply(apply_href)
    for col in df.columns:
        if col in df.columns:
            df[col] = df[col].apply(apply_td)

    df.rename(columns={'cc_jira': 'SVN check-in JIRA#'}, inplace=True)

    # return html.Table(
    #         [html.Tr([html.Th(col) for col in df.columns])] +
    #         [html.Tr(i) for i in df.values],
    #         className='format-table'
    #     ), html.Div(df.shape[0])
    return html.Table(
        children=[
            html.Thead([html.Tr([html.Th(col) for col in df.columns])]),
            html.Tbody([html.Tr(i) for i in df.values])
        ], className='format-table'
    ), html.Div(df.shape[0])


# Callback function to login the user, or update the screen if the username or password are incorrect


@callback(
    Output("id-instructions", "children"),
    Input("url", "pathname")
)
def update_instructions(pathname):
    user_type = session.get('new_registration', 'new')
    if user_type == 'new':
        return html.Div(
            children=[
                html.Div(children=[
                    html.H4("Instructions", className="text-center"),
                    html.Ol(
                        children=[
                            html.Li(
                                children=[
                                    html.Label("Download "),
                                    html.A(
                                        "Google Authenticator",
                                        href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en&gl=US",
                                        target="_blank",
                                    ),
                                    html.Label(" App on your mobile."),
                                ]
                            ),
                            html.Li("Check your inbox with mail subject 'New Registration Code'"),
                            html.Li("Scan the QR Code in the attachment"),
                            html.Li("Submitted the generated key in the form"),
                        ]
                    )
                ], className="add-border-black"),

            ], className="center-div-tag"
        )
    else:
        return html.Div()


@callback(
    Output("id-input-token", "value"),
    Input("id-input-token", "n_blur"),
    State("id-input-token", "value")
)
def check_pin_value(n_blur: int, value: str):
    if n_blur is None:
        raise PreventUpdate

    return ''.join(filter(str.isdigit, value))[:6]


# Define a list of allowed domains
ALLOWED_DOMAINS = ["graph.microsoft.com"]


# Function to check if a URL is allowed
def is_allowed_domain(url):
    from urllib.parse import urlparse
    domain = urlparse(url).netloc
    return any(allowed_domain in domain for allowed_domain in ALLOWED_DOMAINS)


def send_messages(prompt):
    """OpenAI API call. You may choose parameters here but `stream=True` is required."""
    return openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=prompt,
        stream=True,
        max_tokens=2024,
        temperature=0.5,
    )


@server.route("/streaming-chat", methods=["POST"])
def streaming_chat():
    user_prompt = request.json["prompt"]

    # prompt engineering/data augmentation can be performed here
    # important thing is that this is happening on the backend, so that the users can't tamper with this
    # JS front-end only handles the response, and nothing else
    chat_completion_prompt = [
        {
            "role": "system",
            "content": "Answer the following question as a pirate. Respond in markdown format. Question:\n",
        },
        {"role": "user", "content": user_prompt},
    ]

    def response_stream():
        # yield from (
        #     line.choices[0].delta.get("content", "")
        #     for line in send_messages(chat_completion_prompt)
        # )
        return "this is test message"

    return Response(response_stream(), mimetype="text/response-stream")


# @callback(
#     Output('user-status-div', 'children'),
#     Output('login-status', 'data'),
#     [Input('url', 'pathname')]
# )
# def login_status(url):
#     """ callback to display login/logout link in the header """
#     if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated \
#             and url != '/logout':  # If the URL is /logout, then the user is about to be logged out anyways
#         return dcc.Link('logout', href='/logout'), current_user.get_id()
#     else:
#         return dcc.Link('login', href='/login'), 'loggedout'

# @callback(
#     Output("id-logout-menu", "value"),
#     Input("id-logout-menu", "n_clicks")
# )
# def logout_menu_handler(n_clicks: int):
#     session['logout_reason'] = 'user_logout'
#     return n_clicks


#
# @callback(
#     Output("id-capture-mermaid-click", "children"),
#     Input("id-cc-system-context", "config"),
#     Input("id-cc-system-context", "n_clicks")
# )
# def add_click_evern(chart, n_click):
#     print(f"Chart called: {n_click}")
#     click_event = clicked(dash.callback_context)
#     print(click_event)
#     if click_event is None:
#         raise PreventUpdate
#
#     click_js = """
#         click: function (node, chart, index) {
#             window.open(node.url);
#         }
#         """
#     chart["config"]["click"] = click_js
#     print(chart)
#     return html.Div(chart)

logger = logging.getLogger(__name__)


class DashLogger(logging.StreamHandler):
    def __init__(self, stream=None):
        super().__init__(stream=stream)
        self.logs = list()
        print(f"DashLogger constructor called!!!")

    def emit(self, record):
        try:
            print(f'emit function called with {record}')
            msg = self.format(record)
            self.logs.append(msg)
            self.logs = self.logs[-1000:]
            self.flush()
        except Exception:
            self.handleError(record)


dash_logger = data.custom_logger.DashLogger(stream=sys.stdout)
logger.addHandler(dash_logger)


@callback(
    Output("id-status-report-div", "children"),
    Input("add-filter", "n_clicks"),
    Input("remove-filter", "n_clicks"),
    Input("url", "pathname"),
    State("id-status-report-div", "children"),
)
@inject
def add_release_version(
        add, remove, pathname: str, children,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    click_event = clicked(callback_context)
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    # if isinstance(session_factory, containers.DynamicContainer):
    #     my_logger.debug("session_factory is dynamic container")
    #     pg_session_generator = session_factory.db_ro().session()
    # else:
    #     my_logger.debug("Session not inserted dynamically. Constructing it manually")
    #     pg_session_generator = db.Database(db.DbConnectionURI(kpk_inst, read_only=True)).session()
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})

    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        # updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
        # pg_session.bind = updated_engine
        result = db.get_all_active_versions(pg_session)
        branch_list = db.get_branch_details(pg_session)
        logger.info(f"versions returned {len(result)}")
    options = [dict(label=name, value=name) for name in result]
    branch_options = [dict(label=item, value=item) for item in branch_list]

    new_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-drop-down', 'index': str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '300px'}
    )
    branch_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-branch-drop-down', 'index': str(add - remove)},
        options=branch_options, className="dropdown_custom", multi=True, style={'width': '150px'}
    )

    new_pattern_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-drop-down-pattern', 'index': str(add - remove)},
        style={'width': '200px'},
    )
    new_header_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-header', 'index': str(add - remove)}, style={'width': '250px'}
    )
    new_search_button = html.Button(id={'type': 'status-rpt-search', 'index': str(add - remove)},
                                    className="format-serach-button")
    logger.debug("Created required elements for Cookie")
    if click_event is None or click_event == 'add-filter':
        children.append(
            html.Div(children=[
                html.Div(children=[new_pattern_text_box, new_search_button], className="col"),
                html.Div(children=[new_dropdown], className="col"),
                html.Div(children=branch_dropdown, className="col"),
                html.Div(children=new_header_text_box, className="col")
            ], className="row ms-1 mb-1")
        )
    elif click_event == 'remove-filter':
        if len(children) > 1:
            children.pop()

    return children


# Added for Jazz
@callback(
    Output("id-jazz-status-report-div", "children"),
    Input("jazz-add-filter", "n_clicks"),
    Input("jazz-remove-filter", "n_clicks"),
    Input("url", "pathname"),
    State("id-jazz-status-report-div", "children"),
)
@inject
def add_release_version(
        add, remove, pathname: str, children,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory]
):
    click_event = clicked(callback_context)
    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': schema_name})
    with session_factory.db_conn_provider().session() as pg_session:
        # with session_factory.db_connections()['ro']()()[schema_name].session() as pg_session:
        result = db.get_all_active_versions(pg_session)
    options = [dict(label=name, value=name) for name in result]
    new_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-drop-down', 'index': 'jazz' + str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '300px'}
    )

    branch_dropdown = dcc.Dropdown(
        id={'type': 'status-rpt-branch-drop-down', 'index': 'jazz' + str(add - remove)},
        options=options, className="dropdown_custom", multi=True, style={'width': '150px'}
    )

    new_pattern_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-drop-down-pattern', 'index': 'jazz' + str(add - remove)},
        style={'width': '200px'},
    )
    new_header_text_box = dcc.Input(
        type="text",
        id={'type': 'status-rpt-header', 'index': 'jazz' + str(add - remove)}, style={'width': '250px'}
    )
    new_search_button = html.Button(id={'type': 'status-rpt-search', 'index': 'jazz' + str(add - remove)},
                                    className="format-serach-button")

    if click_event is None or click_event == 'jazz-add-filter':
        children.append(
            html.Div(children=[
                html.Div(children=[new_pattern_text_box, new_search_button], className="col"),
                html.Div(children=[new_dropdown], className="col"),
                html.Div(children=[branch_dropdown], className="col"),
                html.Div(children=new_header_text_box, className="col")
            ], className="row ms-1 mb-1")
        )
    elif click_event == 'jazz-remove-filter':
        if len(children) > 1:
            children.pop()

    return children


# Risk Register
@callback(
    Output("id-plat-risk-register", "children", allow_duplicate=True),
    Input("id-add-filter-risk", "n_clicks"),
    Input("url", "pathname"),
    State("id-plat-risk-register", "children"),
    prevent_initial_call='initial_duplicate',
)
def create_risk_register(add, pathname: str, children):
    click_event = clicked(callback_context)

    schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    if click_event is None or click_event == 'id-add-filter-risk':
        checkbox = dcc.Checklist(
            id={'type': 'risk-chk-box', 'index': len(children) if children else 0},
            options=[{"label": len(children) if children else 0, "value": len(children) if children else 0}],
            inline=True,
            labelStyle={"display": "flex", "align-items": "center", 'width': '10px'},
        )

        # id_label = html.Label(
        #     len(children) + 1 if children else 1,
        #     id={'type': 'risk-label', 'index': len(children) if children else 0},
        # )
        date_picker = dcc.DatePickerSingle(
            id={'type': 'risk-date-picker', 'index': len(children) if children else 0},
            initial_visible_month=date.today(),
            min_date_allowed=date.today() + relativedelta(months=-3),
            max_date_allowed=date.today() + relativedelta(days=1),
            date=date.today()
        )
        description = dcc.Textarea(
            id={'type': 'risk-description-textarea', 'index': len(children) if children else 0},

        )

        probability_scale = dcc.Dropdown(
            options=[
                {'label': 'Unlikely', 'value': 1},
                {'label': 'Low', 'value': 2},
                {'label': '50-50', 'value': 3},
                {'label': 'High', 'value': 4},
                {'label': 'Definite', 'value': 5},
            ],
            id={'type': 'risk-probability', 'index': len(children) if children else 0},
            className="dropdown_custom",
        )

        impact_scale = dcc.Dropdown(
            options=[
                {'label': 'Insignificant', 'value': 1},
                {'label': 'Low', 'value': 2},
                {'label': 'Medium', 'value': 3},
                {'label': 'Critical', 'value': 4},
                {'label': 'Catastrophic', 'value': 5},
            ],
            id={'type': 'risk-impact', 'index': len(children) if children else 0},
            className="dropdown_custom",
        )

        mitigation_plan = dcc.Textarea(
            id={'type': 'risk-mitigation', 'index': len(children) if children else 0},
        )

        contingency_plan = dcc.Textarea(
            id={'type': 'risk-contingency', 'index': len(children) if children else 0},
        )

        children.append(
            html.Div(
                children=[
                    html.Div(date_picker, className="col align-content-center"),
                    html.Div(description, className="col d-flex align-content-center flex-wrap"),
                    html.Div(probability_scale, className="col align-content-center"),
                    html.Div(impact_scale, className="col align-content-center"),
                    html.Div(mitigation_plan, className="col d-flex align-content-center flex-wrap"),
                    html.Div(contingency_plan, className="col d-flex align-content-center flex-wrap"),
                    html.Div(checkbox, className="col d-flex align-content-center flex-wrap"),
                ],
                className="row ms-1 mt-1"
            )
        )
    return children


@callback(
    Output("id-plat-risk-register", "children", allow_duplicate=True),
    Output("paragraph_id", "children", allow_duplicate=True),
    Input("id-remove-filter-risk", "n_clicks"),
    State({'type': 'risk-chk-box', 'index': ALL}, "value"),
    State("id-plat-risk-register", "children"),
    prevent_initial_call=True
)
def remove_risk_register_row(remove, check_box_value, children):
    click_event = clicked(callback_context)
    patched_list = Patch()
    message = ""
    if click_event == 'id-remove-filter-risk':
        if len(children) > 1:
            values_to_remove = []
            for i, value in enumerate(check_box_value):
                if value:
                    values_to_remove.append(i)

            for v in values_to_remove:
                del patched_list[v]

            # Checkbox is not clicked. remove last row.
            if not values_to_remove:
                del patched_list[len(children) - 1]
                message = "No row selected. Deleted last row"
            else:
                message = f"Following rows deleted: {', '.join(list(map(str, values_to_remove)))}"
        else:
            message = "Can't delete only row"

    return patched_list, message


@callback(
    output=[
        Output("paragraph_id", "children"),
        Output("id-status-rpt-filename", "data")
    ],
    inputs=[
        Input("button_id", "n_clicks"),
        Input("url", "pathname")
    ],
    state=[
        State({'type': 'status-rpt-drop-down', 'index': ALL}, "id"),
        State({'type': 'status-rpt-drop-down', 'index': ALL}, "value"),
        State({'type': 'status-rpt-branch-drop-down', 'index': ALL}, "id"),
        State({'type': 'status-rpt-branch-drop-down', 'index': ALL}, "value"),
        State({'type': 'status-rpt-header', 'index': ALL}, "id"),
        State({'type': 'status-rpt-header', 'index': ALL}, "value"),
        State({'type': 'risk-date-picker', 'index': ALL}, "date"),
        State({'type': 'risk-description-textarea', 'index': ALL}, "value"),
        State({'type': 'risk-probability', 'index': ALL}, "value"),
        State({'type': 'risk-impact', 'index': ALL}, "value"),
        State({'type': 'risk-mitigation', 'index': ALL}, "value"),
        State({'type': 'risk-contingency', 'index': ALL}, "value"),
    ],
    background=True,
    running=[
        (Output("button_id", "disabled"), True, False),
        (Output("cancel_button_id", "disabled"), False, True),
        (
                Output("paragraph_id", "style"),
                {"visibility": "hidden"},
                {"visibility": "visible"},
        ),
        (
                Output("progress_bar_graph", "style"),
                {"visibility": "visible"},
                {"visibility": "hidden"},
        ),
    ],
    cancel=[Input("cancel_button_id", "n_clicks")],
    progress=Output("progress_bar_graph", "figure"),
    progress_default=make_progress_graph(0, 5),
)
@inject
def update_progress(
        set_progress, n_clicks, pathname,
        release_status_id, release_status,
        branch_status_id, branch_status,
        rpt_header_id, rpt_header,
        risk_date: list, risk_description: list, risk_probability: list,
        risk_impact: list, risk_mitigation: list, risk_contingency: list,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        my_logger = MyLogger().get_logger()
        risk_data = {
            'Date': risk_date,
            'Description': risk_description,
            'Probability': risk_probability,
            'Impact': risk_impact,
            'Mitigation Plan': risk_mitigation,
            'Contingency Plan': risk_contingency
        }
        df_risk = pd.DataFrame(risk_data)
        ranking = df_risk['Probability'] * df_risk['Impact']
        df_risk.insert(4, "Risk Ranking", ranking)

        total_step = 15
        current_step = 0

        jazz_release = []
        cookie_release = []
        jazz_rpt_header = []
        cookie_rpt_header = []
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        my_logger.debug(f'type of session_factory = {type(session_factory)}')

        if isinstance(session_factory, containers.DynamicContainer):
            session_factory.config.override({'ro_or_rw': 'ro'})
            session_factory.config_schema.override({'schema_name': schema_name})

            # pg_session_generator = session_factory.db_connections()['ro']()()[schema_name].session()
            pg_session_generator = session_factory.db_conn_provider().session()
            my_logger.debug("Dynamically injected DB connection")
        else:
            pg_session_generator = custom_container.Database(
                custom_container.DbConnectionURI(kpk_inst, read_only=True)).session()
            my_logger.debug("Injection of DB connection didn't work")

        for i, release_dict in enumerate(release_status_id):
            if release_dict['index'].startswith('jazz'):
                jazz_release.append(release_status[i])
            else:
                cookie_release.append(release_status[i])

        for i, rpt_header_dict in enumerate(rpt_header_id):
            if rpt_header_dict['index'].startswith('jazz'):
                jazz_rpt_header.append(rpt_header[i])
            else:
                cookie_rpt_header.append(rpt_header[i])

        logger.info(f"Versions to be searched for Cookie: {cookie_release}")
        logger.info(f"Versions to be searched for Cookie: {jazz_release}")
        template_file = r'c:\vishal\report\ReportTemplate.pptx' if os.name == "nt" else "/opt/reports/ReportTemplate.pptx"

        file_part = str(uuid.uuid4())
        report_file_name = f'c:/vishal/report/{file_part}.pptx' if os.name == "nt" else f"/opt/reports/{file_part}.pptx"

        with CreateReport(report_file_name, template_file=template_file) as rpt:
            with pg_session_generator as pg_session:
                if not isinstance(session_factory, containers.DynamicContainer):
                    updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: schema_name})
                    pg_session.bind = updated_engine

                logger.info("Creating title slide")
                rpt.add_slide(0)
                rpt.add_title("Weekly Status Report - Professional Services Project")
                rpt.add_subtitle("Reporting Period: ")
                logger.info("Creating transition slide")
                rpt.add_slide(5)
                rpt.add_section("Executive Summary")
                rpt.add_text_box_with_text(0.25, 0.8, 2, 0.3, "Key Updates", 12)
                set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                logger.info("Creating Release related slides")
                logger.info(cookie_rpt_header)

                for counter, value in enumerate(cookie_rpt_header):
                    rpt.add_slide(2)
                    rpt.add_section('Cookie Updates')
                    df = db.get_release_status(cookie_release[counter], pg_session)

                    df.query('project == "Cookie Enhancement"', inplace=True)
                    StatusCategory = namedtuple('StatusCategory', ['todo', 'wip', 'done'])

                    col_mapper = {
                        'Story & Task': StatusCategory('story_todo', 'story_wip', 'storydone'),
                        'BA & Testing': StatusCategory('other_todo', 'other_wip', 'otherdone'),
                        'All subtasks': StatusCategory('subtask_todo', 'subtask_wip', 'subtaskdone'),
                        'Bugs': StatusCategory('defect_todo', 'defect_wip', 'defectsdone')
                    }
                    for k, v in col_mapper.items():
                        df[k] = df[v.todo].astype(str) + '|' + df[v.wip].astype(str) + '|' + df[v.done].astype(str)

                    df.drop(columns=[
                        'initiative', 'initiative_id', 'Team', 'clientjira',
                        'story_todo', 'story_wip', 'storydone', 'other_todo', 'other_wip', 'otherdone',
                        'subtask_todo', 'subtask_wip', 'subtaskdone', 'defect_todo', 'defect_wip', 'defectsdone',
                        'components'

                    ], inplace=True)
                    df.rename(columns={'project': 'Project', 'status': 'Epic Status'}, inplace=True)

                    batch_size = min(10, df.shape[0])

                    for i in range(0, df.shape[0], batch_size):
                        rpt.add_slide(master_slide_no=5)
                        rpt.add_title(f'Cookie {value}')
                        batch = df.iloc[i:i + batch_size]
                        rpt.add_slide_table(
                            rows=batch.shape[0] + 1, cols=batch.shape[1],
                            left=1.5, top=1, width=8, height=1.5
                        )
                        rpt.add_table_data(batch, df.columns.values.tolist())
                        rpt.custom_style_table(11)
                        rpt.add_text_box_with_text(1.5, 6.25, 6, 0.5, "Legend: To Do| In Progress| Done", 11)

                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))
                    df = db.report_bug_summary(cookie_release[counter], pg_session)
                    df_pivot = pd.pivot_table(df, values=['opt_2'], index=['Team'], columns=['priority'],
                                              aggfunc=lambda x: x, fill_value="")

                    # Drop grand total row
                    # df_pivot.drop(columns='All', axis=1, inplace=True)
                    batch_size = min(12, df_pivot.shape[0])

                    for i in range(0, df_pivot.shape[0], batch_size):
                        rpt.add_slide(master_slide_no=5)
                        rpt.add_title('Cookie ' + value.replace('Release', 'Bug'))
                        batch = df_pivot.iloc[i:i + batch_size]
                        rpt.add_slide_table(
                            rows=batch.shape[0] + 1, cols=batch.shape[1] + 1,
                            left=1.5, top=1, width=8, height=1.5
                        )

                        rpt.add_table_data_with_index(batch, ['Team'] + batch.columns.get_level_values(1).tolist())
                        rpt.custom_style_table(11)
                        rpt.add_text_box_with_text(
                            1.5, 6.25, 7, 0.5,
                            "Legend: Open or In Progress| Fixed or Rejected or Verified| On Hold or Future Release| "
                            "Closed or Cancelled",
                            10
                        )
                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                rpt.add_slide(2)
                rpt.add_section("Jazz Updates")
                set_progress(make_progress_graph(current_step := current_step + 1, total_step))

                # Add Logic for Jazz
                for counter, value in enumerate(jazz_rpt_header):
                    df = db.get_release_status(jazz_release[counter], pg_session)
                    df.query('project == "Jazz Enhancement"', inplace=True)

                    StatusCategory = namedtuple('StatusCategory', ['todo', 'wip', 'done'])

                    col_mapper = {
                        'Story & Task': StatusCategory('story_todo', 'story_wip', 'storydone'),
                        'BA & Testing': StatusCategory('other_todo', 'other_wip', 'otherdone'),
                        'All subtasks': StatusCategory('subtask_todo', 'subtask_wip', 'subtaskdone'),
                        'Bugs': StatusCategory('defect_todo', 'defect_wip', 'defectsdone')
                    }
                    for k, v in col_mapper.items():
                        df[k] = df[v.todo].astype(str) + '|' + df[v.wip].astype(str) + '|' + df[v.done].astype(str)

                    df.drop(columns=[
                        'initiative', 'initiative_id', 'Team', 'clientjira',
                        'story_todo', 'story_wip', 'storydone', 'other_todo', 'other_wip', 'otherdone',
                        'subtask_todo', 'subtask_wip', 'subtaskdone', 'defect_todo', 'defect_wip', 'defectsdone',
                        'components'

                    ], inplace=True)
                    df.rename(columns={'project': 'Project', 'status': 'Epic Status'}, inplace=True)
                    if df.shape[0] > 0:
                        batch_size = min(10, df.shape[0])
                        for i in range(0, df.shape[0], batch_size):
                            rpt.add_slide(master_slide_no=5)
                            rpt.add_title('Jazz ' + value)
                            batch = df.iloc[i:i + batch_size]
                            rpt.add_slide_table(
                                rows=batch.shape[0] + 1, cols=batch.shape[1],
                                left=1.5, top=1, width=8, height=1.5
                            )
                            rpt.add_table_data(batch, df.columns.values.tolist())
                            rpt.custom_style_table(11)

                            rpt.add_text_box_with_text(1.5, 6.25, 6, 0.5, "Legend: To Do| In Progress| Done", 11)

                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))
                    df = db.report_bug_summary(jazz_release[counter], pg_session)
                    df_pivot = pd.pivot_table(df, values=['opt_2'], index=['Team'], columns=['priority'],
                                              aggfunc=lambda x: x, fill_value="")

                    # Drop grand total row
                    # df_pivot.drop(columns='All', axis=1, inplace=True)
                    if df_pivot.shape[0] > 0:

                        batch_size = min(10, df_pivot.shape[0])

                        for i in range(0, df_pivot.shape[0], batch_size):
                            rpt.add_slide(master_slide_no=5)
                            rpt.add_title('Jazz ' + value.replace('Release', 'Bug'))
                            batch = df_pivot.iloc[i:i + batch_size]
                            rpt.add_slide_table(
                                rows=batch.shape[0] + 1, cols=batch.shape[1] + 1,
                                left=1.5, top=1, width=8, height=1.5
                            )

                            rpt.add_table_data_with_index(batch, ['Team'] + batch.columns.get_level_values(1).tolist())
                            rpt.custom_style_table(11)
                            rpt.add_text_box_with_text(
                                1.5, 6.25, 8, 0.5,
                                "Legend: Open or In Progress| Fixed or Rejected or Verified| On Hold or Future Release| "
                                "Closed or Cancelled", 11
                            )
                    set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            # End Logic for Jazz
            rpt.add_slide(2)
            rpt.add_section("Risks")
            rpt.add_slide(5)
            rpt.add_title("Risk Register")
            rpt.add_slide_table(rows=df_risk.shape[0] + 1, cols=df_risk.shape[1], left=0.5, top=1, width=12, height=5)
            rpt.add_table_data(df_risk, df_risk.columns.values.tolist())
            rpt.add_slide(2)
            rpt.add_section("Action Log")
            rpt.add_slide(5)
            rpt.add_title("Action Items")
            rpt.add_slide_table(rows=5, cols=4, left=1.5, top=1, width=8, height=1.5)
            df_action = pd.DataFrame(columns=['Action Item', 'RPOC', 'Due Date', 'Remarks'])
            rpt.add_table_data(df_action, ['Action Item', 'RPOC', 'Due Date', 'Remarks'])
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            logger.info("Creating environment slide")
            rpt.add_slide(2)
            rpt.add_section('Appendix')
            rpt.add_slide(2)
            rpt.add_section('Jazz, Cookie & IOPERF Environment Details')
            pod_data_list = get_data()
            rpt.add_slide(master_slide_no=5)
            rpt.add_title("Application and Platform version: Jazz & IOPERF")

            rpt.add_slide_table(
                rows=pod_data_list[0].shape[0] + 1, cols=pod_data_list[0].shape[1],
                left=1.5, top=1, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[0], ['Jazz', 'POD3'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            rpt.add_slide_table(
                rows=pod_data_list[1].shape[0] + 1, cols=pod_data_list[1].shape[1],
                left=1.5, top=5, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[1], ['IO-PERF', 'DC'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

            rpt.add_slide(master_slide_no=5)
            rpt.add_title("Application and Platform version: Cookie")
            rpt.add_slide_table(
                rows=pod_data_list[2].shape[0] + 1, cols=pod_data_list[2].shape[1],
                left=1.5, top=1, width=8, height=1.5
            )
            rpt.add_table_data(pod_data_list[2], ['Cookie', 'POD1', 'POD2', 'POD4'])
            rpt.custom_style_table(11)
            set_progress(make_progress_graph(current_step := current_step + 1, total_step))

        # populates list of list
        # sample:
        # [['22.11', '22.11.1', '22.11.2']]
        # [['22.10.9.1', '22.10.9', '22.10.2', '22.10.1', '22.10', '22.10.8', '22.10.5', '22.10.7', '22.10.4.3', '22.10.4.2', '22.10.4', '22.10.3', '22.10.6'], ['23.1.4.1', '23.1.2', '23.1.3', '23.1.4', '23.1', '23.1.1']]

        my_logger.info(f"returning!!! Total steps: {current_step}")
        return [f"Clicked {n_clicks} times", report_file_name]
    else:
        raise PreventUpdate


@callback(
    Output("id-status-rpt-download", "data"),
    Input("id-download-report", "n_clicks"),
    State("id-status-rpt-filename", "data")
)
def download_status_report(n_clicks, filename):
    if n_clicks > 0:
        return dcc.send_file(filename)


# @callback(
#     Output('log', 'children'),
#     [Input('log-update', 'n_intervals')],
#
# )
# def update_logs(_):
#     return [html.Div(f'Log message = {log}') for log in dash_logger.logs]


# clientside_callback(
#     """
#     function(id) {
#         feather.replace();
#         return window.dash_clientside.no_update
#     }
#     """,
#     Output("content", "className"),
#     Input("content", "id"),
# )



clientside_callback(
    ClientsideFunction(
        namespace='clientside',
        function_name='swiper_credit_card'
    ),
    Output("id-credit-card-swiper", "className"),
    Input("id-credit-card-swiper", "className"),
)

# Removed usage of socket.io. Keeping it for future reference
# clientside_callback(
#     ClientsideFunction(
#         namespace='clientside',
#         function_name='socket_connection'
#     ),
#     Output("socket-conn", "value"),
#     Input("url", "id")
# )



# JS callback to send the question to the flask API, at the end it enables the submit button
clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="streaming_GPT"),
    Output("submit-prompt", "disabled"),
    Input("submit-prompt", "n_clicks"),
    State("text-prompt", "value"),
    prevent_initial_call=True,
)

# Clears the input field after the user clicks submit, and disables submit button
clientside_callback(
    """
    function updateQuestionFields(n_clicks) {
        return [ "", true ];
    }
    """,
    Output("text-prompt", "value"),
    Output("submit-prompt", "disabled", allow_duplicate=True),
    Input("submit-prompt", "n_clicks"),
    prevent_initial_call=True,
)

redis_conn = Redis()


@server.route(f"{os.getenv('DASH_BASE_PATH', '/')}api/active_sessions")
@inject
def active_sessions(
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
) -> flask.Response:
    """
    Retrieve the count of active sessions.

    Returns:
        flask.Response: A JSON response containing the count of active sessions.
    """

    session_factory.config.override({'ro_or_rw': 'ro'})
    session_factory.config_schema.override({'schema_name': 'pg_catalog'})

    with session_factory.db_conn_provider().session() as pg_session:
        query = select(
            PGStatActivity.columns.usename, PGStatActivity.columns.state,
            func.count().label("Count")
        ).select_from(PGStatActivity).filter(PGStatActivity.columns.state != None) \
            .group_by(PGStatActivity.columns.usename, PGStatActivity.columns.state)
        res = pg_session.execute(query).all()
        data_dict = [{key: value for key, value in zip(('username', 'state', 'count'), row)} for row in res]

        status_string = pg_session.get_bind().pool.status()
        print(status_string)

        # status_string value
        # "Pool size: 5  Connections in pool: 0 Current Overflow: -4 Current Checked out connections: 1"

        pool_size = int(re.search(r"Pool size: (\d+)", status_string).group(1))
        connections_in_pool = int(re.search(r"Connections in pool: (\d+)", status_string).group(1))
        current_overflow = int(re.search(r"Current Overflow: (-?\d+)", status_string).group(1))
        current_checked_out_connections = int(
            re.search(r"Current Checked out connections: (\d+)", status_string).group(1))

        # Create a dictionary with the extracted information
        status_dict = {
            'Pool size': pool_size,
            'Connections in pool': connections_in_pool,
            'Current Overflow': current_overflow,
            'Current Checked out connections': current_checked_out_connections
        }

    session_ids = redis_conn.keys('*')
    decoded_ids = [session_id.decode('utf-8') for session_id in session_ids if session_id.startswith(b'session:')]
    timeout = server.config.get('SESSION_COOKIE_MAX_AGE')
    session_cookie_name = server.config.get('SESSION_COOKIE_NAME')
    return jsonify(
        {
            'SESSIONS': len(decoded_ids),
            'SESSION_COOKIE_MAX_AGE': timeout,
            'SESSION_COOKIE_NAME': session_cookie_name,
            'DB_CONNECTION': data_dict,
            'DB_POOL': status_dict,
        }
    )


def is_excel_doc(file: io.IOBase):
    excel_signature = [
        ('xlsx', b'\x50\x4B\x05\x06', 2, -22, 4),  # Validated the signature
        ('xls', b'\xfd\xff\xff\xff\x02\x00\x00\x00', 0, 512, 8),  # Saved from Excel
        ('xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 1536, 8),  # Saved from LibreOffice Calc
        ('xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 2048, 8),  # Saved from Excel then saved from Calc
        ('xls', b'\xef\xbb\xbf', 0, 0, 3)  # CSV file saved as xls
    ]

    for sigType, sig, whence, offset, size in excel_signature:

        file.seek(offset, whence)
        value = file.read(size)
        if value == sig:
            return True
    return False


def is_valid_extension(expected_extension: list, actual_extension) -> bool:
    if actual_extension in expected_extension:
        return True
    return False


@callback(
    Output("id-plat-teams-msg", "children", allow_duplicate=True),
    Output("id-plat-teams-msg", "color", allow_duplicate=True),
    Output("id-plat-teams-msg", "duration", allow_duplicate=True),
    Input("id-file-upload-plat-teams", "contents"),
    Input("id-file-upload-plat-teams", "filename"),
    prevent_initial_call=True
)
def validate_file(contents, file_name):
    excelSigs = [
        ('.xlsx', b'PK\x03\x04', 2, -22, 4),
        ('.xls', b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1', 0, 512, 8),  # Saved from Excel
        ('xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 1536, 8),  # Saved from LibreOffice Calc
        ('xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 2048, 8),  # Saved from Excel then saved from Calc
    ]

    filename = secure_filename(file_name)
    if filename != "":
        file_extension = os.path.splitext(filename)[1]

        if not is_valid_extension(['.xlsx', '.xls'], file_extension):
            return html.Div("Invalid file type used"), "warning", 10000

        content_type, content_string = contents.split(',')
        decoded = base64.b64decode(content_string)

        # validate if this is a valid xlsx file
        if not is_excel_doc(io.BytesIO(decoded)):
            return html.Label(f"file is not valid {file_extension}"), "danger", 5000

        df = pd.read_excel(io.BytesIO(decoded))
        return html.Label(f"# of Records in file = {df.shape[0]}"), "info", 5000
    else:
        raise PreventUpdate


@callback(
    Output("id-plat-teams-msg", "children", allow_duplicate=True),
    Output("id-plat-teams-msg", "color", allow_duplicate=True),
    Output("id-plat-teams-msg", "duration", allow_duplicate=True),
    Input("id-run-plat-team", "n_clicks"),
    prevent_initial_call=True
)
def processing_message(_):
    return html.Label(f"Processing the file..."), "info", 5000


@callback(
    Output("id-plat-teams-msg", "children"),
    Output("id-plat-teams-msg", "color"),
    Input("id-run-plat-team", "n_clicks"),
    Input("url", "pathname"),
    State("id-file-upload-plat-teams", "contents"),
    State("id-file-upload-plat-teams", "filename")
)
@inject
def bulk_upload_team(
        n_clicks, pathname, contents, file_name,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        filename = secure_filename(file_name)
        if filename != "":
            file_extension = os.path.splitext(filename)[1]
            if not is_valid_extension(['.xlsx', '.xls'], file_extension):
                return html.Div("Invalid file type used"), "warning"

            content_type, content_string = contents.split(',')
            decoded = base64.b64decode(content_string)

            # validate if this is a valid xlsx file
            if not is_excel_doc(io.BytesIO(decoded)):
                return html.Label(f"file is not valid {file_extension}"), "danger"

            df = pd.read_excel(io.BytesIO(decoded))
            # Validate template used
            if df.shape[1] != 4:
                return html.Div(f"Please check template used. Columns found: {df.shape[1]}"), "danger"
            if df.columns.tolist() != ['team_name', 'emailAddress', 'startDate', 'endDate']:
                return html.Div(f"Unexpected columns names found: {df.columns.values}"), "danger"

            df['team_name'] = df['team_name'].str.upper().str.strip()
            df['emailAddress'] = df['emailAddress'].str.lower().str.strip()
            df['startDate'] = df['startDate'].fillna('2000-1-1')
            df['endDate'] = df['endDate'].fillna('2050-12-31')
            try:
                df['valid_start_date'] = pd.to_datetime(df['startDate'], errors='raise').notnull()
            except ValueError:
                return html.Div("Invalid start date"), "warning"
            try:
                df['valid_end_date'] = pd.to_datetime(df['endDate'], errors='raise').notnull()
            except ValueError:
                return html.Div("Invalid end date"), "warning"

            if not df['valid_start_date'].all():
                return Exception("Invalid start date"), "warning"

            if not df['valid_end_date'].all():
                return Exception("invalid end date"), "warning"

            df.drop(columns=['valid_start_date', 'valid_end_date'], inplace=True)
            if os.name == 'nt':
                df.to_csv(r"e:\vishal\teams.csv")
            else:
                df.to_csv("/tmp/teams.csv")

            session_factory.config.override({'ro_or_rw': 'rw'})
            session_factory.config_schema.override({'schema_name': schema_name})
            with session_factory.db_conn_provider().session() as pg_session:
                users_row = db.get_users(pg_session)
                user_list = [[row.emailAddress, row.accountId] for row in users_row]
                user_df = pd.DataFrame(user_list, columns=["emailAddress", "accountId"])
                df = df.merge(user_df, on="emailAddress", how="left")
                try:
                    db.upsert(pg_session, db.Teams, df, primary_key="emailAddress", on_conflict_update=False)
                    pg_session.commit()
                except IntegrityError:
                    return html.Div("DB Exception: Check for duplicate records"), "danger"

            return html.Div(
                children=[
                    html.P(filename),
                    html.Br(),
                    html.P(f"Successfully processed {df.shape[0]} records")
                ]
            ), "success"
    else:
        raise PreventUpdate


@callback(
    Output("id-request-tracker", "children", allow_duplicate=True),
    Output("id-request-tracker", "color", allow_duplicate=True),
    Output("id-file-upload-request-tracker", "duration", allow_duplicate=True),
    Input("id-file-upload-request-tracker", "contents"),
    Input("id-file-upload-request-tracker", "filename"),
    prevent_initial_call=True
)
def validate_file(contents, file_name):
    # Input file is a unicode text file with .xls extension
    excelSigs = [
        ('.xlsx', b'PK\x03\x04\x14', 2, -22, 4),
        ('.xls', b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1', 0, 512, 8),  # Saved from Excel
        ('.xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 1536, 8),  # Saved from LibreOffice Calc
        ('.xls', b'\x09\x08\x10\x00\x00\x06\x05\x00', 0, 2048, 8),  # Saved from Excel then saved from Calc
    ]

    filename = secure_filename(file_name)
    if filename != "":
        file_extension = os.path.splitext(filename)[1]

        if not is_valid_extension(['.xlsx', '.xls'], file_extension):
            return html.Div("Invalid file type used"), "warning", 10000

        content_type, content_string = contents.split(',')
        decoded = base64.b64decode(content_string)
        f = magic.Magic(mime=True)
        file_type = f.from_buffer(decoded)
        # sample = io.BytesIO(decoded)
        # sample.seek(0)

        # validate if this is a valid xlsx file
        if not is_excel_doc(io.BytesIO(decoded)):
            return html.Label(f"file is not valid {file_extension}"), "danger", 5000
        start_time = time.perf_counter_ns()
        if file_type == 'text/plain':
            df = pd.read_csv(io.BytesIO(decoded), quotechar='"')
        else:
            df = pd.read_excel(io.BytesIO(decoded))

        return html.Label(
            children=[
                html.Label(f"# of Records in file = {df.shape[0]}"),
                html.Hr(),
                html.Label(f"# of columns = {df.shape[1]}"),
                html.Label(f"Time Taken: {(time.perf_counter_ns() - start_time) * pow(10, -6)} ms")
            ],

        ), "info", 5000
    else:
        raise PreventUpdate


@callback(
    Output("id-request-tracker", "children", allow_duplicate=True),
    Output("id-request-tracker", "color", allow_duplicate=True),
    Output("id-request-tracker", "duration", allow_duplicate=True),
    Input("id-run-request-tracker", "n_clicks"),
    prevent_initial_call=True
)
def processing_message_request_tracker(_):
    return html.Label(f"Processing the file..."), "info", 5000


@callback(
    Output("id-request-tracker", "children"),
    Output("id-request-tracker", "color"),
    Input("id-run-request-tracker", "n_clicks"),
    Input("url", "pathname"),
    State("id-file-upload-request-tracker", "contents"),
    State("id-file-upload-request-tracker", "filename")
)
@inject
def bulk_upload_request_tracker(
        n_clicks, pathname, contents, file_name,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

        filename = secure_filename(file_name)
        if filename != "":

            file_extension = os.path.splitext(filename)[1]
            if not is_valid_extension(['.xlsx', '.xls'], file_extension):
                return html.Div("Invalid file type used"), "warning"

            content_type, content_string = contents.split(',')
            decoded = base64.b64decode(content_string)

            # validate if this is a valid xlsx file
            if not is_excel_doc(io.BytesIO(decoded)):
                return html.Label(f"file is not valid {file_extension}"), "danger", 5000

            f = magic.Magic(mime=True)
            file_type = f.from_buffer(decoded)

            start_time = time.perf_counter_ns()
            if file_type == 'text/plain':
                df = pd.read_csv(io.BytesIO(decoded), quotechar='"')
            else:
                df = pd.read_excel(io.BytesIO(decoded), sheet_name=0)

            print(f'df shape = {df.shape}, Time Take: {(time.perf_counter_ns() - start_time) * pow(10, -6)} ms')
            # Validate template used
            if df.shape[1] != 22:
                return html.Div(f"Please check template used. Columns found: {df.shape[1]}"), "danger"

            df.rename(columns={
                'reported by': 'reported_by',
                'reported on': 'reported_on',
                'last updated by': 'last_updated_by',
                'last updated on': 'last_updated_on',
                'assigned to': 'assigned_to',
                'Recurring Task': 'recurring_task',
                'Add this RT to IRR?': 'add_rt_to_irr',
                'IRR Id': 'irr_id',
                'HelpFrom': 'help_from',
                'HelpType': 'help_type',
                'ReferenceNo': 'reference_no',
                'Functionality': 'functionality',
                'Estimated-Time-Hours': 'estimated_time_hours',
                'Complexity': 'complexity', 'Task': 'task'
            }, inplace=True)

            session_factory.config.override({'ro_or_rw': 'rw'})
            session_factory.config_schema.override({'schema_name': schema_name})
            df['reported_on'] = pd.to_datetime(df['reported_on'], format='mixed')
            df['last_updated_on'] = pd.to_datetime(df['last_updated_on'], format='mixed')

            # Replace NaT values with the default value '1970-01-01'
            default_value = pd.to_datetime('1970-01-01')
            df['last_updated_on'] = df['last_updated_on'].fillna(default_value)
            df.fillna("", inplace=True)
            df['desc'] = df['desc'].astype(str)

            with session_factory.db_conn_provider().session() as pg_session:
                try:
                    rows = db.get_nlp_taining_data(pg_session)
                    texts, labels = zip(*rows)

                    # Create a pipeline for feature extraction and classification
                    # model = make_pipeline(TfidfVectorizer(), MultinomialNB())
                    model = make_pipeline(TfidfVectorizer(), SVC(kernel='linear', probability=True))

                    # Train the model
                    model.fit(texts, labels)
                    # df['label'] = df['desc'].apply(lambda x: model.predict([x])[0])

                    # Get the probabilities for each class (category)
                    probabilities = model.predict_proba(df["desc"])
                    df_prob = pd.DataFrame(probabilities, columns=model.classes_)
                    if os.name == "nt":
                        df_prob.to_csv(r"C:\Users\<USER>\Downloads\probability.csv")
                    else:
                        df_prob.to_csv("/tmp/probability.csv")

                    predicted_labels = [model.predict([x])[0] for x in df["desc"]]
                    predicted_probabilities = [probabilities[i, model.classes_.tolist().index(label)]
                                               for i, label in enumerate(predicted_labels)]

                    df['label'] = predicted_labels
                    df['confidence_percent'] = predicted_probabilities

                    # Get the label (class) with the highest probability for each data point
                    winning_labels = [model.classes_[i] for i in probabilities.argmax(axis=1)]
                    df['label_max_percent'] = winning_labels

                    winning_probabilities = probabilities.max(axis=1)
                    df['max_confidence_percent'] = winning_probabilities

                    df = df.round(decimals=4)

                    df_write = df[
                        ['id', 'desc', 'label', 'confidence_percent', 'label_max_percent', 'max_confidence_percent']
                    ].copy(deep=True)

                    # Create a new column in the DataFrame for each class and store the probabilities
                    for i, category in enumerate(model.classes_):
                        df_write[category] = probabilities[:, i]

                    if os.name == "nt":
                        with pd.ExcelWriter(r'C:\Users\<USER>\Downloads\classification.xlsx') as writer:
                            df_write.to_excel(writer, sheet_name="Sheet1")

                    db.upsert(pg_session, RequestTracker, df, primary_key="id", on_conflict_update=True)
                    print("Upsert done!!!")
                    pg_session.commit()
                except IntegrityError:
                    print("Exception Occurred :-(")
                    return html.Div("DB Exception: Check for duplicate records"), "danger"
                except Exception as e:
                    print(f"Exception Occured {e.args[0]}")
                    return html.Div("DB Exception Occurred. Contact Admin!!"), "danger"

            return html.Div(
                children=[
                    html.Label(filename),
                    html.Br(),
                    html.P(f"Successfully processed {df.shape[0]} records")
                ]
            ), "success"
    else:
        raise PreventUpdate


@callback(
    Output("id-download-file-rt", "data"),
    Input("id-execute-pattern-rt", "n_clicks"),
    Input("url", "pathname"),
    State("id-text-pattern-rt", "value"),
    State("id-percent-rt", "value"),
    prevent_initial_call=True,
)
@inject
def download_rt_pattern(
        n_clicks, pathname: str, regex_pattern: str, sample_fraction: float,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': schema_name})
        with session_factory.db_conn_provider().session() as pg_session:
            try:
                rows = db.get_request_tracker_data(pg_session, desc_only=True)
            except ProgrammingError as e:
                print('SQL Exception caught')
                raise e
            df = pd.DataFrame(rows)
            filtered_df = df[df['desc'].str.contains(regex_pattern, case=False, regex=True)]
            df_sample = filtered_df.sample(frac=sample_fraction / 100.0, random_state=42)
            return dcc.send_data_frame(df_sample.to_excel, "sample_data.xlsx", sheet_name="Sheet1", index=False)
    else:
        raise PreventUpdate


@callback(
    Output("id-request-tracker-data", "rowData"),
    Input("id-get-rt-data", "n_clicks"),
)
@inject
def get_rt_data(
        n_clicks,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    if n_clicks > 0:
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': 'public'})
        with session_factory.db_conn_provider().session() as pg_session:
            try:
                rows = db.get_request_tracker_data(pg_session, desc_only=False)
            except ProgrammingError as e:
                print('SQL Exception caught')
                raise e
            df = pd.DataFrame(rows)
        return df.to_dict("records")


clientside_callback(
    """
    function(publicKey, password) {            
            if ( publicKey == "") {
                return dash.PreventUpdate; 
            } 

            async function handlePasswordEncryption(publicKey, password) {                          
                if (!password) return 'Operation Failed';

                try {                    
                    const encryptedPassword = await encryptPassword(publicKey, password);                    
                    return encryptedPassword;                    
                } catch (err) {
                    console.error('Encryption failed:', err);
                    return '';
                }
            }
            return handlePasswordEncryption(publicKey, password);        
    }
    """,
    Output(component_id='encrypted-token', component_property='data'),
    Input(component_id="public-key", component_property="data"),
    State('id-login-passwd-main', component_property='value'),
    prevent_initial_call=True
)

@callback(
    Output("public-key", component_property="data"),
    Input(component_id="id-button-login-main", component_property="n_clicks"),
    prevent_initial_call=True
)
@inject
def create_keys(
        n_clicks: int,
        pwd_mgr: custom_container.AppContainer = Provide[custom_container.AppContainer.my_keepass]

):
    if n_clicks > 0:
        my_logger = MyLogger().get_logger()
        user_pin = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'PIN')
        token_label = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'Token Label')
        keypair_label = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'KEY_LABEL')
        lib = pkcs11.lib(os.environ['PKCS11_MODULE'])
        token = lib.get_token(token_label=token_label)

        with token.open(user_pin=user_pin, rw=True) as hsm_session:
            keys = list(hsm_session.get_objects({
                Attribute.LABEL: keypair_label,
            }))

            if len(keys) == 0:
                public_key, private_key = hsm_session.generate_keypair(
                    pkcs11.KeyType.RSA, 2048, label=keypair_label,
                    # mechanism=Mechanism.RSA_PKCS_KEY_PAIR_GEN,
                    public_exponent=65537,
                    store=True,
                    public_template={
                        Attribute.ENCRYPT: True,
                        Attribute.VERIFY: True,
                        Attribute.TOKEN: True,
                        Attribute.MODULUS_BITS: 2048,
                    },
                    private_template={
                        Attribute.SENSITIVE: True,
                        Attribute.EXTRACTABLE: True,
                        Attribute.PRIVATE: True,
                        Attribute.SIGN: True,
                        Attribute.DECRYPT: True,
                    }
                )
            else:
                keys = list(hsm_session.get_objects({
                    Attribute.LABEL: keypair_label,
                    # Attribute.PRIVATE: True
                }))
                my_logger.debug("keys")
                my_logger.debug(f"public key = {keys[0]}")
                my_logger.debug(keys[1])
                public_key = keys[0]

            # To import a PKCS #1 DER-encoded RSA key, use following methods
            # decode_rsa_public_key & decode_rsa_private_key
            # Export the public key in DER format
            print(f"public key attributes")
            print(public_key[Attribute.MODULUS])
            print(public_key[Attribute.PUBLIC_EXPONENT])

            # Extract the public key components
            public_modulus = public_key[Attribute.MODULUS]
            public_exponent = public_key[Attribute.PUBLIC_EXPONENT]

            # Convert to base64 for easier transport
            public_key_data = {
                'modulus': base64.b64encode(public_modulus).decode('utf-8'),
                'exponent': base64.b64encode(public_exponent).decode('utf-8')
            }
            print(f"exponent dict")
            print(public_key_data)


            public_key_der = encode_rsa_public_key(public_key)

            # Convert DER to PEM
            public_key_pem = serialization.load_der_public_key(public_key_der).public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            # Convert bytes to string for JavaScript
            public_key_pem_str = public_key_pem.decode('utf-8')

            public_key_pem_str = re.sub(r'-----BEGIN PUBLIC KEY-----|-----END PUBLIC KEY-----', '', public_key_pem_str)
            public_key_pem_str = re.sub(r'\s+', '', public_key_pem_str)
            print("public key")
            print(public_key_pem_str)
            return public_key_pem_str
    else:
        raise PreventUpdate


# @callback(
#     [Output('logout-message', 'children'),
#      Output('loading-message', 'children'),
#      Output('login-message', 'children'),
#      Output('loading-icon', 'className')],
#     Input('id-logout-menu', "n_clicks"),
#     State('session', 'data')
# )
# def update_content(
#         n_clicks: int,
#         session_data
# ):
#
#     Used in LogoutPageStrategy from page_layout
#     Args:
#         n_clicks:
#         session_data:
#
#     Returns:
#
#     """
#     if n_clicks > 0:
#         print(session_data['logout_reason'])
#         if 'user_initiated' in session_data['logout_reason']:
#             logout_message = "You have logged off!"
#             login_message = "Please, click below to log back in."
#         elif 'inactivity' in session_data['logout_reason']:
#             logout_message = "Logged off due to inactivity"
#             login_message = "Please, click below to log back in."
#         else:
#             logout_message = "You have not logged in"
#             login_message = "Please, click below to login."
#
#         if session_data.get('loadingShowed', False):
#             loading_message = "Login in {} seconds.".format(session_data.get('seconds', 0))
#             loading_icon_class = "fa fa-3x fa-spinner fa-pulse fa-fw"
#         else:
#             loading_message = ""
#             loading_icon_class = "fa fa-3x fa-sign-in"
#
#         return logout_message, loading_message, login_message, loading_icon_class
#     else:
#         raise PreventUpdate
